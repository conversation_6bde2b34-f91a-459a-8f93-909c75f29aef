#!/usr/bin/env python3
"""
Enhanced Model Integration Utility
Provides seamless integration of trained ML models from backtesting-ML workflow
into trading agents for real-time decision making.

Features:
- Model loading and caching
- Real-time prediction serving
- Feature engineering for live data
- Model ensemble predictions
- Performance monitoring
- Fallback mechanisms
"""

import os
import sys
import logging
import joblib
import json
import numpy as np
import pandas as pd
import polars as pl
from pathlib import Path
from typing import Dict, List, Optional, Union, Any, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta
from collections import defaultdict, deque
import warnings
warnings.filterwarnings('ignore')

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

logger = logging.getLogger(__name__)

@dataclass
class ModelPrediction:
    """Model prediction result"""
    task_name: str
    model_name: str
    prediction: Union[float, int, List[float]]
    confidence: float
    feature_importance: Dict[str, float]
    timestamp: datetime
    processing_time_ms: float

@dataclass
class EnsemblePrediction:
    """Ensemble prediction result"""
    task_name: str
    prediction: Union[float, int, List[float]]
    confidence: float
    individual_predictions: List[ModelPrediction]
    consensus_score: float
    timestamp: datetime

class EnhancedModelIntegration:
    """
    Enhanced Model Integration for Trading Agents
    
    Provides seamless integration of trained ML models into trading agents
    for real-time strategy evaluation, risk assessment, and decision making.
    """
    
    def __init__(self, models_base_path: str = "data/models/enhanced"):
        """Initialize Enhanced Model Integration"""
        self.models_base_path = Path(models_base_path)
        self.loaded_models = {}
        self.scalers = {}
        self.label_encoders = {}
        self.feature_importance = {}
        self.model_metadata = {}
        
        # Performance tracking
        self.prediction_cache = {}
        self.performance_metrics = {
            'predictions_made': 0,
            'cache_hits': 0,
            'avg_prediction_time_ms': 0.0,
            'model_load_time_ms': 0.0
        }
        
        # Available tasks from training
        self.available_tasks = [
            'sharpe_ratio_prediction',
            'roi_prediction', 
            'profit_factor_prediction',
            'drawdown_prediction',
            'profitability_classification'
        ]
        
        logger.info(f"Enhanced Model Integration initialized with base path: {self.models_base_path}")
    
    async def initialize(self):
        """Initialize and load all available models"""
        try:
            logger.info("🧠 Initializing Enhanced Model Integration...")
            
            # Load latest models for each task
            await self._load_latest_models()
            
            # Load preprocessing components
            await self._load_preprocessing_components()
            
            # Load model metadata
            await self._load_model_metadata()
            
            # Validate model integrity
            await self._validate_models()
            
            logger.info(f"✅ Enhanced Model Integration initialized with {len(self.loaded_models)} models")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize Enhanced Model Integration: {e}")
            return False
    
    async def _load_latest_models(self):
        """Load the latest trained models for each task"""
        for task in self.available_tasks:
            task_dir = self.models_base_path / task
            if not task_dir.exists():
                logger.warning(f"⚠️ Task directory not found: {task}")
                continue
            
            # Find latest model file
            model_files = list(task_dir.glob("lightgbm_*.joblib"))
            if not model_files:
                logger.warning(f"⚠️ No model files found for task: {task}")
                continue
            
            # Get the latest model file
            latest_model_file = max(model_files, key=lambda x: x.stat().st_mtime)
            
            try:
                # Load the model
                model = joblib.load(latest_model_file)
                self.loaded_models[task] = {
                    'model': model,
                    'file_path': latest_model_file,
                    'loaded_at': datetime.now(),
                    'model_type': 'lightgbm'
                }
                logger.info(f"✅ Loaded model for {task}: {latest_model_file.name}")
                
            except Exception as e:
                logger.error(f"❌ Failed to load model for {task}: {e}")
    
    async def _load_preprocessing_components(self):
        """Load scalers and label encoders"""
        # Find latest preprocessing files
        scaler_files = list(self.models_base_path.glob("scalers_*.joblib"))
        encoder_files = list(self.models_base_path.glob("label_encoders_*.joblib"))
        
        if scaler_files:
            latest_scaler_file = max(scaler_files, key=lambda x: x.stat().st_mtime)
            try:
                self.scalers = joblib.load(latest_scaler_file)
                logger.info(f"✅ Loaded scalers: {latest_scaler_file.name}")
            except Exception as e:
                logger.error(f"❌ Failed to load scalers: {e}")
        
        if encoder_files:
            latest_encoder_file = max(encoder_files, key=lambda x: x.stat().st_mtime)
            try:
                self.label_encoders = joblib.load(latest_encoder_file)
                logger.info(f"✅ Loaded label encoders: {latest_encoder_file.name}")
            except Exception as e:
                logger.error(f"❌ Failed to load label encoders: {e}")
    
    async def _load_model_metadata(self):
        """Load model metadata and feature importance"""
        # Find latest training results and feature importance files
        result_files = list(self.models_base_path.glob("training_results_*.json"))
        importance_files = list(self.models_base_path.glob("feature_importance_*.json"))
        
        if result_files:
            latest_result_file = max(result_files, key=lambda x: x.stat().st_mtime)
            try:
                with open(latest_result_file, 'r') as f:
                    self.model_metadata = json.load(f)
                logger.info(f"✅ Loaded model metadata: {latest_result_file.name}")
            except Exception as e:
                logger.error(f"❌ Failed to load model metadata: {e}")
        
        if importance_files:
            latest_importance_file = max(importance_files, key=lambda x: x.stat().st_mtime)
            try:
                with open(latest_importance_file, 'r') as f:
                    self.feature_importance = json.load(f)
                logger.info(f"✅ Loaded feature importance: {latest_importance_file.name}")
            except Exception as e:
                logger.error(f"❌ Failed to load feature importance: {e}")
    
    async def _validate_models(self):
        """Validate loaded models"""
        for task, model_info in self.loaded_models.items():
            try:
                model = model_info['model']
                # Basic validation - check if model has predict method
                if not hasattr(model, 'predict'):
                    logger.error(f"❌ Model for {task} doesn't have predict method")
                    continue
                
                logger.info(f"✅ Model validation passed for {task}")
                
            except Exception as e:
                logger.error(f"❌ Model validation failed for {task}: {e}")
    
    def prepare_features_for_prediction(self, strategy_data: Dict[str, Any]) -> np.ndarray:
        """
        Prepare features for model prediction from strategy data
        
        Args:
            strategy_data: Dictionary containing strategy metrics and context
            
        Returns:
            Prepared feature array for model prediction
        """
        try:
            # Expected features based on training (from feature importance)
            expected_features = [
                'avg_sharpe_ratio', 'min_sharpe_ratio', 'max_sharpe_ratio', 'std_sharpe_ratio',
                'avg_roi', 'min_roi', 'max_roi', 'std_roi',
                'avg_profit_factor', 'min_profit_factor', 'max_profit_factor', 'std_profit_factor',
                'avg_max_drawdown', 'min_max_drawdown', 'max_max_drawdown', 'std_max_drawdown',
                'avg_expectancy', 'min_expectancy', 'max_expectancy', 'std_expectancy',
                'avg_accuracy', 'min_accuracy', 'max_accuracy', 'std_accuracy',
                'avg_total_trades', 'std_total_trades',
                'avg_winning_trades', 'std_winning_trades',
                'avg_total_pnl', 'min_total_pnl', 'max_total_pnl', 'std_total_pnl',
                'consistency_score', 'sharpe_consistency', 'roi_drawdown_ratio',
                'trades_per_period', 'walk_forward_steps'
            ]
            
            # Extract features from strategy data
            features = []
            for feature_name in expected_features:
                if feature_name in strategy_data:
                    features.append(strategy_data[feature_name])
                else:
                    # Use default values for missing features
                    default_values = {
                        'avg_sharpe_ratio': 0.0, 'min_sharpe_ratio': -2.0, 'max_sharpe_ratio': 2.0, 'std_sharpe_ratio': 1.0,
                        'avg_roi': 0.0, 'min_roi': -20.0, 'max_roi': 20.0, 'std_roi': 10.0,
                        'avg_profit_factor': 1.0, 'min_profit_factor': 0.5, 'max_profit_factor': 2.0, 'std_profit_factor': 0.5,
                        'avg_max_drawdown': -10.0, 'min_max_drawdown': -20.0, 'max_max_drawdown': -5.0, 'std_max_drawdown': 5.0,
                        'avg_expectancy': 0.0, 'min_expectancy': -100.0, 'max_expectancy': 100.0, 'std_expectancy': 50.0,
                        'avg_accuracy': 50.0, 'min_accuracy': 30.0, 'max_accuracy': 70.0, 'std_accuracy': 10.0,
                        'avg_total_trades': 50, 'std_total_trades': 20,
                        'avg_winning_trades': 25, 'std_winning_trades': 10,
                        'avg_total_pnl': 0.0, 'min_total_pnl': -1000.0, 'max_total_pnl': 1000.0, 'std_total_pnl': 500.0,
                        'consistency_score': 0.5, 'sharpe_consistency': 0.5, 'roi_drawdown_ratio': 1.0,
                        'trades_per_period': 10.0, 'walk_forward_steps': 5
                    }
                    features.append(default_values.get(feature_name, 0.0))
            
            # Convert to numpy array and reshape for single prediction
            feature_array = np.array(features).reshape(1, -1)
            
            # Apply scaling if available
            if self.scalers:
                try:
                    feature_array = self.scalers.transform(feature_array)
                except Exception as e:
                    logger.warning(f"⚠️ Failed to apply scaling: {e}")
            
            return feature_array
            
        except Exception as e:
            logger.error(f"❌ Failed to prepare features: {e}")
            return np.array([]).reshape(1, -1)
    
    async def predict(self, task_name: str, strategy_data: Dict[str, Any], 
                     use_cache: bool = True) -> Optional[ModelPrediction]:
        """
        Make prediction for a specific task
        
        Args:
            task_name: Name of the prediction task
            strategy_data: Strategy data for prediction
            use_cache: Whether to use prediction cache
            
        Returns:
            ModelPrediction object or None if prediction fails
        """
        start_time = datetime.now()
        
        try:
            # Check cache first
            cache_key = f"{task_name}_{hash(str(sorted(strategy_data.items())))}"
            if use_cache and cache_key in self.prediction_cache:
                self.performance_metrics['cache_hits'] += 1
                return self.prediction_cache[cache_key]
            
            # Check if model is available
            if task_name not in self.loaded_models:
                logger.error(f"❌ Model not available for task: {task_name}")
                return None
            
            # Prepare features
            features = self.prepare_features_for_prediction(strategy_data)
            if features.size == 0:
                logger.error(f"❌ Failed to prepare features for {task_name}")
                return None
            
            # Make prediction
            model_info = self.loaded_models[task_name]
            model = model_info['model']
            
            prediction = model.predict(features)[0]
            
            # Calculate confidence (using prediction probability for classifiers)
            confidence = 0.8  # Default confidence
            if hasattr(model, 'predict_proba'):
                try:
                    proba = model.predict_proba(features)[0]
                    confidence = max(proba)
                except:
                    pass
            
            # Get feature importance for this prediction
            feature_importance = {}
            if task_name in self.feature_importance:
                task_importance = self.feature_importance[task_name]
                if 'lightgbm' in task_importance:
                    importance_list = task_importance['lightgbm'][:10]  # Top 10 features
                    for item in importance_list:
                        feature_importance[item['feature']] = item['importance']
            
            # Calculate processing time
            processing_time = (datetime.now() - start_time).total_seconds() * 1000
            
            # Create prediction result
            result = ModelPrediction(
                task_name=task_name,
                model_name=model_info['model_type'],
                prediction=prediction,
                confidence=confidence,
                feature_importance=feature_importance,
                timestamp=datetime.now(),
                processing_time_ms=processing_time
            )
            
            # Cache the result
            if use_cache:
                self.prediction_cache[cache_key] = result
            
            # Update performance metrics
            self.performance_metrics['predictions_made'] += 1
            self.performance_metrics['avg_prediction_time_ms'] = (
                (self.performance_metrics['avg_prediction_time_ms'] * 
                 (self.performance_metrics['predictions_made'] - 1) + processing_time) /
                self.performance_metrics['predictions_made']
            )
            
            return result
            
        except Exception as e:
            logger.error(f"❌ Prediction failed for {task_name}: {e}")
            return None
    
    async def predict_all_tasks(self, strategy_data: Dict[str, Any]) -> Dict[str, ModelPrediction]:
        """
        Make predictions for all available tasks
        
        Args:
            strategy_data: Strategy data for prediction
            
        Returns:
            Dictionary of task_name -> ModelPrediction
        """
        predictions = {}
        
        for task_name in self.available_tasks:
            if task_name in self.loaded_models:
                prediction = await self.predict(task_name, strategy_data)
                if prediction:
                    predictions[task_name] = prediction
        
        return predictions
    
    async def get_ensemble_prediction(self, task_name: str, strategy_data: Dict[str, Any]) -> Optional[EnsemblePrediction]:
        """
        Get ensemble prediction (currently single model, but extensible)
        
        Args:
            task_name: Name of the prediction task
            strategy_data: Strategy data for prediction
            
        Returns:
            EnsemblePrediction object or None
        """
        try:
            # For now, we only have single models, but this can be extended
            individual_prediction = await self.predict(task_name, strategy_data)
            
            if not individual_prediction:
                return None
            
            # Create ensemble prediction
            ensemble_prediction = EnsemblePrediction(
                task_name=task_name,
                prediction=individual_prediction.prediction,
                confidence=individual_prediction.confidence,
                individual_predictions=[individual_prediction],
                consensus_score=1.0,  # Single model, so perfect consensus
                timestamp=datetime.now()
            )
            
            return ensemble_prediction
            
        except Exception as e:
            logger.error(f"❌ Ensemble prediction failed for {task_name}: {e}")
            return None
    
    def get_model_performance_summary(self) -> Dict[str, Any]:
        """Get performance summary of loaded models"""
        summary = {
            'loaded_models': list(self.loaded_models.keys()),
            'model_count': len(self.loaded_models),
            'performance_metrics': self.performance_metrics.copy(),
            'cache_size': len(self.prediction_cache),
            'available_tasks': self.available_tasks
        }
        
        # Add model metadata if available
        if self.model_metadata:
            summary['model_scores'] = {}
            for task, task_data in self.model_metadata.items():
                if 'lightgbm' in task_data:
                    model_data = task_data['lightgbm']
                    summary['model_scores'][task] = {
                        'r2_score': model_data.get('r2_score', 0.0),
                        'test_score': model_data.get('test_score', 0.0),
                        'train_score': model_data.get('train_score', 0.0)
                    }
        
        return summary
    
    def clear_cache(self):
        """Clear prediction cache"""
        self.prediction_cache.clear()
        logger.info("🧹 Prediction cache cleared")
    
    def get_feature_importance_for_task(self, task_name: str, top_n: int = 10) -> List[Dict[str, Any]]:
        """
        Get feature importance for a specific task
        
        Args:
            task_name: Name of the task
            top_n: Number of top features to return
            
        Returns:
            List of feature importance dictionaries
        """
        if task_name not in self.feature_importance:
            return []
        
        task_importance = self.feature_importance[task_name]
        if 'lightgbm' not in task_importance:
            return []
        
        return task_importance['lightgbm'][:top_n]

# Global instance for easy access
enhanced_model_integration = EnhancedModelIntegration()

async def initialize_enhanced_models():
    """Initialize the global enhanced model integration instance"""
    return await enhanced_model_integration.initialize()

def get_enhanced_model_integration() -> EnhancedModelIntegration:
    """Get the global enhanced model integration instance"""
    return enhanced_model_integration