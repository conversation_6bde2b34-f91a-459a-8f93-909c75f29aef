#!/usr/bin/env python3
"""
Enhanced Model Training Agent - Usage Example
Demonstrates how to use the enhanced training agent with backtesting data

This example shows:
1. Loading backtesting results
2. Training multiple models
3. Making predictions
4. Evaluating performance
5. Saving and loading models
"""

import asyncio
import sys
import logging
from pathlib import Path
import pandas as pd
import numpy as np

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from agents.enhanced_model_training_agent import EnhancedModelTrainingAgent, EnhancedTrainingConfig

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def create_sample_backtesting_data():
    """Create sample backtesting data for demonstration"""
    logger.info("Creating sample backtesting data...")
    
    np.random.seed(42)
    n_strategies = 100
    
    # Generate sample strategy results
    data = {
        'strategy_name': [f'Strategy_{i:03d}' for i in range(n_strategies)],
        'avg_sharpe_ratio': np.random.normal(0.5, 0.8, n_strategies),
        'avg_roi': np.random.normal(5.0, 15.0, n_strategies),
        'avg_profit_factor': np.random.lognormal(0.2, 0.5, n_strategies),
        'avg_max_drawdown': -np.abs(np.random.normal(8.0, 5.0, n_strategies)),
        'avg_var_95': -np.abs(np.random.normal(2.0, 1.0, n_strategies)),
        'avg_cvar_95': -np.abs(np.random.normal(3.0, 1.5, n_strategies)),
        'consistency_score': np.random.beta(2, 2, n_strategies),
        'avg_accuracy': np.random.beta(3, 2, n_strategies) * 100,
        'avg_total_trades': np.random.poisson(50, n_strategies),
        'walk_forward_steps': np.random.randint(3, 8, n_strategies),
        'avg_win': np.random.exponential(100, n_strategies),
        'avg_loss': -np.random.exponential(80, n_strategies),
    }
    
    # Create derived features
    df = pd.DataFrame(data)
    
    # Add some realistic relationships
    df['win_loss_ratio'] = df['avg_win'] / np.abs(df['avg_loss'])
    df['roi_drawdown_ratio'] = df['avg_roi'] / np.abs(df['avg_max_drawdown'])
    df['trades_per_period'] = df['avg_total_trades'] / df['walk_forward_steps']
    
    # Create target variables
    df['passes_risk_criteria'] = (
        (df['avg_sharpe_ratio'] >= 1.0) &
        (df['avg_profit_factor'] >= 1.2) &
        (df['avg_max_drawdown'] >= -10.0)
    )
    
    # Create regime suitability (simplified)
    regime_conditions = [
        df['avg_sharpe_ratio'] > 1.0,
        df['avg_sharpe_ratio'] < -0.5,
        (df['avg_sharpe_ratio'] >= -0.5) & (df['avg_sharpe_ratio'] <= 1.0) & (df['consistency_score'] > 0.6),
        df['consistency_score'] < 0.3,
    ]
    
    regime_values = ['trending_up', 'trending_down', 'ranging', 'high_volatility']
    
    df['best_regime'] = 'low_volatility'  # default
    for condition, regime in zip(regime_conditions, regime_values):
        df.loc[condition, 'best_regime'] = regime
    
    return df

async def demonstrate_training_workflow():
    """Demonstrate the complete training workflow"""
    
    print("🧠 Enhanced Model Training Agent - Example Workflow")
    print("="*60)
    
    try:
        # Step 1: Create sample data
        print("\n1. Creating sample backtesting data...")
        df = await create_sample_backtesting_data()
        print(f"   Created dataset with {len(df)} strategies and {len(df.columns)} features")
        
        # Save sample data
        data_dir = project_root / "data" / "backtest_improved"
        data_dir.mkdir(parents=True, exist_ok=True)
        data_file = data_dir / "sample_strategy_results.parquet"
        df.to_parquet(data_file)
        print(f"   Saved sample data to: {data_file}")
        
        # Step 2: Initialize training agent
        print("\n2. Initializing Enhanced Model Training Agent...")
        config = EnhancedTrainingConfig()
        config.input_file = "sample_strategy_results.parquet"
        config.optuna_trials = 20  # Reduced for demo
        config.optuna_timeout = 300  # 5 minutes for demo
        
        agent = EnhancedModelTrainingAgent(config)
        print("   ✓ Agent initialized")
        
        # Step 3: Load and preprocess data
        print("\n3. Loading and preprocessing data...")
        processed_df = await agent.load_backtesting_data(str(data_file))
        print(f"   ✓ Loaded and processed {processed_df.height} rows, {processed_df.width} columns")
        
        # Step 4: Train models
        print("\n4. Training models for all prediction tasks...")
        training_results = await agent.train_all_models(processed_df)
        
        # Step 5: Display results
        print("\n5. Training Results Summary:")
        print("="*60)
        
        for task_name, task_results in training_results.items():
            if "error" in task_results:
                print(f"\n❌ {task_name}: ERROR - {task_results['error']}")
                continue
            
            print(f"\n📊 {task_name.upper().replace('_', ' ')}:")
            
            for model_name, metrics in task_results.items():
                if isinstance(metrics, dict) and "test_score" in metrics:
                    score = metrics["test_score"]
                    if isinstance(score, (int, float)):
                        print(f"   {model_name:12}: {score:.4f}")
                    else:
                        print(f"   {model_name:12}: {score}")
                
                # Show additional metrics for the best model
                if model_name == "lightgbm" and isinstance(metrics, dict):
                    for metric_name, value in metrics.items():
                        if metric_name not in ["test_score", "train_score", "best_params"] and isinstance(value, (int, float)):
                            print(f"     {metric_name}: {value:.4f}")
        
        # Step 6: Demonstrate prediction
        print("\n6. Making predictions on sample data...")
        
        # Create sample prediction data
        sample_data = processed_df.head(5).to_pandas()
        
        for task_name in config.prediction_tasks.keys():
            if task_name in agent.models and agent.models[task_name]:
                predictions = await agent.predict(sample_data, task_name, use_ensemble=True)
                
                if "error" not in predictions:
                    print(f"\n🔮 Predictions for {task_name}:")
                    for model_name, pred in predictions.items():
                        if len(pred) > 0:
                            print(f"   {model_name}: {pred[:3]}...")  # Show first 3 predictions
                break
        
        # Step 7: Feature importance
        print("\n7. Feature Importance (Top 10):")
        print("="*60)
        
        for task_name, importance_dict in agent.feature_importance.items():
            if importance_dict:
                print(f"\n📈 {task_name.upper().replace('_', ' ')}:")
                
                # Get importance from first available model
                for model_name, importance_list in importance_dict.items():
                    if importance_list:
                        print(f"   Top features ({model_name}):")
                        for i, feature_info in enumerate(importance_list[:10]):
                            feature_name = feature_info.get('feature', 'Unknown')
                            importance = feature_info.get('importance', 0)
                            print(f"     {i+1:2d}. {feature_name:20}: {importance:.4f}")
                        break
                break
        
        # Step 8: Model saving demonstration
        print("\n8. Models automatically saved to:")
        models_dir = project_root / config.models_dir
        print(f"   {models_dir}")
        
        if models_dir.exists():
            saved_files = list(models_dir.rglob("*.joblib"))
            print(f"   Found {len(saved_files)} saved model files")
        
        print("\n" + "="*60)
        print("✅ EXAMPLE WORKFLOW COMPLETED SUCCESSFULLY!")
        print("="*60)
        
        print("\nKey Features Demonstrated:")
        print("• Multi-task learning with different prediction objectives")
        print("• Automated hyperparameter optimization with Optuna")
        print("• Multiple model types (LightGBM, XGBoost, CatBoost, TabNet)")
        print("• Feature engineering and importance analysis")
        print("• Ensemble model creation")
        print("• Model persistence and loading")
        print("• Comprehensive performance metrics")
        
        return True
        
    except Exception as e:
        logger.error(f"Example workflow failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def demonstrate_model_loading():
    """Demonstrate loading and using saved models"""
    
    print("\n🔄 Model Loading Demonstration")
    print("="*40)
    
    try:
        # Initialize agent
        config = EnhancedTrainingConfig()
        agent = EnhancedModelTrainingAgent(config)
        
        # Try to load latest models
        await agent.load_models()
        
        print("✓ Models loaded successfully")
        
        # Show loaded models
        for task_name, models in agent.models.items():
            print(f"   {task_name}: {list(models.keys())}")
        
        return True
        
    except Exception as e:
        print(f"❌ Model loading failed: {e}")
        return False

async def main():
    """Main example function"""
    
    # Run the main demonstration
    success = await demonstrate_training_workflow()
    
    if success:
        # Demonstrate model loading
        await demonstrate_model_loading()
        
        print("\n🎉 All demonstrations completed successfully!")
        print("\nNext Steps:")
        print("1. Replace sample data with your actual backtesting results")
        print("2. Adjust configuration in enhanced_training_config.yaml")
        print("3. Run the training agent on your data")
        print("4. Use trained models for strategy selection and optimization")
    else:
        print("\n❌ Demonstration failed. Please check the logs for details.")

if __name__ == "__main__":
    asyncio.run(main())