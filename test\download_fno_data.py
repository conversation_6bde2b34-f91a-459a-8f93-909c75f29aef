"""
Interactive Historical Data Downloader with MongoDB Integration - SPEED OPTIMIZED VERSION

🚀 PERFORMANCE OPTIMIZATIONS:
- Batch downloads: Download multiple symbols in single yfinance calls (10-50x faster)
- Threading: Concurrent downloads for SmartAPI with rate limiting
- Rate limiting: Intelligent delays to avoid 429 errors
- Retry mechanism: Exponential backoff for failed requests
- Configurable settings: Easily adjust performance parameters

This enhanced script provides an interactive menu to download historical data for different stock categories:
- F&O Enabled Stocks (NIFTYFNO)
- Nifty 50 Stocks
- Nifty 100 Stocks
- Nifty 200 Stocks
- Nifty 500 Stocks
- Custom Symbol List

Features:
- Downloads data from Yahoo Finance or SmartAPI
- Multiple timeframes: 5m, 15m, 1hr, D (daily), W (weekly) - Optimized for 15-minute candles
- Dynamic period adjustment based on timeframe
- Integrates with MongoDB centralized symbol mappings
- Stores data in MongoDB with proper timezone handling (UTC to IST)
- Interactive menu system for easy selection
- Progress tracking and error handling
- Configurable date ranges and timeframes
- Enhanced volume data validation and logging

TIMEZONE HANDLING:
- yfinance downloads data in UTC timezone
- SmartAPI downloads data in IST timezone
- We convert all data to IST before storing in MongoDB
- All timestamps in MongoDB are stored as timezone-naive IST datetime objects

VOLUME DATA HANDLING:
- Both yfinance and SmartAPI provide volume data
- Volume is stored as integer values in MongoDB
- Volume validation ensures data quality
- Special handling for zero/missing volume data
"""

import os
import sys
import time
import logging
import argparse
from datetime import datetime, timedelta
from typing import List, Optional, Dict
import yfinance as yf
import pandas as pd
import pytz
from pymongo import MongoClient
from pymongo.errors import BulkWriteError
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
import queue
from functools import wraps
import random

# SmartAPI imports (optional - will fallback if not available)
try:
    from SmartApi import SmartConnect
    import pyotp
    SMARTAPI_AVAILABLE = True
except ImportError:
    SMARTAPI_AVAILABLE = False
    SmartConnect = None
    pyotp = None

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("historical_data_download.log"),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# ============================================================================
# OPTIMIZATION CONFIGURATION - Adjust these settings for better performance
# ============================================================================

# Rate limiting configuration (adjust based on your network and yfinance limits)
RATE_LIMIT_DELAY = 0.5  # Minimum delay between requests (seconds) - reduce for faster downloads
MAX_CONCURRENT_DOWNLOADS = 3  # Maximum concurrent downloads for SmartAPI
BATCH_SIZE = 15  # Number of symbols to download in one yfinance batch (increased from 10)
MAX_RETRIES = 3  # Maximum retry attempts for failed downloads
RETRY_DELAY_BASE = 1.0  # Base delay for exponential backoff

# Performance settings
ENABLE_BATCH_DOWNLOADS = True  # Use batch downloads for yfinance (much faster)
ENABLE_THREADING = True  # Use threading for SmartAPI downloads
ENABLE_RATE_LIMITING = True  # Enable rate limiting (disable for testing only)

# Global rate limiter
rate_limiter_lock = threading.Lock()
last_request_time = 0

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# MongoDB connection settings
MONGO_URL = os.getenv('MONGO_URL', 'mongodb://localhost:27017')
DB_NAME = os.getenv('DB_NAME', 'intraday_trading')
COLLECTION_NAME = 'stocks'

# SmartAPI configuration
SMARTAPI_CONFIG = {
    'api_key': os.getenv('ANGEL_API_KEY'),
    'username': os.getenv('ANGEL_CLIENT_ID'),
    'password': os.getenv('ANGEL_CLIENT_PIN'),
    'totp_token': os.getenv('ANGEL_TOTP_KEY')
}

# Timeframe mappings - Optimized for 15-minute candles with volume
TIMEFRAME_MAPPINGS = {
    # Display name -> (yfinance_interval, smartapi_interval, db_suffix, default_days)
    '5m': ('5m', 'FIVE_MINUTE', '5min', 30),
    '15m': ('15m', 'FIFTEEN_MINUTE', '15min', 45),  # Conservative 45 days for yfinance 15m data limit
    '1hr': ('1h', 'ONE_HOUR', '1hr', 180),
    'D': ('1d', 'ONE_DAY', 'daily', 365),
    'W': ('1wk', 'ONE_WEEK', 'weekly', 730)
}

# Data source options
DATA_SOURCES = {
    'yfinance': 'Yahoo Finance',
    'smartapi': 'SmartAPI (Angel One)'
}

# Import symbol mapping service for getting stock categories
SymbolMappingService = None
SYMBOL_MAPPING_AVAILABLE = False

try:
    # Add the services directory to the path
    current_dir = os.path.dirname(os.path.abspath(__file__))
    # Go up two levels: python/downloaders -> python -> root
    root_dir = os.path.dirname(os.path.dirname(current_dir))
    services_path = os.path.join(root_dir, 'python_orders', 'services')

    if services_path not in sys.path:
        sys.path.insert(0, services_path)

    # Import the service using importlib for better error handling
    import importlib.util
    spec = importlib.util.spec_from_file_location(
        "symbol_mapping_service",
        os.path.join(services_path, "symbol_mapping_service.py")
    )
    if spec and spec.loader:
        symbol_mapping_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(symbol_mapping_module)
        SymbolMappingService = symbol_mapping_module.SymbolMappingService
        SYMBOL_MAPPING_AVAILABLE = True
        logger.info("✅ Symbol mapping service imported successfully")
    else:
        logger.warning("⚠️ Could not load symbol mapping service spec")

except ImportError as e:
    logger.warning(f"⚠️ Symbol mapping service not available: {e}. Using fallback stock lists.")
except Exception as e:
    logger.warning(f"⚠️ Error importing symbol mapping service: {e}. Using fallback stock lists.")

# Fallback stock lists if symbol mapping service is not available
FALLBACK_STOCK_LISTS = {
    'NIFTYFNO': [
        # Core F&O stocks - approximately 220 stocks
        'RELIANCE', 'TCS', 'HDFCBANK', 'INFY', 'ICICIBANK', 'SBIN', 'BHARTIARTL',
        'ITC', 'KOTAKBANK', 'LT', 'HINDUNILVR', 'BAJFINANCE', 'ASIANPAINT',
        'MARUTI', 'AXISBANK', 'TATASTEEL', 'SUNPHARMA', 'TITAN', 'BAJAJFINSV',
        'WIPRO', 'HCLTECH', 'ULTRACEMCO', 'NTPC', 'POWERGRID', 'TATAMOTORS',
        'M&M', 'TECHM', 'ADANIPORTS', 'GRASIM', 'DRREDDY', 'INDUSINDBK',
        'NESTLEIND', 'COALINDIA', 'HINDALCO', 'ONGC', 'SBILIFE', 'BRITANNIA',
        'DIVISLAB', 'CIPLA', 'EICHERMOT', 'HEROMOTOCO', 'JSWSTEEL', 'BAJAJ-AUTO',
        'SHREECEM', 'UPL', 'IOC', 'BPCL', 'TATACONSUM', 'HDFCLIFE', 'APOLLOHOSP',
        'TRENT', 'ADANIENT', 'GODREJCP', 'DABUR', 'HAVELLS', 'PIDILITIND',
        'MARICO', 'COLPAL', 'MCDOWELL-N', 'BERGEPAINT', 'SIEMENS', 'DLF',
        'GLAND', 'TORNTPHARM', 'AUROPHARMA', 'LUPIN', 'BIOCON', 'CADILAHC',
        'ZYDUSLIFE', 'LALPATHLAB', 'METROPOLIS', 'FORTIS', 'MAXHEALTH',
        'HDFCAMC', 'MUTHOOTFIN', 'BAJAJHLDNG', 'PGHH', 'UNITDSPR', 'ABFRL',
        'POLYCAB', 'OFSS', 'PFC', 'SAIL', 'GODREJPROP', 'PHOENIXLTD',
        'YESBANK', 'IDEA', 'BDL', 'BLUESTARCO', 'KAYNES', 'MANKIND',
        'MAZDOCK', 'PPLPHARMA', 'RVNL', 'UNOMINDA', 'CANBK', 'PNB',
        'BANKBARODA', 'UNIONBANK', 'IDFCFIRSTB', 'FEDERALBNK', 'RBLBANK',
        'BANDHANBNK', 'AUBANK', 'INDHOTEL', 'LEMONTREE', 'CHALET',
        'JUBLFOOD', 'WESTLIFE', 'DEVYANI', 'ZOMATO', 'NYKAA', 'PAYTM',
        'POLICYBZR', 'CARTRADE', 'EASEMYTRIP', 'IXIGO', 'RAILTEL',
        'IRCTC', 'CONCOR', 'CONTAINER', 'GATEWAY', 'ALLCARGO', 'BLUEDART',
        'DELHIVERY', 'MAHLOG', 'GATI', 'SNOWMAN', 'COLDEX', 'KPITTECH',
        'LTTS', 'PERSISTENT', 'MINDTREE', 'MPHASIS', 'COFORGE', 'LTIM',
        'ZENSAR', 'CYIENT', 'RATEGAIN', 'HAPPSTMNDS', 'NEWGEN', 'SONATSOFTW',
        'INTELLECT', 'RAMCOCEM', 'JKCEMENT', 'HEIDELBERG', 'AMBUJCEM',
        'DALMIA', 'PRISM', 'STARCEMENT', 'ORIENTCEM', 'INDIACEM',
        'JKLAKSHMI', 'BURNPUR', 'KAJARIACER', 'CERA', 'SOMANY',
        'HSIL', 'RAJRATAN', 'ORIENTBELL', 'NITCO', 'DIXON',
        'VOLTAS', 'WHIRLPOOL', 'CROMPTON', 'AMBER', 'SCHAEFFLER',
        'MOTHERSON', 'BALKRISIND', 'APOLLOTYRE', 'MRF', 'CEATLTD',
        'EXIDEIND', 'AMBUJACEM', 'ACC', 'JKPAPER', 'VEDL',
        'HINDZINC', 'NATIONALUM', 'JINDALSTEL', 'WELCORP', 'WELSPUNIND',
        'GMRINFRA', 'IRB', 'IRCON', 'NCC', 'PFC', 'REC',
        'SJVN', 'NHPC', 'THERMAX', 'BHEL', 'BEL', 'HAL',
        'BEML', 'COCHINSHIP', 'GRSE', 'MAZAGON', 'TIINDIA', 'RATNAMANI',
        'ASTRAL', 'FINOLEX', 'SUPREME', 'NILKAMAL', 'RELAXO', 'BATA',
        'PAGEIND', 'VBL', 'RADICO', 'MCDOWELL-N', 'GLOBUSSPR', 'SHOPERSTOP',
        'TRENT', 'ADITYA', 'SPANDANA', 'MANAPPURAM', 'MUTHOOTFIN', 'CHOLAFIN',
        'BAJAJHLDNG', 'BAJAJFINSV', 'BAJFINANCE', 'SBICARD', 'HDFCAMC',
        'NIPPONLIF', 'SBILIFE', 'HDFCLIFE', 'ICICIPRULI', 'ICICIGI',
        'NIACL', 'STARHEALTH', 'CARERATING', 'CRISIL', 'CREDITACC'
    ],
    'NIFTY50': [
        'RELIANCE', 'TCS', 'HDFCBANK', 'INFY', 'ICICIBANK', 'SBIN', 'BHARTIARTL',
        'ITC', 'KOTAKBANK', 'LT', 'HINDUNILVR', 'BAJFINANCE', 'ASIANPAINT',
        'MARUTI', 'AXISBANK', 'TATASTEEL', 'SUNPHARMA', 'TITAN', 'BAJAJFINSV',
        'WIPRO', 'HCLTECH', 'ULTRACEMCO', 'NTPC', 'POWERGRID', 'TATAMOTORS',
        'M&M', 'TECHM', 'ADANIPORTS', 'GRASIM', 'DRREDDY', 'INDUSINDBK',
        'NESTLEIND', 'COALINDIA', 'HINDALCO', 'ONGC', 'SBILIFE', 'BRITANNIA',
        'DIVISLAB', 'CIPLA', 'EICHERMOT', 'HEROMOTOCO', 'JSWSTEEL', 'BAJAJ-AUTO',
        'SHREECEM', 'UPL', 'IOC', 'BPCL', 'TATACONSUM', 'HDFCLIFE'
    ],
    'NIFTY100': [
        # All NIFTY50 stocks plus additional 50 stocks
        'RELIANCE', 'TCS', 'HDFCBANK', 'INFY', 'ICICIBANK', 'SBIN', 'BHARTIARTL',
        'ITC', 'KOTAKBANK', 'LT', 'HINDUNILVR', 'BAJFINANCE', 'ASIANPAINT',
        'MARUTI', 'AXISBANK', 'TATASTEEL', 'SUNPHARMA', 'TITAN', 'BAJAJFINSV',
        'WIPRO', 'HCLTECH', 'ULTRACEMCO', 'NTPC', 'POWERGRID', 'TATAMOTORS',
        'M&M', 'TECHM', 'ADANIPORTS', 'GRASIM', 'DRREDDY', 'INDUSINDBK',
        'NESTLEIND', 'COALINDIA', 'HINDALCO', 'ONGC', 'SBILIFE', 'BRITANNIA',
        'DIVISLAB', 'CIPLA', 'EICHERMOT', 'HEROMOTOCO', 'JSWSTEEL', 'BAJAJ-AUTO',
        'SHREECEM', 'UPL', 'IOC', 'BPCL', 'TATACONSUM', 'HDFCLIFE',
        # Additional NIFTY100 stocks
        'APOLLOHOSP', 'TRENT', 'ADANIENT', 'GODREJCP', 'DABUR', 'HAVELLS',
        'PIDILITIND', 'MARICO', 'COLPAL', 'MCDOWELL-N', 'BERGEPAINT', 'SIEMENS',
        'DLF', 'GLAND', 'TORNTPHARM', 'AUROPHARMA', 'LUPIN', 'BIOCON',
        'ZYDUSLIFE', 'LALPATHLAB', 'METROPOLIS', 'FORTIS', 'MAXHEALTH',
        'HDFCAMC', 'MUTHOOTFIN', 'BAJAJHLDNG', 'PGHH', 'UNITDSPR', 'ABFRL',
        'POLYCAB', 'OFSS', 'PFC', 'SAIL', 'GODREJPROP', 'PHOENIXLTD',
        'YESBANK', 'IDEA', 'BDL', 'BLUESTARCO', 'KAYNES', 'MANKIND',
        'MAZDOCK', 'PPLPHARMA', 'RVNL', 'UNOMINDA', 'CANBK', 'PNB',
        'BANKBARODA', 'UNIONBANK', 'IDFCFIRSTB', 'FEDERALBNK', 'RBLBANK'
    ],
    'NIFTY200': [
        # Includes all NIFTY100 plus additional 100 stocks
        'RELIANCE', 'TCS', 'HDFCBANK', 'INFY', 'ICICIBANK', 'SBIN', 'BHARTIARTL',
        'ITC', 'KOTAKBANK', 'LT', 'HINDUNILVR', 'BAJFINANCE', 'ASIANPAINT',
        'MARUTI', 'AXISBANK', 'TATASTEEL', 'SUNPHARMA', 'TITAN', 'BAJAJFINSV',
        'WIPRO', 'HCLTECH', 'ULTRACEMCO', 'NTPC', 'POWERGRID', 'TATAMOTORS',
        'M&M', 'TECHM', 'ADANIPORTS', 'GRASIM', 'DRREDDY', 'INDUSINDBK',
        'NESTLEIND', 'COALINDIA', 'HINDALCO', 'ONGC', 'SBILIFE', 'BRITANNIA',
        'DIVISLAB', 'CIPLA', 'EICHERMOT', 'HEROMOTOCO', 'JSWSTEEL', 'BAJAJ-AUTO',
        'SHREECEM', 'UPL', 'IOC', 'BPCL', 'TATACONSUM', 'HDFCLIFE',
        # All NIFTY100 stocks
        'APOLLOHOSP', 'TRENT', 'ADANIENT', 'GODREJCP', 'DABUR', 'HAVELLS',
        'PIDILITIND', 'MARICO', 'COLPAL', 'MCDOWELL-N', 'BERGEPAINT', 'SIEMENS',
        'DLF', 'GLAND', 'TORNTPHARM', 'AUROPHARMA', 'LUPIN', 'BIOCON',
        'ZYDUSLIFE', 'LALPATHLAB', 'METROPOLIS', 'FORTIS', 'MAXHEALTH',
        'HDFCAMC', 'MUTHOOTFIN', 'BAJAJHLDNG', 'PGHH', 'UNITDSPR', 'ABFRL',
        'POLYCAB', 'OFSS', 'PFC', 'SAIL', 'GODREJPROP', 'PHOENIXLTD',
        'YESBANK', 'IDEA', 'BDL', 'BLUESTARCO', 'KAYNES', 'MANKIND',
        'MAZDOCK', 'PPLPHARMA', 'RVNL', 'UNOMINDA', 'CANBK', 'PNB',
        'BANKBARODA', 'UNIONBANK', 'IDFCFIRSTB', 'FEDERALBNK', 'RBLBANK',
        # Additional NIFTY200 stocks
        'BANDHANBNK', 'AUBANK', 'INDHOTEL', 'LEMONTREE', 'CHALET',
        'JUBLFOOD', 'WESTLIFE', 'DEVYANI', 'ZOMATO', 'NYKAA', 'PAYTM',
        'POLICYBZR', 'CARTRADE', 'EASEMYTRIP', 'IXIGO', 'RAILTEL',
        'IRCTC', 'CONCOR', 'CONTAINER', 'GATEWAY', 'ALLCARGO', 'BLUEDART',
        'DELHIVERY', 'MAHLOG', 'GATI', 'SNOWMAN', 'COLDEX', 'KPITTECH',
        'LTTS', 'PERSISTENT', 'MINDTREE', 'MPHASIS', 'COFORGE', 'LTIM',
        'ZENSAR', 'CYIENT', 'RATEGAIN', 'HAPPSTMNDS', 'NEWGEN', 'SONATSOFTW',
        'INTELLECT', 'RAMCOCEM', 'JKCEMENT', 'HEIDELBERG', 'AMBUJCEM',
        'DALMIA', 'PRISM', 'STARCEMENT', 'ORIENTCEM', 'INDIACEM',
        'JKLAKSHMI', 'BURNPUR', 'KAJARIACER', 'CERA', 'SOMANY',
        'HSIL', 'RAJRATAN', 'ORIENTBELL', 'NITCO', 'DIXON',
        'VOLTAS', 'WHIRLPOOL', 'CROMPTON', 'AMBER', 'SCHAEFFLER',
        'MOTHERSON', 'BALKRISIND', 'APOLLOTYRE', 'MRF', 'CEATLTD',
        'EXIDEIND', 'AMBUJACEM', 'ACC', 'JKPAPER', 'VEDL',
        'HINDZINC', 'NATIONALUM', 'JINDALSTEL', 'WELCORP', 'WELSPUNIND',
        'GMRINFRA', 'IRB', 'IRCON', 'NCC', 'REC', 'SJVN',
        'NHPC', 'THERMAX', 'BHEL', 'BEL', 'HAL', 'BEML',
        'COCHINSHIP', 'GRSE', 'MAZAGON', 'TIINDIA', 'RATNAMANI',
        'ASTRAL', 'FINOLEX', 'SUPREME', 'NILKAMAL', 'RELAXO'
    ],
    'NIFTY500': [
        # Comprehensive NIFTY500 list (503 stocks as per NSE)
        '360ONE', '3MINDIA', 'ABB', 'ACC', 'ACMESOLAR', 'AIAENG', 'APLAPOLLO', 'AUBANK', 'AWL',
        'AADHARHFC', 'AARTIIND', 'AAVAS', 'ABBOTINDIA', 'ACE', 'ADANIENSOL', 'ADANIENT', 'ADANIGREEN',
        'ADANIPORTS', 'ADANIPOWER', 'ATGL', 'ABCAPITAL', 'ABFRL', 'ABREL', 'ABSLAMC', 'AEGISLOG',
        'AFCONS', 'AFFLE', 'AJANTPHARM', 'AKUMS', 'APLLTD', 'ALIVUS', 'ALKEM', 'ALKYLAMINE',
        'ALOKINDS', 'AMBER', 'AMBUJACEM', 'ANANDRATHI', 'ANANTRAJ', 'ANGELONE', 'APARINDS',
        'APOLLOHOSP', 'APOLLOTYRE', 'APTUS', 'ARCHIDPLY', 'AREPL', 'ARVINDFASN', 'ASAHIINDIA',
        'ASHOKLEY', 'ASIANPAINT', 'ASTERDM', 'ASTRAL', 'ATUL', 'AUROPHARMA', 'AVANTIFEED',
        'AXISBANK', 'BAJAJ-AUTO', 'BAJAJFINSV', 'BAJFINANCE', 'BAJAJHLDNG', 'BALAMINES',
        'BALKRISIND', 'BALRAMCHIN', 'BANDHANBNK', 'BANKBARODA', 'BANKINDIA', 'MAHABANK',
        'BATAINDIA', 'BDL', 'BEL', 'BEML', 'BERGEPAINT', 'BHARATFORG', 'BHEL', 'BHARTIARTL',
        'BIOCON', 'BIRLACORPN', 'BLUEDART', 'BLUESTARCO', 'BPCL', 'BRITANNIA', 'BSOFT',
        'CAMS', 'CANBK', 'CANFINHOME', 'CAPLIPOINT', 'CARBORUNIV', 'CASTROLIND', 'CCL',
        'CEATLTD', 'CENTRALBK', 'CENTURYPLY', 'CENTURYTEX', 'CERA', 'CHALET', 'CHAMBLFERT',
        'CHENNPETRO', 'CHOLAFIN', 'CIPLA', 'CUB', 'COALINDIA', 'COCHINSHIP', 'COFORGE',
        'COLPAL', 'CONCOR', 'COROMANDEL', 'CREDITACC', 'CRISIL', 'CROMPTON', 'CUB',
        'CUMMINSIND', 'CYIENT', 'DABUR', 'DALBHARAT', 'DEEPAKNTR', 'DELTACORP', 'DEVYANI',
        'DHANI', 'DHANUKA', 'DIVISLAB', 'DIXON', 'DLF', 'DRREDDY', 'EASEMYTRIP', 'ECLERX',
        'EDELWEISS', 'EICHERMOT', 'EIDPARRY', 'EIHOTEL', 'ELGIEQUIP', 'EMAMILTD', 'ENDURANCE',
        'ENGINERSIN', 'EQUITAS', 'ERIS', 'ESCORTS', 'EXIDEIND', 'FDC', 'FEDERALBNK',
        'FINEORG', 'FINPIPE', 'FIRSTSOURCE', 'FIVESTAR', 'FORTIS', 'FSL', 'GAIL',
        'GALAXYSURF', 'GARFIBRES', 'GESHIP', 'GILLETTE', 'GLAND', 'GLAXO', 'GLENMARK',
        'GMMPFAUDLR', 'GMRINFRA', 'GNFC', 'GODFRYPHLP', 'GODREJCP', 'GODREJIND', 'GODREJPROP',
        'GRANULES', 'GRASIM', 'GREAVESCOT', 'GRINDWELL', 'GSFC', 'GSPL', 'GUJALKALI',
        'GUJGASLTD', 'GULFOILLUB', 'HAL', 'HAPPSTMNDS', 'HATSUN', 'HAVELLS', 'HCLTECH',
        'HDFCAMC', 'HDFCBANK', 'HDFCLIFE', 'HFCL', 'HGINFRA', 'HIMATSEIDE', 'HINDALCO',
        'HINDCOPPER', 'HINDPETRO', 'HINDUNILVR', 'HINDZINC', 'HONAUT', 'HSCL', 'HUDCO',
        'ICICIBANK', 'ICICIGI', 'ICICIPRULI', 'IDEA', 'IDFC', 'IDFCFIRSTB', 'IEX',
        'IFBIND', 'IGL', 'IIFL', 'INDHOTEL', 'INDIACEM', 'INDIAMART', 'INDIANB',
        'INDIGO', 'INDOCO', 'INDUSINDBK', 'INDUSTOWER', 'INFIBEAM', 'INFY', 'INGERRAND',
        'INOXLEISUR', 'INTELLECT', 'IOC', 'IPCALAB', 'IRB', 'IRCON', 'IRCTC',
        'ISEC', 'ITC', 'ITI', 'JBCHEPHARM', 'JKCEMENT', 'JKLAKSHMI', 'JKPAPER',
        'JMFINANCIL', 'JSL', 'JSWENERGY', 'JSWSTEEL', 'JUBLFOOD', 'JUSTDIAL', 'JYOTHYLAB',
        'KAJARIACER', 'KALPATPOWR', 'KANSAINER', 'KARURVYSYA', 'KAYNES', 'KEC',
        'KEI', 'KFINTECH', 'KIMS', 'KNRCON', 'KOTAKBANK', 'KPITTECH', 'KRBL',
        'L&TFH', 'LALPATHLAB', 'LAURUSLABS', 'LAXMIMACH', 'LEMONTREE', 'LICHSGFIN',
        'LINDEINDIA', 'LT', 'LTIM', 'LTTS', 'LUPIN', 'LUXIND', 'LXCHEM',
        'M&M', 'M&MFIN', 'MAHABANK', 'MAHINDCIE', 'MAHLOG', 'MANAPPURAM', 'MANKIND',
        'MAPMYINDIA', 'MARICO', 'MARUTI', 'MASTEK', 'MAXHEALTH', 'MAZDOCK', 'MCDOWELL-N',
        'MCX', 'MEDPLUS', 'METROBRAND', 'METROPOLIS', 'MFSL', 'MGL', 'MHRIL',
        'MINDTREE', 'MIDHANI', 'MMTC', 'MOIL', 'MOTHERSON', 'MOTILALOFS', 'MPHASIS',
        'MRF', 'MRPL', 'MUTHOOTFIN', 'NATCOPHARM', 'NATIONALUM', 'NAUKRI', 'NAVINFLUOR',
        'NBCC', 'NCC', 'NESTLEIND', 'NETWORK18', 'NEWGEN', 'NHPC', 'NIACL',
        'NIITLTD', 'NLCINDIA', 'NMDC', 'NOCIL', 'NTPC', 'NUVOCO', 'NYKAA',
        'OBEROIRLTY', 'OFSS', 'OIL', 'ONGC', 'ORIENTBELL', 'ORIENTCEM', 'ORIENTELEC',
        'PAGEIND', 'PARAS', 'PATANJALI', 'PAYTM', 'PBAINFRA', 'PEL', 'PERSISTENT',
        'PETRONET', 'PFC', 'PFIZER', 'PGHH', 'PHOENIXLTD', 'PIDILITIND', 'PIIND',
        'PNB', 'PNBHOUSING', 'PNCINFRA', 'POLICYBZR', 'POLYCAB', 'POLYMED', 'POONAWALLA',
        'POWERGRID', 'PPLPHARMA', 'PRSMJOHNSN', 'PSB', 'PVR', 'QUESS', 'RADICO',
        'RAILTEL', 'RAIN', 'RAJESHEXPO', 'RAMCOCEM', 'RANBAXY', 'RATEGAIN', 'RATNAMANI',
        'RBLBANK', 'RCF', 'RECLTD', 'REDINGTON', 'RELAXO', 'RELIANCE', 'RENUKA',
        'RESPONIND', 'RITES', 'RVNL', 'SAIL', 'SANOFI', 'SAPPHIRE', 'SAREGAMA',
        'SBICARD', 'SBILIFE', 'SBIN', 'SCHAEFFLER', 'SCHNEIDER', 'SCI', 'SFL',
        'SHANKARA', 'SHILPAMED', 'SHOPERSTOP', 'SHREECEM', 'SHRIRAMFIN', 'SIEMENS', 'SIS',
        'SJVN', 'SKFINDIA', 'SOBHA', 'SOLARINDS', 'SONACOMS', 'SONATSOFTW', 'SOUTHBANK',
        'SPANDANA', 'SPARC', 'SRF', 'STARHEALTH', 'STLTECH', 'SUDARSCHEM', 'SUMICHEM',
        'SUNPHARMA', 'SUNTV', 'SUPRAJIT', 'SURYODAY', 'SUZLON', 'SWANENERGY', 'SYMPHONY',
        'SYNGENE', 'TANLA', 'TATACOMM', 'TATACONSUM', 'TATAELXSI', 'TATAMOTORS', 'TATAPOWER',
        'TATASTEEL', 'TCS', 'TEAMLEASE', 'TECHM', 'TEJASNET', 'THERMAX', 'THYROCARE',
        'TIDEWATER', 'TIINDIA', 'TIMKEN', 'TITAN', 'TORNTPHARM', 'TORNTPOWER', 'TRENT',
        'TRIDENT', 'TRITURBINE', 'TTKPRESTIG', 'TV18BRDCST', 'TVSMOTOR', 'UJJIVAN',
        'ULTRACEMCO', 'UNIONBANK', 'UPL', 'UTIAMC', 'VAIBHAVGBL', 'VARROC', 'VBL',
        'VEDL', 'VENKEYS', 'VGUARD', 'VINATIORGA', 'VIPIND', 'VOLTAS', 'VTL',
        'WELCORP', 'WELSPUNIND', 'WESTLIFE', 'WHIRLPOOL', 'WIPRO', 'WOCKPHARMA', 'YESBANK',
        'ZEEL', 'ZENSARTECH', 'ZOMATO', 'ZYDUSLIFE'
    ]
}

def rate_limited(func):
    """Decorator to enforce rate limiting on API calls"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        if not ENABLE_RATE_LIMITING:
            return func(*args, **kwargs)

        global last_request_time
        with rate_limiter_lock:
            current_time = time.time()
            time_since_last = current_time - last_request_time
            if time_since_last < RATE_LIMIT_DELAY:
                sleep_time = RATE_LIMIT_DELAY - time_since_last
                logger.debug(f"Rate limiting: sleeping for {sleep_time:.2f} seconds")
                time.sleep(sleep_time)
            last_request_time = time.time()
        return func(*args, **kwargs)
    return wrapper

def retry_on_failure(max_retries=MAX_RETRIES, base_delay=RETRY_DELAY_BASE):
    """Decorator to retry failed operations with exponential backoff"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            for attempt in range(max_retries):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    if attempt == max_retries - 1:
                        logger.error(f"Failed after {max_retries} attempts: {e}")
                        raise

                    delay = base_delay * (2 ** attempt) + random.uniform(0, 1)
                    logger.warning(f"Attempt {attempt + 1} failed: {e}. Retrying in {delay:.2f} seconds...")
                    time.sleep(delay)
            return None
        return wrapper
    return decorator

@rate_limited
@retry_on_failure()
def download_single_symbol_yfinance(symbol: str, timeframe: str, start_date: datetime, end_date: datetime) -> Optional[pd.DataFrame]:
    """Download data for a single symbol from yfinance with rate limiting and retry"""
    try:
        # Validate and adjust date range for yfinance limits
        validated_start, validated_end = validate_yfinance_date_range(start_date, end_date, timeframe)

        yfinance_interval, _, _, _ = TIMEFRAME_MAPPINGS[timeframe]
        yahoo_symbol = f"{symbol}.NS"

        logger.debug(f"Downloading {symbol} from {validated_start} to {validated_end}")

        df = yf.download(
            yahoo_symbol,
            start=validated_start,
            end=validated_end,
            interval=yfinance_interval,
            auto_adjust=True,
            prepost=False,
            threads=False,  # Disable threading in individual calls
            group_by='ticker',
            progress=False
        )

        if df.empty:
            logger.warning(f"No data available for {symbol}")
            return None

        # Handle multi-level column structure if present
        if isinstance(df.columns, pd.MultiIndex):
            df = df.droplevel(0, axis=1)

        return df

    except Exception as e:
        logger.error(f"Error downloading {symbol}: {e}")
        raise

def download_batch_yfinance(symbols: List[str], timeframe: str, start_date: datetime, end_date: datetime) -> Dict[str, pd.DataFrame]:
    """Download data for multiple symbols in a single yfinance call (most efficient)"""
    if not ENABLE_BATCH_DOWNLOADS:
        # Fallback to individual downloads
        results = {}
        for symbol in symbols:
            df = download_single_symbol_yfinance(symbol, timeframe, start_date, end_date)
            if df is not None:
                results[symbol] = df
        return results

    try:
        # Validate and adjust date range for yfinance limits
        validated_start, validated_end = validate_yfinance_date_range(start_date, end_date, timeframe)

        yfinance_interval, _, _, _ = TIMEFRAME_MAPPINGS[timeframe]
        yahoo_symbols = [f"{symbol}.NS" for symbol in symbols]

        logger.info(f"Batch downloading {len(symbols)} symbols: {', '.join(symbols)}")
        logger.debug(f"Yahoo symbols: {yahoo_symbols}")
        logger.debug(f"Date range: {validated_start} to {validated_end}")

        # Use yfinance batch download (most efficient method)
        data = yf.download(
            yahoo_symbols,
            start=validated_start,
            end=validated_end,
            interval=yfinance_interval,
            auto_adjust=True,
            prepost=False,
            threads=True,  # Enable threading for batch downloads
            group_by='ticker',
            progress=False
        )

        # Debug: Log data structure
        if data.empty:
            logger.warning(f"Batch download returned empty data for symbols: {', '.join(symbols)}")
            return {}

        logger.debug(f"Batch data info: shape={data.shape}, columns_type={type(data.columns)}")
        if isinstance(data.columns, pd.MultiIndex):
            logger.debug(f"MultiIndex levels: {data.columns.nlevels}, level_0_values: {list(data.columns.levels[0]) if len(data.columns.levels) > 0 else 'None'}")
        else:
            logger.debug(f"Regular columns: {list(data.columns)[:10]}...")  # Show first 10 columns

        results = {}

        if len(symbols) == 1:
            # Single symbol case
            symbol = symbols[0]
            if not data.empty:
                # Handle multi-level column structure if present (same as individual download)
                if isinstance(data.columns, pd.MultiIndex):
                    data = data.droplevel(0, axis=1)
                results[symbol] = data
        else:
            # Multiple symbols case - handle different data structures
            logger.debug(f"Batch data structure: columns={data.columns}, shape={data.shape}")

            # Check if data has MultiIndex columns (expected for multiple symbols)
            if isinstance(data.columns, pd.MultiIndex):
                # Standard case: MultiIndex with ticker names at level 0
                for symbol in symbols:
                    yahoo_symbol = f"{symbol}.NS"
                    try:
                        # Check if symbol exists in the data
                        if hasattr(data.columns, 'levels') and len(data.columns.levels) > 0:
                            # Check level 0 for ticker names
                            if yahoo_symbol in data.columns.levels[0]:
                                symbol_data = data[yahoo_symbol]
                                if not symbol_data.empty and not symbol_data.isna().all().all():
                                    results[symbol] = symbol_data
                                    logger.debug(f"Successfully extracted data for {symbol}")
                                else:
                                    logger.warning(f"No valid data for {symbol}")
                            else:
                                logger.warning(f"Symbol {symbol} not found in batch response")
                        else:
                            # Try alternative column access
                            try:
                                symbol_data = data[yahoo_symbol]
                                if not symbol_data.empty and not symbol_data.isna().all().all():
                                    results[symbol] = symbol_data
                                    logger.debug(f"Successfully extracted data for {symbol} (alternative method)")
                                else:
                                    logger.warning(f"No valid data for {symbol}")
                            except KeyError:
                                logger.warning(f"Symbol {symbol} not found in batch response")
                    except Exception as e:
                        logger.warning(f"Error processing {symbol} from batch: {e}")
                        logger.debug(f"Available columns: {list(data.columns) if hasattr(data.columns, '__iter__') else 'Unknown'}")
            else:
                # Fallback: if not MultiIndex, might be a single symbol or different structure
                logger.warning(f"Unexpected data structure in batch download. Columns: {data.columns}")
                logger.warning("Falling back to individual downloads for this batch")

                # Fallback to individual downloads for this batch
                for symbol in symbols:
                    try:
                        df = download_single_symbol_yfinance(symbol, timeframe, start_date, end_date)
                        if df is not None:
                            results[symbol] = df
                    except Exception as e:
                        logger.warning(f"Individual fallback failed for {symbol}: {e}")

        logger.info(f"Batch download completed: {len(results)}/{len(symbols)} symbols successful")
        return results

    except Exception as e:
        logger.error(f"Batch download failed: {e}")
        return {}

class SmartAPIClient:
    """SmartAPI client for data download"""

    def __init__(self):
        self.smart_api = None
        self.authenticated = False
        self.last_api_call = 0  # Track last API call time for rate limiting

    def authenticate(self) -> bool:
        """Authenticate with SmartAPI"""
        if not SMARTAPI_AVAILABLE:
            logger.warning("SmartAPI not available - install SmartApi package")
            return False

        try:
            config = SMARTAPI_CONFIG
            if not all([config['api_key'], config['username'], config['password'], config['totp_token']]):
                logger.error("SmartAPI credentials not found in environment variables")
                logger.error("Please check .env file has: ANGEL_API_KEY, ANGEL_CLIENT_ID, ANGEL_CLIENT_PIN, ANGEL_TOTP_KEY")
                return False

            # Initialize SmartConnect
            self.smart_api = SmartConnect(api_key=config['api_key'])

            # Generate TOTP
            totp = pyotp.TOTP(config['totp_token']).now()

            # Generate session
            data = self.smart_api.generateSession(config['username'], config['password'], totp)

            if not data or not data.get('status'):
                logger.error(f"SmartAPI authentication failed: {data}")
                return False

            # Store tokens for session management
            self.auth_token = data['data']['jwtToken']
            self.refresh_token = data['data']['refreshToken']

            # Get feed token
            self.feed_token = self.smart_api.getfeedToken()

            self.authenticated = True
            logger.info("✅ SmartAPI authentication successful")
            logger.debug(f"Auth token: {self.auth_token[:20]}...")
            logger.debug(f"Feed token: {self.feed_token[:20]}...")
            return True

        except Exception as e:
            logger.error(f"SmartAPI authentication error: {e}")
            return False

    def get_historical_data(self, symbol: str, timeframe: str, start_date: datetime, end_date: datetime) -> Optional[List[Dict]]:
        """Get historical data from SmartAPI"""
        if not self.authenticated:
            return None

        try:
            # Get SmartAPI interval
            if timeframe not in TIMEFRAME_MAPPINGS:
                logger.error(f"Unsupported timeframe: {timeframe}")
                return None

            _, smartapi_interval, _, _ = TIMEFRAME_MAPPINGS[timeframe]

            # Prepare historical data request
            historic_param = {
                "exchange": "NSE",
                "symboltoken": self._get_symbol_token(symbol),
                "interval": smartapi_interval,
                "fromdate": start_date.strftime("%Y-%m-%d %H:%M"),
                "todate": end_date.strftime("%Y-%m-%d %H:%M")
            }

            # Rate limiting: Wait at least 1 second between API calls
            current_time = time.time()
            time_since_last_call = current_time - self.last_api_call
            if time_since_last_call < 1.0:
                sleep_time = 1.0 - time_since_last_call
                logger.debug(f"Rate limiting: sleeping for {sleep_time:.2f} seconds")
                time.sleep(sleep_time)

            # Get historical data
            logger.debug(f"Making SmartAPI call for {symbol} with params: {historic_param}")
            response = self.smart_api.getCandleData(historic_param)
            self.last_api_call = time.time()

            if not response or not response.get('status') or not response.get('data'):
                # Log detailed error information
                if response:
                    error_msg = response.get('message', 'Unknown error')
                    error_code = response.get('errorcode', 'No error code')
                    logger.warning(f"SmartAPI error for {symbol}: {error_msg} (Code: {error_code})")
                    logger.debug(f"SmartAPI request params: {historic_param}")
                    logger.debug(f"SmartAPI response: {response}")
                else:
                    logger.warning(f"No response from SmartAPI for {symbol}")
                return None

            # Convert SmartAPI data format to our format with enhanced volume validation
            candles = []
            ist_tz = pytz.timezone('Asia/Kolkata')
            zero_volume_count = 0
            total_volume = 0

            for candle_data in response['data']:
                # SmartAPI format: [timestamp, open, high, low, close, volume]
                timestamp_str = candle_data[0]
                timestamp = datetime.strptime(timestamp_str, "%Y-%m-%dT%H:%M:%S%z")

                # Convert to IST and make timezone-naive
                ist_timestamp = timestamp.astimezone(ist_tz).replace(tzinfo=None)

                # Enhanced volume validation for SmartAPI data
                try:
                    volume = int(float(candle_data[5])) if candle_data[5] is not None else 0
                    volume = max(0, volume)  # Ensure non-negative
                    if volume == 0:
                        zero_volume_count += 1
                    total_volume += volume
                except (ValueError, TypeError, IndexError):
                    logger.warning(f"Invalid volume data from SmartAPI at {ist_timestamp}: {candle_data[5] if len(candle_data) > 5 else 'missing'}")
                    volume = 0
                    zero_volume_count += 1

                candle = {
                    'timestamp': ist_timestamp,
                    'open': float(candle_data[1]),
                    'high': float(candle_data[2]),
                    'low': float(candle_data[3]),
                    'close': float(candle_data[4]),
                    'volume': volume
                }
                candles.append(candle)

            # Log SmartAPI volume data quality
            if candles:
                avg_volume = total_volume / len(candles) if len(candles) > 0 else 0
                zero_volume_percentage = (zero_volume_count / len(candles)) * 100
                logger.info(f"SmartAPI volume data for {symbol}: Total={total_volume:,.0f}, Average={avg_volume:,.0f} per {timeframe} candle")
                if zero_volume_percentage > 10:
                    logger.warning(f"SmartAPI volume quality concern: {zero_volume_count}/{len(candles)} candles ({zero_volume_percentage:.1f}%) have zero volume")

            return candles

        except Exception as e:
            logger.error(f"Error getting SmartAPI data for {symbol}: {e}")
            return None

    def _get_symbol_token(self, symbol: str) -> str:
        """Get symbol token for SmartAPI using proper token mapping"""
        try:
            # Try to use the centralized mapping service if available
            try:
                # Import the centralized mapping client
                import sys
                import os

                # Add python_orders path for centralized mapping
                current_dir = os.path.dirname(os.path.abspath(__file__))
                # Go up two levels: python/downloaders -> python -> root
                root_dir = os.path.dirname(os.path.dirname(current_dir))
                python_orders_path = os.path.join(root_dir, 'python_orders')

                if python_orders_path not in sys.path:
                    sys.path.insert(0, python_orders_path)

                from services.centralized_mapping_client import CentralizedMappingClient

                # Initialize mapping client
                mapping_client = CentralizedMappingClient()

                # Get token for equity symbol
                token_info = mapping_client.get_smartapi_token(symbol, 'EQ')
                if token_info:
                    token, exchange = token_info
                    logger.info(f"Found token for {symbol}: {token} on {exchange}")
                    return token

            except ImportError:
                logger.warning("Centralized mapping service not available, using fallback")
            except Exception as e:
                logger.warning(f"Error using centralized mapping: {e}, using fallback")

            # Fallback: Try to get token from SmartAPI master data directly
            try:
                import requests
                import pandas as pd

                url = 'https://margincalculator.angelbroking.com/OpenAPI_File/files/OpenAPIScripMaster.json'
                response = requests.get(url, timeout=10)

                if response.status_code == 200:
                    data = response.json()
                    token_df = pd.DataFrame.from_dict(data)

                    # Look for equity symbol with -EQ suffix
                    equity_symbol = f"{symbol}-EQ"
                    matches = token_df[
                        (token_df['symbol'] == equity_symbol) &
                        (token_df['exch_seg'] == 'NSE')
                    ]

                    if not matches.empty:
                        token = str(matches.iloc[0]['token'])
                        logger.info(f"Found token for {symbol} from master data: {token}")
                        return token

            except Exception as e:
                logger.warning(f"Error fetching master data: {e}")

            # Final fallback: return symbol with -EQ suffix (this will likely fail but follows correct format)
            logger.warning(f"No token found for {symbol}, using symbol with -EQ suffix as fallback")
            return f"{symbol}-EQ"

        except Exception as e:
            logger.error(f"Error getting token for {symbol}: {e}")
            return symbol

def connect_to_mongodb():
    """Connect to MongoDB"""
    try:
        client = MongoClient(MONGO_URL)
        db = client[DB_NAME]
        logger.info(f"Connected to MongoDB: {MONGO_URL}, Database: {DB_NAME}")
        return db
    except Exception as e:
        logger.error(f"Error connecting to MongoDB: {str(e)}")
        return None

def get_symbol_mapping_service():
    """Initialize symbol mapping service"""
    if not SYMBOL_MAPPING_AVAILABLE:
        return None

    try:
        mapping_service = SymbolMappingService(logger=logger)
        # Check if the service has a connection by trying to access the database
        if hasattr(mapping_service, 'db') and mapping_service.db is not None:
            logger.info("Connected to symbol mapping service")
            return mapping_service
        else:
            logger.warning("Symbol mapping service not connected")
            return None
    except Exception as e:
        logger.warning(f"Error initializing symbol mapping service: {e}")
        return None

def get_stocks_by_category(category: str, mapping_service=None, force_fallback: bool = False) -> List[str]:
    """Get stocks by category from MongoDB or fallback lists"""
    logger.info(f"🔍 get_stocks_by_category called for {category}, force_fallback={force_fallback}")
    logger.info(f"🔍 mapping_service: {mapping_service}")
    logger.info(f"🔍 mapping_service has db: {hasattr(mapping_service, 'db') if mapping_service else 'N/A'}")
    logger.info(f"🔍 mapping_service.db is not None: {mapping_service.db is not None if mapping_service and hasattr(mapping_service, 'db') else 'N/A'}")

    if not force_fallback and mapping_service and hasattr(mapping_service, 'db') and mapping_service.db is not None:
        try:
            logger.info(f"🔍 Calling mapping_service.get_stocks_by_category({category})")
            stocks = mapping_service.get_stocks_by_category(category)
            logger.info(f"🔍 MongoDB query returned {len(stocks)} stocks for {category}")

            if stocks:
                # Check if we have a reasonable number of stocks for each category
                expected_counts = {
                    'NIFTY50': 45,      # Allow some variance
                    'NIFTY100': 90,     # Allow some variance
                    'NIFTY200': 180,    # Allow some variance
                    'NIFTY500': 450,    # Allow some variance
                    'NIFTYFNO': 180     # Allow some variance
                }

                expected_min = expected_counts.get(category, 0)
                logger.info(f"🔍 Expected minimum for {category}: {expected_min}, actual: {len(stocks)}")

                if expected_min > 0 and len(stocks) < expected_min:
                    logger.warning(f"MongoDB returned only {len(stocks)} stocks for {category} (expected ~{expected_min}+), using fallback list instead")
                    fallback_stocks = FALLBACK_STOCK_LISTS.get(category, [])
                    if fallback_stocks:
                        logger.info(f"Using fallback list for {category}: {len(fallback_stocks)} stocks")
                        return fallback_stocks

                logger.info(f"✅ Retrieved {len(stocks)} stocks from {category} via MongoDB")
                return stocks
            else:
                logger.warning(f"⚠️ MongoDB returned empty list for {category}")
        except Exception as e:
            logger.warning(f"❌ Error getting {category} from MongoDB: {e}")
            import traceback
            logger.warning(f"❌ Traceback: {traceback.format_exc()}")
    else:
        logger.info(f"🔍 Skipping MongoDB query: force_fallback={force_fallback}, mapping_service={mapping_service is not None}, has_db={hasattr(mapping_service, 'db') if mapping_service else False}")

    # Fallback to hardcoded lists
    fallback_stocks = FALLBACK_STOCK_LISTS.get(category, [])
    if fallback_stocks:
        logger.info(f"Using fallback list for {category}: {len(fallback_stocks)} stocks")
        return fallback_stocks

    logger.error(f"No stocks found for category: {category}")
    return []

def display_menu():
    """Display the interactive menu"""
    print("\n" + "="*80)
    print("🚀 INTERACTIVE HISTORICAL DATA DOWNLOADER")
    print("="*80)
    print("📈 Select stock category to download historical data:")
    print()
    print("1. 📊 F&O Enabled Stocks (NIFTYFNO)")
    print("2. 🏆 Nifty 50 Stocks")
    print("3. 📈 Nifty 100 Stocks")
    print("4. 📊 Nifty 200 Stocks")
    print("5. 🌟 Nifty 500 Stocks")
    print("6. ✏️  Custom Symbol List")
    print("7. 📋 Show Available Categories")
    print("8. ❌ Exit")
    print()
    print("="*80)

def get_timeframe_selection() -> str:
    """Get timeframe selection from user - Optimized for 15-minute candles"""
    print("\n⏰ Select timeframe (Optimized for 15-minute candles with volume):")
    print("1. 5 minutes (5m)")
    print("2. 15 minutes (15m) ⭐ RECOMMENDED")
    print("3. 1 hour (1hr)")
    print("4. Daily (D)")
    print("5. Weekly (W)")

    while True:
        choice = input("Enter choice (1-5) [default: 2 (15m)]: ").strip()

        timeframe_map = {
            '1': '5m',
            '2': '15m',
            '3': '1hr',
            '4': 'D',
            '5': 'W',
            '': '15m'  # default changed to 15m
        }

        if choice in timeframe_map:
            selected_timeframe = timeframe_map[choice]
            if selected_timeframe == '15m':
                print(f"✅ Selected timeframe: {selected_timeframe} (Recommended for volume analysis)")
            else:
                print(f"✅ Selected timeframe: {selected_timeframe}")
            return selected_timeframe
        else:
            print("❌ Invalid choice. Please try again.")

def get_data_source_selection() -> str:
    """Get data source selection from user"""
    print("\n📡 Select data source:")
    print("1. Yahoo Finance (yfinance)")
    if SMARTAPI_AVAILABLE:
        print("2. SmartAPI (Angel One)")
    else:
        print("2. SmartAPI (Angel One) - ❌ Not Available")

    while True:
        choice = input("Enter choice (1-2) [default: 1]: ").strip()

        if choice == '2' and SMARTAPI_AVAILABLE:
            print("✅ Selected data source: SmartAPI (Angel One)")
            return 'smartapi'
        elif choice == '2' and not SMARTAPI_AVAILABLE:
            print("❌ SmartAPI not available. Please install SmartApi package.")
            continue
        elif choice in ['1', '']:
            print("✅ Selected data source: Yahoo Finance")
            return 'yfinance'
        else:
            print("❌ Invalid choice. Please try again.")

def get_dynamic_period(timeframe: str) -> int:
    """Get dynamic period based on timeframe with user override option"""
    if timeframe not in TIMEFRAME_MAPPINGS:
        return 30  # default

    _, _, _, default_days = TIMEFRAME_MAPPINGS[timeframe]

    print(f"\n📅 Default period for {timeframe}: {default_days} days")
    print("Options:")
    print(f"1. Use default ({default_days} days)")
    print("2. Custom period")

    choice = input("Enter choice (1-2) [default: 1]: ").strip()

    if choice == '2':
        # Check if this is Daily or Weekly timeframe for unlimited input
        if timeframe in ['D', 'W']:
            print(f"\n💡 Note: For {timeframe} timeframe, you can enter any number of days.")
            print(f"   Recommended: {default_days} days (default)")
            print(f"   Data availability depends on the data source.")

            while True:
                try:
                    custom_days = int(input(f"Enter number of days (minimum 1): "))
                    if custom_days >= 1:
                        if custom_days > default_days * 3:  # Warning for very large values
                            print(f"⚠️  Warning: Requesting {custom_days} days is significantly more than recommended ({default_days} days)")
                            print(f"   This may take longer to download and process.")
                            confirm = input("Continue? (y/n): ").strip().lower()
                            if confirm not in ['y', 'yes']:
                                continue
                        print(f"✅ Selected period: {custom_days} days")
                        return custom_days
                    else:
                        print(f"❌ Please enter a value of 1 or greater")
                except ValueError:
                    print("❌ Please enter a valid number")
        else:
            # For short timeframes (5m, 15m, 1hr), keep the original limit
            while True:
                try:
                    custom_days = int(input(f"Enter number of days (1-{default_days*2}): "))
                    if 1 <= custom_days <= default_days * 2:
                        print(f"✅ Selected period: {custom_days} days")
                        return custom_days
                    else:
                        print(f"❌ Please enter a value between 1 and {default_days*2}")
                except ValueError:
                    print("❌ Please enter a valid number")
    else:
        print(f"✅ Using default period: {default_days} days")
        return default_days

def get_custom_symbols() -> List[str]:
    """Get custom symbol list from user input"""
    print("\n📝 Enter stock symbols (comma-separated):")
    print("Example: RELIANCE,TCS,HDFCBANK,INFY")

    while True:
        user_input = input("Symbols: ").strip()
        if not user_input:
            print("❌ Please enter at least one symbol")
            continue

        # Parse symbols
        symbols = [symbol.strip().upper() for symbol in user_input.split(',')]
        symbols = [symbol for symbol in symbols if symbol]  # Remove empty strings

        if symbols:
            print(f"✅ Selected {len(symbols)} symbols: {', '.join(symbols)}")
            return symbols
        else:
            print("❌ No valid symbols entered. Please try again.")

def validate_yfinance_date_range(start_date: datetime, end_date: datetime, timeframe: str) -> tuple:
    """Validate and adjust date range for yfinance limits"""
    # yfinance limits for different timeframes
    yfinance_limits = {
        '5m': 60,    # 60 days for 5-minute data
        '15m': 60,   # 60 days for 15-minute data
        '1h': 730,   # 2 years for hourly data
        '1d': 3650,  # 10 years for daily data
        '1wk': 3650  # 10 years for weekly data
    }

    # Get yfinance interval
    if timeframe in TIMEFRAME_MAPPINGS:
        yfinance_interval, _, _, _ = TIMEFRAME_MAPPINGS[timeframe]
        max_days = yfinance_limits.get(yfinance_interval, 60)
    else:
        max_days = 60  # default conservative limit

    # Calculate actual days requested
    actual_days = (end_date - start_date).days

    # If exceeds limit, adjust start_date to be within limit
    if actual_days > max_days:
        # Use a slightly smaller limit to be safe (5 days buffer)
        safe_days = max_days - 5
        adjusted_start_date = end_date - timedelta(days=safe_days)
        logger.warning(f"Date range adjusted for yfinance limits: {actual_days} days -> {safe_days} days")
        logger.warning(f"Original: {start_date} to {end_date}")
        logger.warning(f"Adjusted: {adjusted_start_date} to {end_date}")
        return adjusted_start_date, end_date

    return start_date, end_date

def get_date_range_from_days(days: int) -> tuple:
    """Get date range from number of days"""
    end_date = datetime.now()
    start_date = end_date - timedelta(days=days)

    # Ensure dates are timezone-naive
    if hasattr(end_date, 'tzinfo') and end_date.tzinfo is not None:
        end_date = end_date.replace(tzinfo=None)
    if hasattr(start_date, 'tzinfo') and start_date.tzinfo is not None:
        start_date = start_date.replace(tzinfo=None)

    return start_date, end_date

def show_available_categories(mapping_service=None):
    """Show available stock categories"""
    print("\nAvailable Stock Categories:")
    print("="*50)

    if mapping_service and hasattr(mapping_service, 'db') and mapping_service.db is not None:
        try:
            stats = mapping_service.get_category_stats()
            if stats and 'category_breakdown' in stats:
                for category_info in stats['category_breakdown']:
                    category = category_info['_id']
                    count = category_info['count']
                    print(f"{category}: {count} stocks")
            else:
                print("No category statistics available")
        except Exception as e:
            logger.warning(f"Error getting category stats: {e}")
            print("Error retrieving category information")
    else:
        print("Available categories (fallback):")
        for category, stocks in FALLBACK_STOCK_LISTS.items():
            print(f"{category}: {len(stocks)} stocks")

    print("="*50)

def convert_candles_to_dict(df):
    """
    Convert DataFrame to list of dictionaries for MongoDB storage

    Handles timezone conversion from UTC (yfinance default) to IST
    and stores as timezone-naive IST datetime objects
    Enhanced volume data validation for better data quality
    """
    candles = []
    zero_volume_count = 0
    total_candles = len(df)

    # Set up timezone objects
    utc_tz = pytz.UTC
    ist_tz = pytz.timezone('Asia/Kolkata')

    for index, row in df.iterrows():
        # Get timestamp from DataFrame index
        timestamp = index.to_pydatetime()

        # Convert from UTC to IST
        if timestamp.tzinfo is None:
            # If timezone-naive, assume it's UTC (yfinance default)
            utc_timestamp = utc_tz.localize(timestamp)
        else:
            # If timezone-aware, convert to UTC first
            utc_timestamp = timestamp.astimezone(utc_tz)

        # Convert to IST and make timezone-naive
        ist_timestamp = utc_timestamp.astimezone(ist_tz)
        naive_ist_timestamp = ist_timestamp.replace(tzinfo=None)

        # Extract and validate volume data
        volume_raw = row['Volume'].iloc[0] if hasattr(row['Volume'], 'iloc') else row['Volume']

        # Handle volume validation and conversion
        try:
            volume = int(float(volume_raw)) if volume_raw is not None else 0
            # Ensure volume is non-negative
            volume = max(0, volume)
            if volume == 0:
                zero_volume_count += 1
        except (ValueError, TypeError):
            logger.warning(f"Invalid volume data at {naive_ist_timestamp}: {volume_raw}, setting to 0")
            volume = 0
            zero_volume_count += 1

        candle = {
            'timestamp': naive_ist_timestamp,
            'open': float(row['Open'].iloc[0] if hasattr(row['Open'], 'iloc') else row['Open']),
            'high': float(row['High'].iloc[0] if hasattr(row['High'], 'iloc') else row['High']),
            'low': float(row['Low'].iloc[0] if hasattr(row['Low'], 'iloc') else row['Low']),
            'close': float(row['Close'].iloc[0] if hasattr(row['Close'], 'iloc') else row['Close']),
            'volume': volume
        }
        candles.append(candle)

    # Log volume data quality
    if total_candles > 0:
        zero_volume_percentage = (zero_volume_count / total_candles) * 100
        if zero_volume_percentage > 10:  # Warn if more than 10% of candles have zero volume
            logger.warning(f"Volume data quality concern: {zero_volume_count}/{total_candles} candles ({zero_volume_percentage:.1f}%) have zero volume")
        else:
            logger.info(f"Volume data quality: {zero_volume_count}/{total_candles} candles ({zero_volume_percentage:.1f}%) have zero volume")

    return candles

def save_to_mongodb(db, symbol, timeframe, candles):
    """Save candles to MongoDB"""
    try:
        # Check if document exists
        existing_doc = db[COLLECTION_NAME].find_one({
            'symbol': symbol,
            'timeframe': timeframe
        })

        if existing_doc:
            # Update existing document
            now = datetime.now()
            # Ensure now is timezone-naive
            if hasattr(now, 'tzinfo') and now.tzinfo is not None:
                now = now.replace(tzinfo=None)

            db[COLLECTION_NAME].update_one(
                {'symbol': symbol, 'timeframe': timeframe},
                {
                    '$set': {'candles': candles, 'last_updated': now}
                }
            )
            logger.info(f"Updated {symbol} ({timeframe}) with {len(candles)} candles")
            return True
        else:
            # Insert new document
            now = datetime.now()
            # Ensure now is timezone-naive
            if hasattr(now, 'tzinfo') and now.tzinfo is not None:
                now = now.replace(tzinfo=None)

            db[COLLECTION_NAME].insert_one({
                'symbol': symbol,
                'timeframe': timeframe,
                'candles': candles,
                'last_updated': now
            })
            logger.info(f"Inserted {symbol} ({timeframe}) with {len(candles)} candles")
            return True
    except Exception as e:
        logger.error(f"Error saving {symbol} ({timeframe}) to MongoDB: {str(e)}")
        return False

def download_and_save_data_batch(db, symbols: List[str], timeframe: str, start_date: datetime, end_date: datetime,
                               data_source='yfinance', smartapi_client=None, force_historical=False) -> Dict[str, bool]:
    """Download data for multiple symbols efficiently using batch processing"""
    results = {}

    if data_source == 'yfinance':
        # Use batch download for yfinance (most efficient)
        try:
            batch_data = download_batch_yfinance(symbols, timeframe, start_date, end_date)

            # Check batch success rate
            batch_success_count = len(batch_data)
            batch_success_rate = batch_success_count / len(symbols) if symbols else 0

            logger.info(f"Batch download success rate: {batch_success_rate:.1%} ({batch_success_count}/{len(symbols)})")

            # If batch success rate is very low, fall back to individual downloads
            if batch_success_rate < 0.3:  # Less than 30% success
                logger.warning(f"Batch download success rate too low ({batch_success_rate:.1%}), falling back to individual downloads")
                for symbol in symbols:
                    results[symbol] = download_and_save_data_single(
                        db, symbol, timeframe, start_date, end_date, data_source, smartapi_client, force_historical
                    )
                return results

            # Process successful batch downloads
            for symbol in symbols:
                if symbol in batch_data:
                    df = batch_data[symbol]
                    success = process_and_save_dataframe(db, symbol, timeframe, df, force_historical)
                    results[symbol] = success
                else:
                    logger.warning(f"No data received for {symbol} in batch, trying individual download")
                    # Try individual download for failed symbols
                    results[symbol] = download_and_save_data_single(
                        db, symbol, timeframe, start_date, end_date, data_source, smartapi_client, force_historical
                    )

        except Exception as e:
            logger.error(f"Batch download failed: {e}")
            logger.info("Falling back to individual downloads for entire batch")
            # Fallback to individual downloads
            for symbol in symbols:
                results[symbol] = download_and_save_data_single(
                    db, symbol, timeframe, start_date, end_date, data_source, smartapi_client, force_historical
                )
    else:
        # For SmartAPI, use individual downloads with threading
        with ThreadPoolExecutor(max_workers=MAX_CONCURRENT_DOWNLOADS) as executor:
            future_to_symbol = {
                executor.submit(download_and_save_data_single, db, symbol, timeframe, start_date, end_date, data_source, smartapi_client, force_historical): symbol
                for symbol in symbols
            }

            for future in as_completed(future_to_symbol):
                symbol = future_to_symbol[future]
                try:
                    results[symbol] = future.result()
                except Exception as e:
                    logger.error(f"Error downloading {symbol}: {e}")
                    results[symbol] = False

    return results

def process_and_save_dataframe(db, symbol: str, timeframe: str, df: pd.DataFrame, force_historical: bool = False) -> bool:
    """Process DataFrame and save to MongoDB"""
    try:
        if df.empty:
            logger.warning(f"Empty DataFrame for {symbol}")
            return False

        # Get database timeframe format
        _, _, db_timeframe, _ = TIMEFRAME_MAPPINGS[timeframe]

        # Log volume data availability
        if 'Volume' in df.columns:
            total_volume = df['Volume'].sum()
            avg_volume = df['Volume'].mean()
            logger.info(f"Volume data for {symbol}: Total={total_volume:,.0f}, Average={avg_volume:,.0f} per {timeframe} candle")
        else:
            logger.warning(f"No volume column found for {symbol}")

        # Convert DataFrame to list of dictionaries with timezone conversion
        new_candles = convert_candles_to_dict(df)

        if not new_candles:
            logger.warning(f"No candles generated for {symbol}")
            return False

        # Handle existing data
        existing_doc = db[COLLECTION_NAME].find_one({
            'symbol': symbol,
            'timeframe': db_timeframe
        })

        if force_historical:
            # Replace existing data
            success = save_to_mongodb(db, symbol, db_timeframe, new_candles)
        elif existing_doc and 'candles' in existing_doc and existing_doc['candles']:
            # Merge with existing data
            existing_candles = existing_doc['candles']
            existing_timestamps = {candle['timestamp'].isoformat(): True for candle in existing_candles}
            unique_new_candles = [candle for candle in new_candles
                                if candle['timestamp'].isoformat() not in existing_timestamps]

            if unique_new_candles:
                combined_candles = existing_candles + unique_new_candles
                combined_candles = sorted(combined_candles, key=lambda x: x['timestamp'])
                success = save_to_mongodb(db, symbol, db_timeframe, combined_candles)
                logger.info(f"Added {len(unique_new_candles)} new candles to {symbol}")
            else:
                logger.info(f"No new candles to add for {symbol}")
                success = True
        else:
            # Save new data
            success = save_to_mongodb(db, symbol, db_timeframe, new_candles)

        return success

    except Exception as e:
        logger.error(f"Error processing data for {symbol}: {e}")
        return False

def download_and_save_data_single(db, symbol, timeframe, start_date, end_date, data_source='yfinance', smartapi_client=None, force_historical=False):
    """Download data for a single symbol from Yahoo Finance or SmartAPI and save to MongoDB (legacy function)"""
    try:
        # Get database timeframe format
        if timeframe not in TIMEFRAME_MAPPINGS:
            logger.error(f"Unsupported timeframe: {timeframe}")
            return False

        yfinance_interval, smartapi_interval, db_timeframe, _ = TIMEFRAME_MAPPINGS[timeframe]

        # Check if document exists in MongoDB
        existing_doc = db[COLLECTION_NAME].find_one({
            'symbol': symbol,
            'timeframe': db_timeframe
        })

        # Determine the actual start date based on existing data and force_historical flag
        actual_start_date = start_date

        if force_historical:
            # For historical downloads, always use the requested start_date
            logger.info(f"Force historical mode: Downloading {timeframe} data for {symbol} from {actual_start_date} to {end_date}...")
        elif existing_doc and 'candles' in existing_doc and existing_doc['candles']:
            # Get the latest timestamp in the database
            latest_candles = sorted(existing_doc['candles'], key=lambda x: x['timestamp'], reverse=True)
            if latest_candles:
                latest_timestamp = latest_candles[0]['timestamp']
                # Set start date to one interval after the latest timestamp
                actual_start_date = latest_timestamp + timedelta(minutes=5)

                # Ensure end_date is timezone-naive for consistent comparison
                naive_end_date = end_date
                if hasattr(end_date, 'tzinfo') and end_date.tzinfo is not None:
                    naive_end_date = end_date.replace(tzinfo=None)

                # If we already have data up to the end date, no need to download
                if actual_start_date >= naive_end_date:
                    logger.info(f"Data for {symbol} ({db_timeframe}) is already up to date")
                    return True

                logger.info(f"Incremental update: Downloading {timeframe} data for {symbol} from {actual_start_date} to {end_date}...")
            else:
                logger.info(f"Downloading {timeframe} data for {symbol} from {actual_start_date} to {end_date}...")
        else:
            logger.info(f"No existing data found. Downloading {timeframe} data for {symbol} from {actual_start_date} to {end_date}...")

        # Download data based on selected source
        if data_source == 'smartapi' and smartapi_client:
            logger.info(f"Downloading {timeframe} data for {symbol} from SmartAPI from {actual_start_date} to {end_date}...")
            new_candles = smartapi_client.get_historical_data(symbol, timeframe, actual_start_date, end_date)

            if not new_candles:
                logger.warning(f"No SmartAPI data available for {symbol} ({timeframe})")
                return False

        else:
            # Use Yahoo Finance with rate limiting
            df = download_single_symbol_yfinance(symbol, timeframe, actual_start_date, end_date)

            if df is None or df.empty:
                logger.warning(f"No yfinance data available for {symbol} ({timeframe})")
                return False

            # Convert DataFrame to list of dictionaries with timezone conversion
            new_candles = convert_candles_to_dict(df)

        if not new_candles:
            logger.info(f"No new candles to add for {symbol} ({db_timeframe})")
            return True

        # Handle data merging based on force_historical flag
        if force_historical:
            # For historical downloads, replace existing data with new data
            logger.info(f"Force historical mode: Replacing existing data with {len(new_candles)} new candles")
            success = save_to_mongodb(db, symbol, db_timeframe, new_candles)
        elif existing_doc and 'candles' in existing_doc and existing_doc['candles']:
            # Get existing candles
            existing_candles = existing_doc['candles']

            # Create a dictionary of existing timestamps for quick lookup
            existing_timestamps = {candle['timestamp'].isoformat(): True for candle in existing_candles}

            # Only add candles that don't already exist
            unique_new_candles = [candle for candle in new_candles
                                if candle['timestamp'].isoformat() not in existing_timestamps]

            # Combine existing and new candles
            combined_candles = existing_candles + unique_new_candles

            # Sort by timestamp
            combined_candles = sorted(combined_candles, key=lambda x: x['timestamp'])

            logger.info(f"Adding {len(unique_new_candles)} new candles to existing {len(existing_candles)} candles")

            # Save to MongoDB
            success = save_to_mongodb(db, symbol, db_timeframe, combined_candles)
        else:
            # Save new candles to MongoDB
            success = save_to_mongodb(db, symbol, db_timeframe, new_candles)

        if success:
            logger.info(f"Successfully saved candles for {symbol} ({db_timeframe})")

        return success
    except Exception as e:
        logger.error(f"Error downloading data for {symbol} ({timeframe}): {str(e)}")
        return False

def download_symbols_optimized(db, symbols: List[str], timeframe: str, period_days: int,
                              data_source: str = 'yfinance', smartapi_client=None, force_historical: bool = False):
    """Optimized download function using batch processing and threading"""
    start_time = time.time()
    start_date, end_date = get_date_range_from_days(period_days)

    logger.info("🚀 STARTING OPTIMIZED DOWNLOAD")
    logger.info("="*60)
    logger.info(f"📊 Symbols: {len(symbols)}")
    logger.info(f"⏱️  Timeframe: {timeframe}")
    logger.info(f"📅 Period: {period_days} days ({start_date} to {end_date})")
    logger.info(f"🔗 Data source: {data_source}")
    logger.info(f"📦 Batch size: {BATCH_SIZE}")
    logger.info(f"🧵 Max concurrent: {MAX_CONCURRENT_DOWNLOADS}")
    logger.info(f"⏳ Rate limit delay: {RATE_LIMIT_DELAY}s")
    logger.info("="*60)

    total_symbols = len(symbols)
    successful_downloads = 0
    failed_downloads = 0

    if data_source == 'yfinance':
        # Process symbols in batches for yfinance
        for i in range(0, len(symbols), BATCH_SIZE):
            batch_symbols = symbols[i:i + BATCH_SIZE]
            batch_num = (i // BATCH_SIZE) + 1
            total_batches = (len(symbols) + BATCH_SIZE - 1) // BATCH_SIZE

            logger.info(f"Processing batch {batch_num}/{total_batches}: {len(batch_symbols)} symbols")

            try:
                batch_results = download_and_save_data_batch(
                    db, batch_symbols, timeframe, start_date, end_date,
                    data_source, smartapi_client, force_historical
                )

                # Count results
                batch_successful = sum(1 for success in batch_results.values() if success)
                batch_failed = len(batch_results) - batch_successful

                successful_downloads += batch_successful
                failed_downloads += batch_failed

                logger.info(f"Batch {batch_num} completed: {batch_successful}/{len(batch_symbols)} successful")

                # Add delay between batches to respect rate limits
                if i + BATCH_SIZE < len(symbols):
                    time.sleep(RATE_LIMIT_DELAY * 2)  # Extra delay between batches

            except Exception as e:
                logger.error(f"Batch {batch_num} failed: {e}")
                failed_downloads += len(batch_symbols)
    else:
        # For SmartAPI, use threading with smaller batches
        batch_size = min(BATCH_SIZE, MAX_CONCURRENT_DOWNLOADS)
        for i in range(0, len(symbols), batch_size):
            batch_symbols = symbols[i:i + batch_size]
            batch_num = (i // batch_size) + 1
            total_batches = (len(symbols) + batch_size - 1) // batch_size

            logger.info(f"Processing SmartAPI batch {batch_num}/{total_batches}: {len(batch_symbols)} symbols")

            batch_results = download_and_save_data_batch(
                db, batch_symbols, timeframe, start_date, end_date,
                data_source, smartapi_client, force_historical
            )

            batch_successful = sum(1 for success in batch_results.values() if success)
            batch_failed = len(batch_results) - batch_successful

            successful_downloads += batch_successful
            failed_downloads += batch_failed

            logger.info(f"SmartAPI batch {batch_num} completed: {batch_successful}/{len(batch_symbols)} successful")

    # Calculate performance metrics
    end_time = time.time()
    total_time = end_time - start_time
    symbols_per_minute = (total_symbols / total_time) * 60 if total_time > 0 else 0

    # Final summary
    logger.info("="*80)
    logger.info("🎯 OPTIMIZED DOWNLOAD SUMMARY")
    logger.info("="*80)
    logger.info(f"📊 Total symbols: {total_symbols}")
    logger.info(f"✅ Successful downloads: {successful_downloads}")
    logger.info(f"❌ Failed downloads: {failed_downloads}")
    logger.info(f"📈 Success rate: {(successful_downloads/total_symbols)*100:.1f}%")
    logger.info(f"⏱️  Total time: {total_time:.1f} seconds")
    logger.info(f"🚀 Speed: {symbols_per_minute:.1f} symbols/minute")
    logger.info(f"⚡ Avg time per symbol: {total_time/total_symbols:.2f} seconds")
    logger.info("="*80)

    return successful_downloads, failed_downloads

def cleanup_old_data(db):
    """Clean up old short-term data (daily and weekly data are preserved) - Optimized for 15-minute retention"""
    try:
        # Define timeframes that should be cleaned up and their retention periods
        # Aligned with yfinance data availability limits
        cleanup_timeframes = {
            '5min': 30,    # Keep 5-minute data for 30 days
            '15min': 60,   # Keep 15-minute data for 60 days (yfinance limit)
            '1hr': 180,    # Keep 1-hour data for 180 days
            # Daily and weekly data are NOT cleaned up - kept forever
        }

        logger.info("Cleaning up old short-term data...")

        for timeframe, retention_days in cleanup_timeframes.items():
            # Calculate cutoff date for this timeframe
            cutoff_date = datetime.now() - timedelta(days=retention_days)
            # Ensure cutoff_date is timezone-naive
            if hasattr(cutoff_date, 'tzinfo') and cutoff_date.tzinfo is not None:
                cutoff_date = cutoff_date.replace(tzinfo=None)

            logger.info(f"Cleaning up {timeframe} data older than {cutoff_date.strftime('%Y-%m-%d')} ({retention_days} days)")

            # Get all stocks with this specific timeframe
            stocks = db[COLLECTION_NAME].find({'timeframe': timeframe})

            for stock in stocks:
                symbol = stock['symbol']
                candles = stock['candles']

                # Filter out old candles, ensuring timezone-naive comparison
                new_candles = []
                for candle in candles:
                    candle_timestamp = candle['timestamp']
                    # Ensure timestamp is timezone-naive
                    if hasattr(candle_timestamp, 'tzinfo') and candle_timestamp.tzinfo is not None:
                        candle_timestamp = candle_timestamp.replace(tzinfo=None)

                    if candle_timestamp >= cutoff_date:
                        new_candles.append(candle)

                # Update stock data if candles were removed
                if len(new_candles) < len(candles):
                    db[COLLECTION_NAME].update_one(
                        {'symbol': symbol, 'timeframe': timeframe},
                        {'$set': {'candles': new_candles}}
                    )
                    logger.info(f"Removed {len(candles) - len(new_candles)} old candles for {symbol} ({timeframe})")

        # Log that daily and weekly data is preserved
        daily_count = db[COLLECTION_NAME].count_documents({'timeframe': 'daily'})
        weekly_count = db[COLLECTION_NAME].count_documents({'timeframe': 'weekly'})

        if daily_count > 0:
            logger.info(f"Preserved {daily_count} daily data documents (no cleanup)")
        if weekly_count > 0:
            logger.info(f"Preserved {weekly_count} weekly data documents (no cleanup)")

        return True
    except Exception as e:
        logger.error(f"Error cleaning up old data: {str(e)}")
        return False

def download_stocks_data(db, symbols: List[str], start_date: datetime, end_date: datetime,
                        timeframe: str = '5m', data_source: str = 'yfinance',
                        force_update: bool = False, force_historical: bool = False) -> Dict:
    """Download historical data for a list of symbols"""
    logger.info(f"Starting download for {len(symbols)} symbols")
    logger.info(f"Date range: {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")
    logger.info(f"Timeframe: {timeframe}, Data source: {data_source}")
    if force_historical:
        logger.info(f"Force historical mode enabled - will download full period regardless of existing data")

    # Initialize SmartAPI client if needed
    smartapi_client = None
    if data_source == 'smartapi':
        smartapi_client = SmartAPIClient()
        if not smartapi_client.authenticate():
            logger.error("Failed to authenticate with SmartAPI, falling back to yfinance")
            data_source = 'yfinance'

    # Handle force update by removing existing data
    if force_update:
        logger.info("Force update enabled - removing existing data for all symbols")
        if timeframe in TIMEFRAME_MAPPINGS:
            _, _, db_timeframe, _ = TIMEFRAME_MAPPINGS[timeframe]
            for symbol in symbols:
                db[COLLECTION_NAME].delete_one({'symbol': symbol, 'timeframe': db_timeframe})

    # Calculate period in days for optimized download
    period_days = (end_date - start_date).days

    # Use optimized batch download
    success_count, error_count = download_symbols_optimized(
        db, symbols, timeframe, period_days, data_source, smartapi_client, force_historical
    )

    # Calculate failed symbols (for backward compatibility)
    failed_symbols = []  # This would need to be tracked in the optimized function if needed

    # Summary
    results = {
        'total_symbols': len(symbols),
        'success': success_count,
        'errors': error_count,
        'failed_symbols': failed_symbols,
        'timeframe': timeframe,
        'data_source': data_source
    }

    return results

def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description='Interactive Historical Data Downloader with MongoDB Integration')
    parser.add_argument('--force-update', action='store_true', help='Force update all data regardless of last update')
    parser.add_argument('--force-historical', action='store_true', help='Force download full historical period regardless of existing data')
    parser.add_argument('--non-interactive', action='store_true', help='Run in non-interactive mode (for automation)')
    parser.add_argument('--category', type=str, help='Stock category for non-interactive mode (NIFTYFNO, NIFTY50, etc.)')
    parser.add_argument('--days', type=int, help='Number of days to download (auto-calculated based on timeframe if not specified)')
    parser.add_argument('--timeframe', type=str, default='15m', choices=['5m', '15m', '1hr', 'D', 'W'],
                       help='Timeframe for data download (default: 15m - optimized for volume analysis)')
    parser.add_argument('--data-source', type=str, default='yfinance', choices=['yfinance', 'smartapi'],
                       help='Data source for download (default: yfinance)')
    return parser.parse_args()

def main():
    """Main interactive function"""
    # Parse command line arguments
    args = parse_arguments()

    logger.info("Interactive Historical Data Downloader Started")
    logger.info(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # Connect to MongoDB
    db = connect_to_mongodb()
    if db is None:
        logger.error("Failed to connect to MongoDB. Exiting.")
        return False

    # Initialize symbol mapping service
    mapping_service = get_symbol_mapping_service()

    # Clean up old data first
    logger.info("Cleaning up old data...")
    cleanup_old_data(db)

    # Non-interactive mode for automation
    if args.non_interactive:
        if not args.category:
            logger.error("Category required for non-interactive mode. Use --category option.")
            return False

        logger.info(f"Running in non-interactive mode for category: {args.category}")
        logger.info(f"Timeframe: {args.timeframe}, Data source: {getattr(args, 'data_source', 'yfinance')}")

        # Get symbols for the category
        symbols = get_stocks_by_category(args.category, mapping_service)
        if not symbols:
            logger.error(f"No symbols found for category: {args.category}")
            return False

        # Calculate date range
        if args.days:
            days = args.days
        else:
            # Use default days based on timeframe
            if args.timeframe in TIMEFRAME_MAPPINGS:
                _, _, _, days = TIMEFRAME_MAPPINGS[args.timeframe]
            else:
                days = 30  # fallback

        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)

        # Download data
        data_source = getattr(args, 'data_source', 'yfinance')
        results = download_stocks_data(db, symbols, start_date, end_date, args.timeframe, data_source, args.force_update, args.force_historical)
        return results['errors'] == 0

    # Interactive mode
    while True:
        try:
            display_menu()
            choice = input("Enter your choice (1-8): ").strip()

            if choice == '8':
                print("👋 Goodbye!")
                break
            elif choice == '7':
                show_available_categories(mapping_service)
                continue
            elif choice in ['1', '2', '3', '4', '5']:
                # Map choices to categories
                category_map = {
                    '1': 'NIFTYFNO',
                    '2': 'NIFTY50',
                    '3': 'NIFTY100',
                    '4': 'NIFTY200',
                    '5': 'NIFTY500'
                }

                category = category_map[choice]
                symbols = get_stocks_by_category(category, mapping_service)

                if not symbols:
                    print(f"❌ No symbols found for {category}")
                    continue

                print(f"✅ Found {len(symbols)} symbols in {category}")

            elif choice == '6':
                symbols = get_custom_symbols()
            else:
                print("❌ Invalid choice. Please try again.")
                continue

            # Get timeframe selection
            timeframe = get_timeframe_selection()

            # Get data source selection
            data_source = get_data_source_selection()

            # Get dynamic period based on timeframe
            days = get_dynamic_period(timeframe)
            start_date, end_date = get_date_range_from_days(days)

            # Ask about historical download mode for longer periods
            force_historical = False
            if days > 30:  # For periods longer than 30 days, ask about historical mode
                print(f"\n🔄 DOWNLOAD MODE SELECTION:")
                print(f"1. 📈 Incremental Update (default) - Only download missing data")
                print(f"2. 🔄 Force Historical Download - Download full {days} days regardless of existing data")

                mode_choice = input("Enter choice (1-2) [default: 1]: ").strip()
                if mode_choice == '2':
                    force_historical = True
                    print(f"✅ Selected: Force Historical Download")
                else:
                    print(f"✅ Selected: Incremental Update")

            # Confirm download
            print(f"\n📋 Download Summary:")
            print(f"   Symbols: {len(symbols)}")
            print(f"   Timeframe: {timeframe}")
            print(f"   Data source: {data_source}")
            print(f"   Period: {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')} ({days} days)")
            print(f"   Force update: {'Yes' if args.force_update else 'No'}")
            print(f"   Force historical: {'Yes' if force_historical else 'No'}")

            confirm = input("\n🚀 Start download? (y/N): ").strip().lower()
            if confirm not in ['y', 'yes']:
                print("❌ Download cancelled.")
                continue

            # Start download
            results = download_stocks_data(db, symbols, start_date, end_date, timeframe, data_source, args.force_update, force_historical)

            # Show results
            print(f"\n✅ Download completed!")
            print(f"📊 Results: {results['success']}/{results['total_symbols']} successful")
            print(f"⏰ Timeframe: {results['timeframe']}")
            print(f"📡 Data source: {results['data_source']}")

            if results['failed_symbols']:
                print(f"⚠️ Failed: {', '.join(results['failed_symbols'])}")

            # Ask if user wants to continue
            continue_choice = input("\n🔄 Download more data? (y/N): ").strip().lower()
            if continue_choice not in ['y', 'yes']:
                break

        except KeyboardInterrupt:
            print("\n\n⚠️ Download interrupted by user")
            break
        except Exception as e:
            logger.error(f"❌ Unexpected error: {e}")
            print(f"❌ Error: {e}")
            continue

    logger.info("Interactive Historical Data Downloader finished")
    return True

if __name__ == "__main__":
    main()
