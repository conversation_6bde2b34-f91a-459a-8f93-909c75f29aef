# 🚀 Live Stock Selection and Strategy Assignment System

A comprehensive ML-driven stock selection and strategy assignment system that extends the existing ML workflow for live trading applications. This system provides intelligent premarket preparation by selecting optimal stocks and assigning appropriate trading strategies based on machine learning predictions and market analysis.

## 🎯 Overview

The Live Stock Selection System consists of 5 specialized agents that work together to:

1. **Download and validate historical data** for stock analysis
2. **Calculate comprehensive technical indicators** and market features  
3. **Generate ML predictions** for returns, risk, and strategy suitability
4. **Score and select top stocks** based on multiple criteria
5. **Assign optimal trading strategies** with confidence scores

## 🏗️ Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                Live Stock Selection Workflow                │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │ Data Management │  │ Feature Engine  │  │ ML Prediction   │ │
│  │ Agent           │→ │ Agent           │→ │ Agent           │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
│           │                     │                     │        │
│           ▼                     ▼                     ▼        │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │ Stock Selection │  │ Strategy        │  │ Report          │ │
│  │ Agent           │  │ Assignment      │  │ Generation      │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

## 📦 Components

### 1. Data Management Agent (`live_data_management_agent.py`)
- Downloads 25 days of historical OHLCV data
- Handles API rate limits and retries
- Validates data quality with comprehensive metrics
- Saves data in efficient Parquet format
- Provides fallback to demo data for testing

**Key Features:**
- ✅ Rate limiting (100 req/min, 2 req/sec)
- ✅ Data quality validation (completeness, outliers, coverage)
- ✅ Robust error handling with fallback mechanisms
- ✅ Efficient data storage with compression

### 2. Feature Engineering Agent (`live_feature_engineering_agent.py`)
- Calculates 50+ technical indicators
- Generates market condition features
- Creates 5-day forward return targets
- Handles missing data and edge cases

**Technical Indicators:**
- **Volatility**: Rolling std, ATR, Garman-Klass
- **Momentum**: RSI (14-day), ROC, Williams %R
- **Trend**: MACD, EMA (5, 20, 50), ADX
- **Mean Reversion**: Bollinger Bands, %B
- **Volume**: Volume SMA, Volume ratio, VWAP
- **Oscillators**: Stochastic %K/%D

**Market Conditions:**
- Trend strength analysis
- Volatility regime detection  
- Market correlation analysis

### 3. ML Prediction Agent (`live_ml_prediction_agent.py`)
- Loads trained models from existing ML workflow
- Generates predictions for returns, risk, and strategy suitability
- Provides confidence intervals and uncertainty estimates
- Handles model versioning and fallback scenarios

**Predictions:**
- **Expected Return**: 5-day forward return predictions
- **Risk Metrics**: Expected drawdown, volatility estimates
- **Strategy Suitability**: Confidence scores for each strategy type
- **Confidence Intervals**: Bootstrap-based uncertainty estimates

### 4. Stock Selection Agent (`live_stock_selection_agent.py`)
- Scores stocks using weighted composite scoring
- Applies diversification and risk management filters
- Selects top 50 stocks with optimal characteristics

**Scoring Weights:**
- 40% - Predicted Returns
- 30% - Risk-Adjusted Returns (Sharpe-like)
- 20% - Strategy Fit Confidence
- 10% - Data Quality Score

**Filters:**
- Minimum score thresholds
- Maximum volatility limits
- Liquidity requirements
- Data quality standards
- Sector diversification constraints

### 5. Strategy Assignment Agent (`live_strategy_assignment_agent.py`)
- Assigns primary and backup strategies for each selected stock
- Considers current market conditions and stock characteristics
- Calculates expected performance metrics
- Provides assignment rationale and risk warnings

**Available Strategies:**
- **Momentum**: For trending markets with strong directional moves
- **Mean Reversion**: For ranging markets with low volatility
- **Breakout**: For high volatility with trending potential
- **Trend Following**: For sustained directional movements

## 🔧 Configuration

The system uses a comprehensive YAML configuration file (`config/live_stock_selection_config.yaml`) with sections for:

- **Data Management**: API limits, data quality thresholds, storage settings
- **Feature Engineering**: Technical indicator parameters, market condition settings
- **ML Prediction**: Model loading, confidence thresholds, fallback options
- **Stock Selection**: Scoring weights, selection criteria, diversification rules
- **Strategy Assignment**: Strategy definitions, market condition matching
- **Reporting**: Output formats, risk warnings, recommendations
- **System**: Performance settings, logging, error handling

## 🚀 Usage

### Basic Usage

```python
from agents.live_stock_selection_workflow import LiveStockSelectionWorkflow

# Initialize workflow
workflow = LiveStockSelectionWorkflow()
await workflow.initialize()

# Execute complete workflow
result = await workflow.execute_workflow()

# Access results
selected_stocks = result.selected_stocks
strategy_assignments = result.strategy_assignments
performance_summary = result.performance_summary

# Cleanup
await workflow.cleanup()
```

### Integration with Clean Trading System

The system integrates seamlessly with the existing clean trading system by replacing the `UniversePreparer` with `MLUniversePreparer`:

```python
# In run_clean_trading_system.py
preparer = MLUniversePreparer(event_bus, market_data_agent)
selected_stocks = await preparer.run()
```

### Command Line Usage

```bash
# Run the complete trading system with ML-driven stock selection
python scripts/run_clean_trading_system.py --mode paper

# Run tests
python tests/run_tests.py --all
python tests/run_tests.py --quick
python tests/run_tests.py --integration
```

## 📊 Output

The system generates comprehensive reports including:

### Selected Stocks Report
```yaml
selected_stocks:
  - RELIANCE
  - TCS
  - INFY
  # ... up to 50 stocks

strategy_assignments:
  RELIANCE:
    primary_strategy: "momentum"
    primary_confidence: 0.85
    backup_strategy: "breakout"
    backup_confidence: 0.72
    expected_performance:
      expected_return: 0.045
      expected_sharpe: 1.2
      expected_drawdown: 0.08
    rationale: "Selected momentum strategy with 85% confidence. Current market: normal volatility, trending_up trend"
    risk_warnings: []
```

### Performance Summary
```yaml
performance_summary:
  avg_expected_return: 0.042
  avg_expected_sharpe: 1.15
  total_assignments: 50
  avg_primary_confidence: 0.78
  strategy_distribution:
    momentum: 18
    mean_reversion: 12
    breakout: 15
    trend_following: 5
```

### Risk Assessment
```yaml
risk_assessment:
  overall_risk_level: "medium"
  high_risk_stocks: []
  concentration_risks: {}
  strategy_risks: {}
  market_risks: {}
```

## 🧪 Testing

Comprehensive test suite covering:

### Unit Tests
- Individual agent functionality
- Technical indicator calculations
- Data quality validation
- ML prediction generation
- Stock scoring algorithms
- Strategy assignment logic

### Integration Tests
- End-to-end workflow execution
- Integration with existing ML models
- Data format compatibility
- Configuration validation
- Error handling and fallbacks

### Performance Tests
- Processing 500+ stocks efficiently
- Memory usage optimization
- Concurrent processing capabilities
- Scalability validation

### Running Tests

```bash
# Run all tests
python tests/run_tests.py --all

# Run quick validation tests
python tests/run_tests.py --quick

# Run integration tests
python tests/run_tests.py --integration

# Run performance tests
python tests/run_tests.py --performance

# Run with pytest (if available)
pytest tests/test_live_stock_selection_workflow.py -v
```

## 🔄 Integration Points

### With Existing ML Workflow
- Uses trained models from `EnhancedModelTrainingAgent`
- Compatible with existing feature engineering utilities
- Leverages established data preprocessing pipelines
- Maintains consistency with backtesting results

### With Clean Trading System
- Replaces `UniversePreparer` with ML-driven approach
- Provides selected stocks to signal generation agents
- Integrates with existing event bus architecture
- Maintains compatibility with execution agents

### With Data Infrastructure
- Uses existing SmartAPI client for data downloads
- Leverages instrument master for symbol resolution
- Compatible with existing data storage formats
- Integrates with caching mechanisms

## ⚙️ Technical Specifications

### Performance
- **Processing Speed**: 500+ stocks in under 30 minutes
- **Memory Usage**: Optimized for 2GB memory limit
- **Concurrency**: Configurable concurrent processing
- **Caching**: 1-hour cache duration for efficiency

### Data Storage
- **Format**: Parquet with Brotli compression
- **Directory**: `data/live/` for live trading data
- **Retention**: Configurable data retention policies
- **Backup**: Automatic quality report generation

### Error Handling
- **Graceful Degradation**: Fallback mechanisms at each step
- **Retry Logic**: Configurable retry attempts with exponential backoff
- **Logging**: Comprehensive logging with configurable levels
- **Monitoring**: Built-in performance and quality monitoring

## 🚨 Risk Management

### Data Quality Controls
- Minimum data point requirements
- Maximum missing data thresholds
- Outlier detection and handling
- Date range coverage validation

### Model Risk Controls
- Model performance monitoring
- Confidence threshold enforcement
- Fallback to simple scoring methods
- Version compatibility checks

### Selection Risk Controls
- Diversification constraints
- Correlation limits
- Volatility caps
- Liquidity requirements

### Strategy Risk Controls
- Market condition matching
- Confidence gap requirements
- Expected performance validation
- Risk warning generation

## 📈 Future Enhancements

### Planned Features
- Real-time market condition updates
- Dynamic strategy rebalancing
- Enhanced sector analysis
- Alternative data integration
- Advanced ensemble methods

### Scalability Improvements
- Distributed processing capabilities
- Cloud deployment options
- Real-time streaming data
- Advanced caching strategies

### Model Enhancements
- Online learning capabilities
- Reinforcement learning integration
- Multi-timeframe analysis
- Sentiment analysis integration

## 🤝 Contributing

1. Follow existing code patterns and documentation standards
2. Add comprehensive tests for new features
3. Update configuration documentation
4. Ensure backward compatibility
5. Test integration with existing systems

## 📝 License

This system extends the existing equity trading framework and follows the same licensing terms.

---

**Note**: This system is designed for premarket preparation and integrates seamlessly with the existing clean trading system. It provides intelligent stock selection and strategy assignment based on machine learning analysis while maintaining robust error handling and fallback mechanisms.
