insights:
  recommendations:
  - Focus on strategies with predicted Sharpe ratio > 1.0
  - Avoid strategies with predicted drawdown > 15%
  - Consider ensemble approach with top 3-5 strategies
  - Monitor model performance and retrain monthly
  risk_warnings: []
  top_strategies: []
model_performance:
  drawdown_prediction:
    feature_importance:
      lightgbm:
      - feature: roi_drawdown_ratio
        importance: 548
      - feature: avg_total_pnl
        importance: 462
      - feature: min_max_drawdown
        importance: 362
      - feature: max_max_drawdown
        importance: 350
      - feature: std_max_drawdown
        importance: 255
      - feature: min_total_pnl
        importance: 255
      - feature: avg_roi
        importance: 229
      - feature: avg_expectancy
        importance: 171
      - feature: std_total_pnl
        importance: 158
      - feature: min_sharpe_ratio
        importance: 146
      - feature: avg_total_trades
        importance: 145
      - feature: min_expectancy
        importance: 142
      - feature: min_accuracy
        importance: 137
      - feature: std_winning_trades
        importance: 137
      - feature: avg_sharpe_ratio
        importance: 132
      - feature: avg_accuracy
        importance: 125
      - feature: max_total_pnl
        importance: 123
      - feature: std_total_trades
        importance: 121
      - feature: avg_winning_trades
        importance: 119
      - feature: min_profit_factor
        importance: 113
      - feature: std_profit_factor
        importance: 107
      - feature: min_roi
        importance: 107
      - feature: max_profit_factor
        importance: 106
      - feature: avg_profit_factor
        importance: 103
      - feature: max_accuracy
        importance: 102
      - feature: std_sharpe_ratio
        importance: 102
      - feature: std_accuracy
        importance: 99
      - feature: std_expectancy
        importance: 97
      - feature: max_expectancy
        importance: 96
      - feature: sharpe_consistency
        importance: 82
      - feature: trades_per_period
        importance: 66
      - feature: max_sharpe_ratio
        importance: 60
      - feature: consistency_score
        importance: 48
      - feature: std_roi
        importance: 39
      - feature: max_roi
        importance: 32
      - feature: walk_forward_steps
        importance: 0
    lightgbm:
      best_params:
        bagging_fraction: 0.9685622144455723
        feature_fraction: 0.776229271919761
        learning_rate: 0.08737313720662243
        max_depth: 10
        n_estimators: 231
        num_leaves: 65
      mae: 0.4050699334107844
      mape: 0.05889206158210345
      mse: 0.3168852048535008
      r2_score: 0.9681320458929913
      rmse: !!python/object/apply:numpy.core.multiarray.scalar
      - &id001 !!python/object/apply:numpy.dtype
        args:
        - f8
        - false
        - true
        state: !!python/tuple
        - 3
        - <
        - null
        - null
        - null
        - -1
        - -1
        - 0
      - !!binary |
        XDm6f3wD4j8=
      test_score: 0.9681320458929913
      train_score: 0.9996963025241971
  ensemble: {}
  profit_factor_prediction:
    feature_importance:
      lightgbm:
      - feature: avg_accuracy
        importance: 725
      - feature: max_profit_factor
        importance: 543
      - feature: min_profit_factor
        importance: 522
      - feature: std_profit_factor
        importance: 462
      - feature: avg_sharpe_ratio
        importance: 453
      - feature: avg_expectancy
        importance: 393
      - feature: max_accuracy
        importance: 327
      - feature: std_accuracy
        importance: 319
      - feature: min_accuracy
        importance: 318
      - feature: roi_drawdown_ratio
        importance: 313
      - feature: max_max_drawdown
        importance: 313
      - feature: min_sharpe_ratio
        importance: 299
      - feature: min_expectancy
        importance: 279
      - feature: std_sharpe_ratio
        importance: 276
      - feature: std_max_drawdown
        importance: 268
      - feature: std_total_trades
        importance: 257
      - feature: sharpe_consistency
        importance: 256
      - feature: max_expectancy
        importance: 247
      - feature: max_sharpe_ratio
        importance: 245
      - feature: std_winning_trades
        importance: 243
      - feature: std_expectancy
        importance: 240
      - feature: avg_total_pnl
        importance: 211
      - feature: max_total_pnl
        importance: 180
      - feature: avg_winning_trades
        importance: 177
      - feature: std_total_pnl
        importance: 173
      - feature: avg_total_trades
        importance: 167
      - feature: avg_max_drawdown
        importance: 165
      - feature: min_max_drawdown
        importance: 159
      - feature: consistency_score
        importance: 140
      - feature: min_total_pnl
        importance: 138
      - feature: trades_per_period
        importance: 132
      - feature: avg_roi
        importance: 108
      - feature: min_roi
        importance: 107
      - feature: max_roi
        importance: 98
      - feature: std_roi
        importance: 91
      - feature: walk_forward_steps
        importance: 0
    lightgbm:
      best_params:
        bagging_fraction: 0.7033561255555947
        feature_fraction: 0.5311200001917681
        learning_rate: 0.04338783699035054
        max_depth: 6
        n_estimators: 687
        num_leaves: 88
      mae: 0.029426186077282677
      mape: 0.03076706020488499
      mse: 0.0014543708794023701
      r2_score: 0.9409749828715535
      rmse: !!python/object/apply:numpy.core.multiarray.scalar
      - *id001
      - !!binary |
        T7G7BpeGoz8=
      test_score: 0.9409749828715535
      train_score: 0.9981111814416244
  profitability_classification:
    feature_importance:
      lightgbm:
      - feature: avg_total_trades
        importance: 0
      - feature: max_expectancy
        importance: 0
      - feature: std_profit_factor
        importance: 0
      - feature: min_profit_factor
        importance: 0
      - feature: max_profit_factor
        importance: 0
      - feature: avg_max_drawdown
        importance: 0
      - feature: std_max_drawdown
        importance: 0
      - feature: min_max_drawdown
        importance: 0
      - feature: max_max_drawdown
        importance: 0
      - feature: avg_sharpe_ratio
        importance: 0
      - feature: std_sharpe_ratio
        importance: 0
      - feature: min_sharpe_ratio
        importance: 0
      - feature: max_sharpe_ratio
        importance: 0
      - feature: walk_forward_steps
        importance: 0
      - feature: consistency_score
        importance: 0
      - feature: roi_drawdown_ratio
        importance: 0
      - feature: sharpe_consistency
        importance: 0
      - feature: avg_profit_factor
        importance: 0
      - feature: min_expectancy
        importance: 0
      - feature: std_total_trades
        importance: 0
      - feature: std_expectancy
        importance: 0
      - feature: avg_winning_trades
        importance: 0
      - feature: std_winning_trades
        importance: 0
      - feature: avg_accuracy
        importance: 0
      - feature: std_accuracy
        importance: 0
      - feature: min_accuracy
        importance: 0
      - feature: max_accuracy
        importance: 0
      - feature: avg_total_pnl
        importance: 0
      - feature: std_total_pnl
        importance: 0
      - feature: min_total_pnl
        importance: 0
      - feature: max_total_pnl
        importance: 0
      - feature: avg_roi
        importance: 0
      - feature: std_roi
        importance: 0
      - feature: min_roi
        importance: 0
      - feature: max_roi
        importance: 0
      - feature: avg_expectancy
        importance: 0
      - feature: trades_per_period
        importance: 0
    lightgbm:
      accuracy: 1.0
      best_params:
        bagging_fraction: 0.8331159470467007
        feature_fraction: 0.7243993338002723
        learning_rate: 0.04076448135172422
        max_depth: 7
        n_estimators: 196
        num_leaves: 59
      f1_score: 1.0
      precision: 1.0
      recall: 1.0
      test_score: 1.0
      train_score: 1.0
  roi_prediction:
    feature_importance:
      lightgbm:
      - feature: avg_total_pnl
        importance: 626
      - feature: roi_drawdown_ratio
        importance: 367
      - feature: avg_max_drawdown
        importance: 343
      - feature: min_total_pnl
        importance: 312
      - feature: std_max_drawdown
        importance: 294
      - feature: std_total_pnl
        importance: 289
      - feature: min_max_drawdown
        importance: 273
      - feature: std_winning_trades
        importance: 221
      - feature: max_max_drawdown
        importance: 215
      - feature: std_total_trades
        importance: 200
      - feature: min_profit_factor
        importance: 190
      - feature: min_sharpe_ratio
        importance: 171
      - feature: std_accuracy
        importance: 168
      - feature: avg_profit_factor
        importance: 168
      - feature: avg_total_trades
        importance: 167
      - feature: std_sharpe_ratio
        importance: 166
      - feature: max_accuracy
        importance: 165
      - feature: min_expectancy
        importance: 163
      - feature: min_accuracy
        importance: 163
      - feature: std_profit_factor
        importance: 162
      - feature: max_profit_factor
        importance: 162
      - feature: avg_accuracy
        importance: 156
      - feature: avg_expectancy
        importance: 147
      - feature: std_expectancy
        importance: 147
      - feature: avg_winning_trades
        importance: 137
      - feature: max_total_pnl
        importance: 130
      - feature: avg_sharpe_ratio
        importance: 119
      - feature: max_sharpe_ratio
        importance: 106
      - feature: max_expectancy
        importance: 101
      - feature: sharpe_consistency
        importance: 78
      - feature: consistency_score
        importance: 70
      - feature: min_roi
        importance: 49
      - feature: max_roi
        importance: 46
      - feature: trades_per_period
        importance: 36
      - feature: std_roi
        importance: 31
      - feature: walk_forward_steps
        importance: 0
    lightgbm:
      best_params:
        bagging_fraction: 0.6917512071669627
        feature_fraction: 0.9443378985908484
        learning_rate: 0.27150342482251816
        max_depth: 6
        n_estimators: 608
        num_leaves: 58
      mae: 0.08520227736535237
      mape: 0.033208331288182326
      mse: 0.038292819731185805
      r2_score: 0.9952898418095016
      rmse: !!python/object/apply:numpy.core.multiarray.scalar
      - *id001
      - !!binary |
        34LaDTkMyT8=
      test_score: 0.9952898418095016
      train_score: 0.9999998329212655
  sharpe_ratio_prediction:
    feature_importance:
      lightgbm:
      - feature: std_sharpe_ratio
        importance: 606
      - feature: min_sharpe_ratio
        importance: 460
      - feature: sharpe_consistency
        importance: 362
      - feature: roi_drawdown_ratio
        importance: 302
      - feature: consistency_score
        importance: 296
      - feature: min_expectancy
        importance: 283
      - feature: avg_expectancy
        importance: 255
      - feature: avg_accuracy
        importance: 253
      - feature: avg_profit_factor
        importance: 212
      - feature: std_profit_factor
        importance: 190
      - feature: max_expectancy
        importance: 171
      - feature: max_max_drawdown
        importance: 154
      - feature: max_sharpe_ratio
        importance: 154
      - feature: std_total_trades
        importance: 152
      - feature: std_expectancy
        importance: 149
      - feature: std_accuracy
        importance: 147
      - feature: max_profit_factor
        importance: 140
      - feature: std_winning_trades
        importance: 131
      - feature: min_accuracy
        importance: 128
      - feature: min_max_drawdown
        importance: 126
      - feature: std_max_drawdown
        importance: 124
      - feature: min_profit_factor
        importance: 122
      - feature: max_total_pnl
        importance: 121
      - feature: avg_winning_trades
        importance: 113
      - feature: std_total_pnl
        importance: 111
      - feature: max_accuracy
        importance: 108
      - feature: avg_max_drawdown
        importance: 100
      - feature: min_total_pnl
        importance: 98
      - feature: avg_total_pnl
        importance: 91
      - feature: max_roi
        importance: 64
      - feature: avg_total_trades
        importance: 51
      - feature: min_roi
        importance: 40
      - feature: avg_roi
        importance: 38
      - feature: trades_per_period
        importance: 35
      - feature: std_roi
        importance: 13
      - feature: walk_forward_steps
        importance: 1
    lightgbm:
      best_params:
        bagging_fraction: 0.869118556269576
        feature_fraction: 0.8735832791630962
        learning_rate: 0.06136279104688373
        max_depth: 4
        n_estimators: 694
        num_leaves: 48
      mae: 0.13337866471459178
      mape: 0.1691804372551207
      mse: 0.032750088707734853
      r2_score: 0.9799594813827661
      rmse: !!python/object/apply:numpy.core.multiarray.scalar
      - *id001
      - !!binary |
        t7QbLgUqxz8=
      test_score: 0.9799594813827661
      train_score: 0.9994910108367023
next_steps:
- Deploy top-performing strategies in paper trading
- Set up automated model retraining pipeline
- Implement real-time prediction serving
- Monitor strategy performance and model drift
predictions:
  drawdown_prediction:
    error: X has 15 features, but StandardScaler is expecting 36 features as input.
  profit_factor_prediction:
    error: X has 15 features, but StandardScaler is expecting 36 features as input.
  profitability_classification:
    error: X has 15 features, but StandardScaler is expecting 37 features as input.
  roi_prediction:
    error: X has 15 features, but StandardScaler is expecting 36 features as input.
  sharpe_ratio_prediction:
    error: X has 15 features, but StandardScaler is expecting 36 features as input.
timestamp: '2025-08-07T07:46:30.785420'
workflow_summary:
  backtesting_completed: true
  ml_training_completed: true
  predictions_generated: true
