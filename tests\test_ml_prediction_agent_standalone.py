#!/usr/bin/env python3
"""
🧪 Standalone Test Suite for LiveMLPredictionAgent
Tests the ML prediction agent with mock data to avoid lengthy data downloads
"""

import pytest
import asyncio
import logging
import polars as pl
import pandas as pd
import numpy as np
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import tempfile
import yaml
import json
import sys

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

from agents.live_ml_prediction_agent import LiveMLPredictionAgent, PredictionResult
from agents.live_stock_selection_workflow import LiveStockSelectionWorkflow, WorkflowResult
from utils.event_bus import EventBus

logger = logging.getLogger(__name__)

class MockEventBus:
    """Mock event bus for testing"""
    def __init__(self):
        pass
    
    def emit(self, event_type, data):
        pass
    
    def subscribe(self, event_type, callback):
        pass

class TestMLPredictionAgentStandalone:
    """Standalone test suite for ML Prediction Agent"""
    
    @pytest.fixture
    def mock_config(self):
        """Create mock configuration"""
        return {
            'ml_prediction': {
                'model_loading': {
                    'models_directory': 'data/models/enhanced',
                    'model_version': 'latest',
                    'fallback_enabled': True,
                    'fallback_models': ['lightgbm']
                },
                'prediction_tasks': {
                    'expected_return': {
                        'enabled': True,
                        'model_type': 'regression',
                        'confidence_threshold': 0.6
                    },
                    'risk_metrics': {
                        'enabled': True,
                        'targets': ['expected_drawdown', 'volatility'],
                        'model_type': 'regression'
                    },
                    'strategy_suitability': {
                        'enabled': True,
                        'model_type': 'classification',
                        'strategy_types': ['momentum', 'mean_reversion', 'breakout', 'trend_following']
                    }
                },
                'confidence_intervals': {
                    'enabled': True,
                    'confidence_level': 0.95,
                    'method': 'bootstrap',
                    'bootstrap_samples': 1000
                },
                'model_management': {
                    'version_check': True,
                    'performance_threshold': 0.6,
                    'fallback_strategy': 'simple_scoring',
                    'cache_predictions': True,
                    'cache_duration': 300
                }
            }
        }
    
    @pytest.fixture
    def sample_features_df(self):
        """Create sample features DataFrame"""
        np.random.seed(42)
        n_stocks = 10
        symbols = [f"STOCK_{i}" for i in range(n_stocks)]
        
        data = {
            'symbol': symbols,
            'volatility_std': np.random.uniform(0.01, 0.05, n_stocks),
            'rsi_14': np.random.uniform(20, 80, n_stocks),
            'macd': np.random.uniform(-2, 2, n_stocks),
            'bb_percent_b': np.random.uniform(0, 1, n_stocks),
            'ema_20': np.random.uniform(100, 5000, n_stocks),
            'volume_ratio': np.random.uniform(0.5, 2.0, n_stocks),
            'roc': np.random.uniform(-5, 5, n_stocks),
            'stoch_k': np.random.uniform(0, 100, n_stocks),
            'williams_r': np.random.uniform(-100, 0, n_stocks),
            'atr': np.random.uniform(1, 50, n_stocks)
        }
        
        return pl.DataFrame(data)
    
    @pytest.fixture
    async def ml_agent(self, mock_config):
        """Create ML prediction agent for testing"""
        event_bus = MockEventBus()
        agent = LiveMLPredictionAgent(
            event_bus=event_bus,
            config=mock_config,
            session_id="test_session"
        )
        
        # Initialize the agent
        await agent.initialize()
        return agent
    
    @pytest.mark.asyncio
    async def test_agent_initialization(self, ml_agent):
        """Test agent initialization"""
        assert ml_agent is not None
        assert hasattr(ml_agent, 'config')
        assert hasattr(ml_agent, 'ml_config')
        assert hasattr(ml_agent, 'loaded_models')
        assert hasattr(ml_agent, 'model_info')
    
    @pytest.mark.asyncio
    async def test_model_version_detection(self, ml_agent):
        """Test model version detection"""
        models_dir = Path("data/models/enhanced")
        if models_dir.exists():
            version = ml_agent._find_latest_model_version(models_dir)
            if version:
                assert isinstance(version, str)
                assert len(version) > 0
                logger.info(f"Found model version: {version}")
            else:
                logger.warning("No model version found")
    
    @pytest.mark.asyncio
    async def test_prediction_generation_with_fallback(self, ml_agent, sample_features_df):
        """Test prediction generation with fallback models"""
        # Generate predictions
        predictions = await ml_agent.generate_predictions(sample_features_df)
        
        # Verify results structure
        assert isinstance(predictions, dict)
        
        # If models are loaded, should have predictions
        if len(predictions) > 0:
            for symbol, prediction in predictions.items():
                assert isinstance(prediction, PredictionResult)
                assert prediction.symbol == symbol
                assert isinstance(prediction.is_valid, bool)
                logger.info(f"Generated prediction for {symbol}: valid={prediction.is_valid}")
        else:
            logger.warning("No predictions generated - this may indicate model loading issues")
    
    @pytest.mark.asyncio
    async def test_feature_vector_preparation(self, ml_agent):
        """Test feature vector preparation"""
        sample_features = {
            'volatility_std': 0.02,
            'rsi_14': 65.0,
            'macd': 0.5,
            'bb_percent_b': 0.7,
            'ema_20': 2500.0,
            'volume_ratio': 1.2,
            'roc': 2.5
        }
        
        feature_vector = ml_agent._prepare_feature_vector(sample_features)
        
        if feature_vector is not None:
            assert feature_vector.shape[0] == 1  # Single sample
            assert feature_vector.shape[1] == len(sample_features)  # All features
            logger.info(f"Feature vector shape: {feature_vector.shape}")
        else:
            logger.warning("Feature vector preparation returned None")

class TestWorkflowIntegration:
    """Test workflow integration with mock data"""
    
    @pytest.fixture
    def workflow_config_path(self):
        """Get workflow configuration path"""
        return Path(__file__).parent.parent / "config" / "live_stock_selection_config.yaml"
    
    @pytest.fixture
    async def workflow(self, workflow_config_path):
        """Create workflow for testing"""
        event_bus = MockEventBus()
        workflow = LiveStockSelectionWorkflow(
            event_bus=event_bus,
            session_id="test_session",
            config_path=workflow_config_path
        )
        
        # Initialize the workflow
        success = await workflow.initialize()
        if success:
            return workflow
        else:
            pytest.skip("Workflow initialization failed")
    
    @pytest.mark.asyncio
    async def test_workflow_initialization(self, workflow):
        """Test workflow initialization"""
        assert workflow is not None
        assert hasattr(workflow, 'config')
        assert hasattr(workflow, 'agents')
        assert 'ml_prediction' in workflow.agents
    
    @pytest.mark.asyncio
    async def test_workflow_with_mock_data(self, workflow):
        """Test workflow execution with mock data"""
        # Create mock stock list
        test_stocks = ["RELIANCE", "TCS", "INFY"]
        
        # This will test the workflow but may fail at data download stage
        # That's expected in a test environment
        try:
            result = await workflow.execute_workflow(test_stocks)
            assert isinstance(result, WorkflowResult)
            logger.info(f"Workflow completed with {len(result.selected_stocks)} selected stocks")
        except Exception as e:
            logger.warning(f"Workflow execution failed (expected in test): {e}")
            # This is expected if data/models are not available

def create_mock_data_files():
    """Create mock data files for testing"""
    project_root = Path(__file__).parent.parent
    live_data_path = project_root / "data" / "live"
    live_data_path.mkdir(parents=True, exist_ok=True)
    
    # Create mock data for a few stocks
    test_stocks = ["RELIANCE", "TCS", "INFY"]
    
    for stock in test_stocks:
        # Create mock 1-minute data
        start_time = datetime.now().replace(hour=9, minute=15, second=0, microsecond=0)
        end_time = datetime.now().replace(hour=15, minute=30, second=0, microsecond=0)
        
        # Generate timestamps manually for 1-minute intervals
        timestamps = []
        current_time = start_time
        while current_time <= end_time:
            timestamps.append(current_time)
            current_time += timedelta(minutes=1)
        
        dates = timestamps
        
        n_points = len(dates)
        base_price = np.random.uniform(100, 5000)
        
        # Generate realistic OHLCV data
        np.random.seed(hash(stock) % 2**32)  # Consistent seed per stock
        returns = np.random.normal(0, 0.001, n_points)
        prices = base_price * np.exp(np.cumsum(returns))
        
        data = {
            'timestamp': dates,
            'open': prices * (1 + np.random.normal(0, 0.0005, n_points)),
            'high': prices * (1 + np.abs(np.random.normal(0, 0.002, n_points))),
            'low': prices * (1 - np.abs(np.random.normal(0, 0.002, n_points))),
            'close': prices,
            'volume': np.random.randint(1000, 100000, n_points)
        }
        
        df = pl.DataFrame(data)
        
        # Save mock data
        output_file = live_data_path / f"{stock}_1min.parquet"
        df.write_parquet(output_file, compression='brotli')
        
        logger.info(f"Created mock data for {stock}: {len(df)} rows")

@pytest.mark.asyncio
async def test_model_loading_debug():
    """Debug test to check model loading"""
    project_root = Path(__file__).parent.parent
    models_dir = project_root / "data" / "models" / "enhanced"
    
    logger.info(f"Checking models directory: {models_dir}")
    logger.info(f"Directory exists: {models_dir.exists()}")
    
    if models_dir.exists():
        # List all files
        all_files = list(models_dir.rglob("*"))
        logger.info(f"Found {len(all_files)} files in models directory")
        
        # List model files
        model_files = list(models_dir.rglob("*.joblib"))
        logger.info(f"Found {len(model_files)} .joblib files")
        
        for model_file in model_files[:5]:  # Show first 5
            logger.info(f"Model file: {model_file}")
        
        # List training results
        training_files = list(models_dir.rglob("training_results_*.json"))
        logger.info(f"Found {len(training_files)} training results files")
        
        for training_file in training_files[:3]:  # Show first 3
            logger.info(f"Training file: {training_file}")
    
    # Test the ML agent initialization
    event_bus = MockEventBus()
    config = {
        'ml_prediction': {
            'model_loading': {
                'models_directory': str(models_dir),
                'model_version': 'latest',
                'fallback_enabled': True,
                'fallback_models': ['lightgbm']
            },
            'prediction_tasks': {
                'expected_return': {'enabled': True, 'model_type': 'regression'},
                'risk_metrics': {'enabled': True, 'targets': ['expected_drawdown', 'volatility']},
                'strategy_suitability': {'enabled': True, 'model_type': 'classification'}
            },
            'confidence_intervals': {'enabled': True, 'confidence_level': 0.95, 'method': 'bootstrap'},
            'model_management': {'version_check': True, 'performance_threshold': 0.6}
        }
    }
    
    agent = LiveMLPredictionAgent(
        event_bus=event_bus,
        config=config,
        session_id="debug_session"
    )
    
    success = await agent.initialize()
    logger.info(f"Agent initialization success: {success}")
    
    if success:
        logger.info(f"Loaded models: {list(agent.loaded_models.keys())}")
        for task, models in agent.loaded_models.items():
            logger.info(f"Task {task}: {list(models.keys())}")

if __name__ == "__main__":
    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Create mock data files
    create_mock_data_files()
    
    # Run debug test
    asyncio.run(test_model_loading_debug())