#!/usr/bin/env python3
"""
Test script to validate token mapping and SmartAPI connectivity.
This script helps debug token mapping issues and test SmartAPI historical data download.
"""

import json
import sys
import asyncio
import logging
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Optional

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

from core.smartapi_client import ModernSmartAPIClient
from agents.clean_market_data_agent import CleanMarketDataAgent

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class TokenMappingTester:
    """Test token mapping and SmartAPI connectivity"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.token_mapping_file = self.project_root / 'token_mapping.json'
        self.symbol_to_token_map = {}
        
    def load_token_mapping(self) -> bool:
        """Load token mapping from JSON file"""
        try:
            with open(self.token_mapping_file, 'r') as f:
                data = json.load(f)
                self.symbol_to_token_map = data.get("symbol_to_token", {})
                logger.info(f"Loaded {len(self.symbol_to_token_map)} token mappings")
                return True
        except Exception as e:
            logger.error(f"Failed to load token mapping: {e}")
            return False
    
    def test_symbol_lookup(self, symbols: List[str]) -> Dict[str, Optional[str]]:
        """Test symbol to token lookup for given symbols"""
        results = {}
        
        for symbol in symbols:
            keys_to_check = [
                f"{symbol}-EQ_NSE",
                f"{symbol}_NSE", 
                symbol,
                f"{symbol.upper()}-EQ_NSE",
                f"{symbol.upper()}_NSE",
                symbol.upper()
            ]
            
            token = None
            matched_key = None
            for key in keys_to_check:
                if key in self.symbol_to_token_map:
                    token = self.symbol_to_token_map[key]
                    matched_key = key
                    break
            
            results[symbol] = {
                'token': token,
                'matched_key': matched_key,
                'available_similar': [k for k in self.symbol_to_token_map.keys() 
                                    if symbol.upper() in k.upper()][:5]
            }
            
            if token:
                logger.info(f"✓ {symbol}: Found token {token} via key '{matched_key}'")
            else:
                similar = results[symbol]['available_similar']
                if similar:
                    logger.warning(f"✗ {symbol}: No token found. Similar keys: {similar}")
                else:
                    logger.warning(f"✗ {symbol}: No token found. No similar keys.")
        
        return results
    
    def find_sample_tokens(self, count: int = 10) -> List[Dict[str, str]]:
        """Find sample tokens for testing"""
        samples = []
        for key, token in list(self.symbol_to_token_map.items())[:count]:
            # Extract symbol from key
            if '-EQ_NSE' in key:
                symbol = key.replace('-EQ_NSE', '')
            elif '_NSE' in key:
                symbol = key.replace('_NSE', '')
            else:
                symbol = key
            
            samples.append({
                'symbol': symbol,
                'token': token,
                'key': key
            })
        
        return samples
    
    async def test_smartapi_download(self, symbol: str, token: str) -> bool:
        """Test SmartAPI historical data download for a specific symbol/token"""
        try:
            # Load credentials
            credentials_file = self.project_root / '.env'
            if not credentials_file.exists():
                logger.error("No .env file found for SmartAPI credentials")
                return False
            
            # Create SmartAPI client
            from dotenv import load_dotenv
            load_dotenv(credentials_file)
            
            import os
            credentials = {
                'api_key': os.getenv('SMARTAPI_API_KEY'),
                'username': os.getenv('SMARTAPI_USERNAME'),
                'pwd': os.getenv('SMARTAPI_PASSWORD'),
                'token': os.getenv('SMARTAPI_TOKEN')
            }
            
            if not all(credentials.values()):
                logger.error("Missing SmartAPI credentials in .env file")
                return False
            
            client = ModernSmartAPIClient(credentials)
            auth_success = await client.authenticate()
            
            if not auth_success:
                logger.error("SmartAPI authentication failed")
                return False
            
            # Test historical data download
            end_date = datetime.now()
            start_date = end_date - timedelta(days=5)  # 5 days of data
            
            logger.info(f"Testing download for {symbol} (token: {token})")
            data = await client.get_historical_data_batch(
                symbol_token=token,
                exchange='NSE',
                start_date=start_date,
                end_date=end_date,
                interval="ONE_MINUTE"
            )
            
            if data:
                logger.info(f"✓ Successfully downloaded {len(data)} candles for {symbol}")
                return True
            else:
                logger.warning(f"✗ No data returned for {symbol}")
                return False
                
        except Exception as e:
            logger.error(f"✗ Error testing {symbol}: {e}")
            return False

async def main():
    """Main test function"""
    tester = TokenMappingTester()
    
    # Load token mapping
    if not tester.load_token_mapping():
        return
    
    # Test common symbols
    test_symbols = [
        'RELIANCE', 'TCS', 'INFY', 'HDFC', 'ICICIBANK', 
        'SBIN', 'BHARTIARTL', 'ITC', 'KOTAKBANK', 'LT'
    ]
    
    logger.info("="*60)
    logger.info("TESTING SYMBOL TO TOKEN MAPPING")
    logger.info("="*60)
    
    results = tester.test_symbol_lookup(test_symbols)
    
    # Count successful mappings
    successful = sum(1 for r in results.values() if r['token'])
    logger.info(f"\nMapping Results: {successful}/{len(test_symbols)} symbols found")
    
    # Test SmartAPI download with a few successful tokens
    logger.info("\n" + "="*60)
    logger.info("TESTING SMARTAPI HISTORICAL DATA DOWNLOAD")
    logger.info("="*60)
    
    successful_symbols = [(symbol, data['token']) for symbol, data in results.items() 
                         if data['token']]
    
    if successful_symbols:
        # Test first 3 successful symbols
        for symbol, token in successful_symbols[:3]:
            await tester.test_smartapi_download(symbol, token)
            await asyncio.sleep(2)  # Rate limiting
    else:
        logger.warning("No successful token mappings found for SmartAPI testing")
    
    # Show sample tokens from mapping file
    logger.info("\n" + "="*60)
    logger.info("SAMPLE TOKENS FROM MAPPING FILE")
    logger.info("="*60)
    
    samples = tester.find_sample_tokens(10)
    for sample in samples:
        logger.info(f"Symbol: {sample['symbol']:<15} Token: {sample['token']:<10} Key: {sample['key']}")

if __name__ == "__main__":
    asyncio.run(main())
