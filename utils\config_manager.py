#!/usr/bin/env python3
"""
CONFIG MANAGER
Centralized configuration management for the trading system

Features:
- Environment variable loading
- Configuration validation
- Default values and fallbacks
- Secure credential handling
"""

import os
import logging
from typing import Dict, Any, Optional
from pathlib import Path
import json

try:
    from dotenv import load_dotenv
    DOTENV_AVAILABLE = True
except ImportError:
    DOTENV_AVAILABLE = False

logger = logging.getLogger(__name__)

class ConfigManager:
    """
    Configuration manager for the trading system
    
    Handles:
    - Environment variable loading
    - Configuration validation
    - Default values
    - Secure credential management
    """
    
    def __init__(self, config_file: Optional[str] = None):
        """Initialize configuration manager"""
        self.config_file = config_file
        self.config = {}
        
        # Load environment variables
        self._load_environment()
        
        # Load config file if provided
        if config_file:
            self._load_config_file(config_file)
        
        logger.info("[INIT] Configuration manager initialized")
    
    def _load_environment(self):
        """Load environment variables"""
        try:
            if DOTENV_AVAILABLE:
                # Try to load .env file from project root
                project_root = Path(__file__).parent.parent
                env_file = project_root / ".env"
                
                if env_file.exists():
                    load_dotenv(env_file)
                    logger.info(f"[ENV] Loaded environment from {env_file}")
                else:
                    logger.warning(f"[ENV] .env file not found at {env_file}")
            else:
                logger.warning("[ENV] python-dotenv not available, using system environment only")
                
        except Exception as e:
            logger.error(f"[ERROR] Failed to load environment: {e}")
    
    def _load_config_file(self, config_file: str):
        """Load configuration from file"""
        try:
            config_path = Path(config_file)
            
            if config_path.exists():
                with open(config_path, 'r') as f:
                    if config_path.suffix.lower() == '.json':
                        file_config = json.load(f)
                    else:
                        # Assume YAML if not JSON
                        try:
                            import yaml
                            file_config = yaml.safe_load(f)
                        except ImportError:
                            logger.error("[ERROR] PyYAML not available for YAML config files")
                            return
                
                self.config.update(file_config)
                logger.info(f"[CONFIG] Loaded configuration from {config_file}")
            else:
                logger.warning(f"[CONFIG] Config file not found: {config_file}")
                
        except Exception as e:
            logger.error(f"[ERROR] Failed to load config file: {e}")
    
    def get(self, key: str, default: Any = None) -> Any:
        """Get configuration value"""
        try:
            # Check environment variables first
            env_value = os.getenv(key.upper())
            if env_value is not None:
                return env_value
            
            # Check config file
            if key in self.config:
                return self.config[key]
            
            # Return default
            return default
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to get config value for {key}: {e}")
            return default
    
    def get_smartapi_credentials(self) -> Dict[str, str]:
        """Get SmartAPI credentials"""
        try:
            credentials = {
                'api_key': self.get('SMARTAPI_API_KEY'),
                'username': self.get('SMARTAPI_USERNAME'),
                'password': self.get('SMARTAPI_PASSWORD'),
                'totp_token': self.get('SMARTAPI_TOTP_TOKEN')
            }
            
            # Validate credentials
            missing_creds = [k for k, v in credentials.items() if not v]
            if missing_creds:
                logger.warning(f"[CREDS] Missing SmartAPI credentials: {missing_creds}")
            else:
                logger.info("[CREDS] SmartAPI credentials loaded successfully")
            
            return credentials
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to get SmartAPI credentials: {e}")
            return {}
    
    def get_trading_config(self) -> Dict[str, Any]:
        """Get trading configuration"""
        try:
            return {
                'max_daily_trades': int(self.get('MAX_DAILY_TRADES', 5)),
                'initial_balance': float(self.get('INITIAL_BALANCE', 100000)),
                'commission_rate': float(self.get('COMMISSION_RATE', 0.0003)),
                'flat_brokerage': float(self.get('FLAT_BROKERAGE', 20)),
                'stt_rate': float(self.get('STT_RATE', 0.001)),
                'gst_rate': float(self.get('GST_RATE', 0.18)),
                'stamp_duty_rate': float(self.get('STAMP_DUTY_RATE', 0.00003)),
                'max_position_size': float(self.get('MAX_POSITION_SIZE', 0.1)),
                'max_daily_loss': float(self.get('MAX_DAILY_LOSS', 0.05)),
                'max_drawdown': float(self.get('MAX_DRAWDOWN', 0.15))
            }
        except Exception as e:
            logger.error(f"[ERROR] Failed to get trading config: {e}")
            return {}
    
    def get_market_config(self) -> Dict[str, Any]:
        """Get market configuration"""
        try:
            return {
                'market_start_time': self.get('MARKET_START_TIME', '09:15'),
                'market_end_time': self.get('MARKET_END_TIME', '15:30'),
                'pre_market_start': self.get('PRE_MARKET_START', '09:00'),
                'post_market_end': self.get('POST_MARKET_END', '16:00'),
                'trading_days': self.get('TRADING_DAYS', 'MON,TUE,WED,THU,FRI').split(','),
                'holidays': self.get('MARKET_HOLIDAYS', '').split(',') if self.get('MARKET_HOLIDAYS') else []
            }
        except Exception as e:
            logger.error(f"[ERROR] Failed to get market config: {e}")
            return {}
    
    def get_data_config(self) -> Dict[str, Any]:
        """Get data configuration"""
        try:
            return {
                'historical_days': int(self.get('HISTORICAL_DAYS', 40)),
                'timeframes': self.get('TIMEFRAMES', '1min,2min,3min,5min,10min,15min,30min,1hr').split(','),
                'data_retention_days': int(self.get('DATA_RETENTION_DAYS', 90)),
                'websocket_reconnect_attempts': int(self.get('WEBSOCKET_RECONNECT_ATTEMPTS', 5)),
                'api_rate_limit_delay': float(self.get('API_RATE_LIMIT_DELAY', 0.5))
            }
        except Exception as e:
            logger.error(f"[ERROR] Failed to get data config: {e}")
            return {}
    
    def get_logging_config(self) -> Dict[str, Any]:
        """Get logging configuration"""
        try:
            return {
                'log_level': self.get('LOG_LEVEL', 'INFO'),
                'log_file': self.get('LOG_FILE', 'logs/trading_system.log'),
                'max_log_files': int(self.get('MAX_LOG_FILES', 10)),
                'max_log_size_mb': int(self.get('MAX_LOG_SIZE_MB', 100)),
                'console_logging': self.get('CONSOLE_LOGGING', 'true').lower() == 'true'
            }
        except Exception as e:
            logger.error(f"[ERROR] Failed to get logging config: {e}")
            return {}
    
    def validate_config(self) -> Dict[str, Any]:
        """Validate configuration"""
        try:
            validation_results = {
                'valid': True,
                'errors': [],
                'warnings': []
            }
            
            # Validate SmartAPI credentials
            creds = self.get_smartapi_credentials()
            missing_creds = [k for k, v in creds.items() if not v]
            if missing_creds:
                validation_results['errors'].append(f"Missing SmartAPI credentials: {missing_creds}")
                validation_results['valid'] = False
            
            # Validate trading config
            trading_config = self.get_trading_config()
            if trading_config['max_daily_trades'] <= 0:
                validation_results['errors'].append("max_daily_trades must be positive")
                validation_results['valid'] = False
            
            if trading_config['initial_balance'] <= 0:
                validation_results['errors'].append("initial_balance must be positive")
                validation_results['valid'] = False
            
            # Validate data config
            data_config = self.get_data_config()
            if data_config['historical_days'] <= 0:
                validation_results['warnings'].append("historical_days should be positive")
            
            return validation_results
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to validate config: {e}")
            return {'valid': False, 'errors': [str(e)], 'warnings': []}
    
    def set(self, key: str, value: Any):
        """Set configuration value"""
        try:
            self.config[key] = value
            logger.debug(f"[SET] Set config {key} = {value}")
        except Exception as e:
            logger.error(f"[ERROR] Failed to set config {key}: {e}")
    
    def save_config(self, filename: str):
        """Save current configuration to file"""
        try:
            config_path = Path(filename)
            
            with open(config_path, 'w') as f:
                if config_path.suffix.lower() == '.json':
                    json.dump(self.config, f, indent=2, default=str)
                else:
                    # Assume YAML
                    try:
                        import yaml
                        yaml.dump(self.config, f, default_flow_style=False)
                    except ImportError:
                        logger.error("[ERROR] PyYAML not available for YAML config files")
                        return False
            
            logger.info(f"[SAVE] Configuration saved to {filename}")
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to save config: {e}")
            return False