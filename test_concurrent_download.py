#!/usr/bin/env python3
"""
Test script to verify the concurrent download improvements work correctly.
"""

import asyncio
import logging
import sys
from pathlib import Path
from datetime import datetime, timedelta

# Add project root to path
sys.path.append(str(Path(__file__).parent))

from scripts.run_clean_trading_system import M<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>parer, ThreadSafeSmartAPIManager
from core.event_system import EventBus
from agents.clean_market_data_agent import CleanMarketDataAgent

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_concurrent_downloads():
    """Test concurrent downloads with a small set of symbols"""
    
    # Test symbols (small set for testing)
    test_symbols = ['RELIANCE', 'TCS', 'HDFCBANK', 'INFY', 'ICICIBANK']
    
    logger.info(f"Testing concurrent downloads with {len(test_symbols)} symbols")
    
    try:
        # Initialize event bus and market data agent
        event_bus = EventBus()
        market_data_agent = CleanMarketDataAgent(event_bus, session_id="test_session")
        
        # Initialize the market data agent
        await market_data_agent.initialize()
        
        # Create ML universe preparer
        ml_preparer = MLUniversePreparer(event_bus, market_data_agent)
        
        # Test the download process
        logger.info("Starting test download...")
        await ml_preparer._download_historical_data(test_symbols)
        
        logger.info("✅ Test completed successfully!")
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        raise

def test_thread_safe_manager():
    """Test the ThreadSafeSmartAPIManager"""
    logger.info("Testing ThreadSafeSmartAPIManager...")
    
    # Test singleton behavior
    manager1 = ThreadSafeSmartAPIManager()
    manager2 = ThreadSafeSmartAPIManager()
    
    assert manager1 is manager2, "ThreadSafeSmartAPIManager should be a singleton"
    logger.info("✅ Singleton behavior verified")
    
    logger.info("✅ ThreadSafeSmartAPIManager test completed")

if __name__ == "__main__":
    # Test the thread-safe manager first
    test_thread_safe_manager()
    
    # Then test concurrent downloads
    asyncio.run(test_concurrent_downloads())