#!/usr/bin/env python3
"""
MARKET DATA AGENT
Modern WebSocket-based market data streaming agent with proper async handling

Features:
- Real-time WebSocket data streaming from Angel One SmartAPI 2.0
- Historical data download with proper date handling
- Modern timeframe generation (1min, 2min, 3min, 5min, 10min, 15min, 30min, 1hr)
- Event-driven architecture for agent communication
- Proper error handling and reconnection logic
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import json
import polars as pl
from dataclasses import dataclass
import threading
import time

# SmartAPI imports
try:
    from SmartApi import SmartConnect
    from SmartApi.smartWebSocketV2 import SmartWebSocketV2
    import pyotp
    SMARTAPI_AVAILABLE = True
except ImportError:
    SMARTAPI_AVAILABLE = False

from .base_agent import BaseAgent
from ..utils.event_bus import EventBus, Event
from ..utils.config_manager import ConfigManager
from ..utils.modern_instrument_master import ModernInstrumentMaster

logger = logging.getLogger(__name__)

@dataclass
class MarketDataPoint:
    """Real-time market data point"""
    symbol: str
    timestamp: datetime
    open: float
    high: float
    low: float
    close: float
    volume: int
    ltp: float

@dataclass
class HistoricalDataRequest:
    """Historical data request parameters"""
    symbol: str
    exchange: str
    symboltoken: str
    interval: str
    fromdate: str
    todate: str

class MarketDataAgent(BaseAgent):
    """
    Market Data Agent for real-time and historical data management
    
    Responsibilities:
    - WebSocket connection management
    - Real-time data streaming
    - Historical data download
    - Multi-timeframe data generation
    - Data quality validation
    """
    
    def __init__(self, event_bus: EventBus, config: Any, session_id: str):
        super().__init__("MarketDataAgent", event_bus, config, session_id)
        
        # SmartAPI client and WebSocket
        self.smartapi_client = None
        self.websocket_client = None
        self.auth_token = None
        self.feed_token = None
        
        # Data storage
        self.market_data = {}  # Real-time data
        self.historical_data = {}  # Historical data
        self.timeframe_data = {  # Multi-timeframe data
            "1min": {},
            "2min": {},
            "3min": {},
            "5min": {},
            "10min": {},
            "15min": {},
            "30min": {},
            "1hr": {}
        }
        
        # WebSocket management
        self.websocket_connected = False
        self.reconnect_attempts = 0
        self.max_reconnect_attempts = 5
        
        # Instrument master
        self.instrument_master = None
        
        # Load credentials
        self.config_manager = ConfigManager()
        self._load_credentials()
    
    def _load_credentials(self):
        """Load SmartAPI credentials"""
        try:
            credentials = self.config_manager.get_smartapi_credentials()
            self.api_key = credentials.get('api_key')
            self.username = credentials.get('username')
            self.password = credentials.get('password')
            self.totp_token = credentials.get('totp_token')
            
            if not all([self.api_key, self.username, self.password, self.totp_token]):
                logger.warning("[WARN] SmartAPI credentials incomplete")
                self.credentials_available = False
            else:
                self.credentials_available = True
                logger.info("[SUCCESS] SmartAPI credentials loaded")
                
        except Exception as e:
            logger.error(f"[ERROR] Failed to load credentials: {e}")
            self.credentials_available = False
    
    async def initialize(self) -> bool:
        """Initialize the market data agent"""
        try:
            logger.info("[INIT] Initializing Market Data Agent...")
            
            # Initialize instrument master
            self.instrument_master = ModernInstrumentMaster()
            await self.instrument_master.load_instruments()
            
            # Initialize SmartAPI client
            if not self._initialize_smartapi_client():
                logger.error("[ERROR] Failed to initialize SmartAPI client")
                return False
            
            # Subscribe to events
            self.event_bus.subscribe("REQUEST_HISTORICAL_DATA", self._handle_historical_data_request)
            self.event_bus.subscribe("REQUEST_REALTIME_DATA", self._handle_realtime_data_request)
            
            logger.info("[SUCCESS] Market Data Agent initialized")
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to initialize Market Data Agent: {e}")
            return False
    
    def _initialize_smartapi_client(self) -> bool:
        """Initialize SmartAPI client with proper authentication"""
        try:
            if not SMARTAPI_AVAILABLE:
                logger.error("[ERROR] SmartAPI not available. Install with: pip install smartapi-python")
                return False
                
            if not self.credentials_available:
                logger.error("[ERROR] SmartAPI credentials not available")
                return False
            
            logger.info("[API] Initializing SmartAPI client...")
            
            # Initialize SmartConnect
            self.smartapi_client = SmartConnect(api_key=self.api_key)
            
            # Generate TOTP
            totp = pyotp.TOTP(self.totp_token)
            totp_code = totp.now()
            
            # Login
            data = self.smartapi_client.generateSession(
                clientCode=self.username,
                password=self.password,
                totp=totp_code
            )
            
            if data['status']:
                self.auth_token = data['data']['jwtToken']
                self.refresh_token = data['data']['refreshToken']
                self.feed_token = self.smartapi_client.getfeedToken()
                
                logger.info("[SUCCESS] SmartAPI authenticated successfully")
                return True
            else:
                logger.error(f"[ERROR] SmartAPI authentication failed: {data.get('message', 'Unknown error')}")
                return False
                
        except Exception as e:
            logger.error(f"[ERROR] Failed to initialize SmartAPI client: {e}")
            return False
    
    async def start(self):
        """Start the market data agent"""
        try:
            logger.info("[START] Starting Market Data Agent...")
            
            # Download historical data for selected stocks
            await self._download_historical_data()
            
            # Initialize WebSocket connection
            self._initialize_websocket()
            
            # Start data processing loop
            await self._start_data_processing_loop()
            
        except Exception as e:
            logger.error(f"[ERROR] Error starting Market Data Agent: {e}")
    
    async def _download_historical_data(self):
        """Download historical data for selected stocks"""
        try:
            logger.info(f"[DATA] Downloading historical data for {len(self.config.selected_stocks)} stocks...")
            
            # Calculate date range (40 days back)
            end_date = datetime.now()
            start_date = end_date - timedelta(days=40)
            
            for symbol in self.config.selected_stocks:
                try:
                    # Get instrument details
                    instrument = await self.instrument_master.get_instrument(symbol, "NSE")
                    if not instrument:
                        logger.warning(f"[WARN] Instrument not found for {symbol}")
                        continue
                    
                    symbol_token = instrument['token']
                    
                    # Download data in batches (30-day limit per request)
                    await self._download_symbol_data(symbol, symbol_token, start_date, end_date)
                    
                    # Generate higher timeframes
                    await self._generate_higher_timeframes(symbol)
                    
                    logger.info(f"[SUCCESS] Downloaded data for {symbol}")
                    
                    # Rate limiting
                    await asyncio.sleep(0.5)
                    
                except Exception as e:
                    logger.error(f"[ERROR] Failed to download data for {symbol}: {e}")
                    continue
            
            logger.info("[SUCCESS] Historical data download completed")
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to download historical data: {e}")
    
    async def _download_symbol_data(self, symbol: str, symbol_token: str, start_date: datetime, end_date: datetime):
        """Download data for a specific symbol"""
        try:
            # Download in 30-day batches
            current_start = start_date
            symbol_data = []
            
            while current_start < end_date:
                batch_end = min(current_start + timedelta(days=30), end_date)
                
                # Format dates properly for SmartAPI
                from_date = current_start.strftime("%Y-%m-%d %H:%M")
                to_date = batch_end.strftime("%Y-%m-%d %H:%M")
                
                try:
                    # Download 1-minute data
                    hist_data = self.smartapi_client.getCandleData({
                        "exchange": "NSE",
                        "symboltoken": symbol_token,
                        "interval": "ONE_MINUTE",
                        "fromdate": from_date,
                        "todate": to_date
                    })
                    
                    if hist_data['status'] and hist_data['data']:
                        batch_data = hist_data['data']
                        symbol_data.extend(batch_data)
                        logger.debug(f"[SUCCESS] Downloaded {len(batch_data)} candles for {symbol}")
                    
                    # Rate limiting
                    await asyncio.sleep(0.5)
                    
                except Exception as e:
                    logger.error(f"[ERROR] Failed to download batch for {symbol}: {e}")
                    break
                
                current_start = batch_end
            
            if symbol_data:
                # Convert to DataFrame with proper date handling
                df_data = []
                for candle in symbol_data:
                    try:
                        # Handle different date formats from SmartAPI
                        timestamp_str = candle[0]
                        if 'T' in timestamp_str:
                            # ISO format: 2025-01-31T09:15:00+05:30
                            timestamp = datetime.fromisoformat(timestamp_str.replace('+05:30', ''))
                        else:
                            # Simple format: 2025-01-31 09:15:00
                            timestamp = datetime.strptime(timestamp_str, "%Y-%m-%d %H:%M:%S")
                        
                        df_data.append({
                            'timestamp': timestamp,
                            'open': float(candle[1]),
                            'high': float(candle[2]),
                            'low': float(candle[3]),
                            'close': float(candle[4]),
                            'volume': int(candle[5])
                        })
                    except Exception as e:
                        logger.warning(f"[WARN] Failed to parse candle data: {e}")
                        continue
                
                # Store 1-minute data
                if df_data:
                    self.timeframe_data["1min"][symbol] = pl.DataFrame(df_data)
                    logger.info(f"[SUCCESS] Stored {len(df_data)} 1-min candles for {symbol}")
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to download symbol data for {symbol}: {e}")
    
    async def _generate_higher_timeframes(self, symbol: str):
        """Generate higher timeframes from 1-minute data"""
        try:
            if symbol not in self.timeframe_data["1min"]:
                return
            
            df_1min = self.timeframe_data["1min"][symbol]
            
            # Generate different timeframes
            timeframe_configs = {
                "2min": "2m",
                "3min": "3m",
                "5min": "5m",
                "10min": "10m",
                "15min": "15m",
                "30min": "30m",
                "1hr": "1h"
            }
            
            for tf_name, tf_period in timeframe_configs.items():
                try:
                    df_tf = df_1min.group_by_dynamic(
                        "timestamp",
                        every=tf_period,
                        closed="left"
                    ).agg([
                        pl.col("open").first(),
                        pl.col("high").max(),
                        pl.col("low").min(),
                        pl.col("close").last(),
                        pl.col("volume").sum()
                    ])
                    
                    self.timeframe_data[tf_name][symbol] = df_tf
                    logger.debug(f"[SUCCESS] Generated {tf_name} data for {symbol}")
                    
                except Exception as e:
                    logger.warning(f"[WARN] Failed to generate {tf_name} for {symbol}: {e}")
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to generate higher timeframes for {symbol}: {e}")
    
    def _initialize_websocket(self):
        """Initialize WebSocket connection with proper handling"""
        try:
            if not self.auth_token or not self.feed_token:
                logger.error("[ERROR] Authentication tokens not available for WebSocket")
                return False
            
            logger.info("[WEBSOCKET] Initializing WebSocket connection...")
            
            # Create WebSocket client
            self.websocket_client = SmartWebSocketV2(
                auth_token=self.auth_token,
                api_key=self.api_key,
                client_code=self.username,
                feed_token=self.feed_token
            )
            
            # Set up callbacks
            self.websocket_client.on_open = self._on_websocket_open
            self.websocket_client.on_data = self._on_websocket_data
            self.websocket_client.on_error = self._on_websocket_error
            self.websocket_client.on_close = self._on_websocket_close
            
            # Connect in a separate thread (SmartAPI WebSocket is not async)
            def connect_websocket():
                try:
                    self.websocket_client.connect()
                except Exception as e:
                    logger.error(f"[ERROR] WebSocket connection failed: {e}")
            
            # Start WebSocket connection in thread
            websocket_thread = threading.Thread(target=connect_websocket, daemon=True)
            websocket_thread.start()
            
            # Give some time for connection
            time.sleep(3)
            
            if self.websocket_connected:
                # Subscribe to symbols
                self._subscribe_to_symbols()
                logger.info("[SUCCESS] WebSocket connected and subscribed")
                return True
            else:
                logger.error("[ERROR] WebSocket connection failed")
                return False
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to initialize WebSocket: {e}")
            return False
    
    def _on_websocket_open(self, ws):
        """WebSocket open callback"""
        logger.info("[WEBSOCKET] Connection opened")
        self.websocket_connected = True
        self.reconnect_attempts = 0
    
    def _on_websocket_data(self, ws, message):
        """WebSocket data callback"""
        try:
            if isinstance(message, dict):
                symbol_token = message.get('tk')
                ltp = message.get('lp')
                
                if symbol_token and ltp:
                    # Find symbol by token
                    symbol = self._get_symbol_by_token(symbol_token)
                    if symbol:
                        # Create market data point
                        data_point = MarketDataPoint(
                            symbol=symbol,
                            timestamp=datetime.now(),
                            open=message.get('o', ltp),
                            high=message.get('h', ltp),
                            low=message.get('l', ltp),
                            close=ltp,
                            volume=message.get('v', 0),
                            ltp=ltp
                        )
                        
                        # Store real-time data
                        self.market_data[symbol] = data_point
                        
                        # Publish market data event
                        event = Event(
                            type="MARKET_DATA_UPDATE",
                            data={
                                "symbol": symbol,
                                "data_point": data_point,
                                "timestamp": datetime.now()
                            },
                            source=self.name
                        )
                        self.event_bus.publish(event)
            
        except Exception as e:
            logger.error(f"[ERROR] WebSocket data processing error: {e}")
    
    def _on_websocket_error(self, ws, error):
        """WebSocket error callback"""
        logger.error(f"[WEBSOCKET] Error: {error}")
        self.websocket_connected = False
        
        # Attempt reconnection
        if self.reconnect_attempts < self.max_reconnect_attempts:
            self.reconnect_attempts += 1
            logger.info(f"[WEBSOCKET] Attempting reconnection {self.reconnect_attempts}/{self.max_reconnect_attempts}")
            # Schedule reconnection
            asyncio.create_task(self._reconnect_websocket())
    
    def _on_websocket_close(self, ws):
        """WebSocket close callback"""
        logger.info("[WEBSOCKET] Connection closed")
        self.websocket_connected = False
    
    async def _reconnect_websocket(self):
        """Reconnect WebSocket with exponential backoff"""
        try:
            # Wait before reconnecting (exponential backoff)
            wait_time = min(2 ** self.reconnect_attempts, 60)
            await asyncio.sleep(wait_time)
            
            # Attempt reconnection
            self._initialize_websocket()
            
        except Exception as e:
            logger.error(f"[ERROR] WebSocket reconnection failed: {e}")
    
    def _subscribe_to_symbols(self):
        """Subscribe to symbols for real-time data"""
        try:
            # Prepare subscription data
            token_list = []
            
            for symbol in self.config.selected_stocks:
                # Use synchronous call since we're in a sync context
                key = f"{symbol}_NSE"
                if key in self.instrument_master.symbol_token_map:
                    instrument = self.instrument_master.symbol_token_map[key]
                    token_list.append({
                        "exchangeType": 1,  # NSE
                        "tokens": [instrument['token']]
                    })
            
            if token_list:
                # Subscribe to LTP mode
                correlation_id = f"subscription_{self.session_id}"
                mode = 1  # LTP mode
                
                self.websocket_client.subscribe(correlation_id, mode, token_list)
                logger.info(f"[WEBSOCKET] Subscribed to {len(token_list)} symbols")
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to subscribe to symbols: {e}")
    
    def _get_symbol_by_token(self, token: str) -> Optional[str]:
        """Get symbol name by token"""
        try:
            return self.instrument_master.get_symbol_by_token(token)
        except Exception as e:
            logger.error(f"[ERROR] Failed to get symbol by token {token}: {e}")
            return None
    
    async def _start_data_processing_loop(self):
        """Start the main data processing loop"""
        try:
            logger.info("[LOOP] Starting data processing loop...")
            
            while self.running:
                try:
                    # Process any pending data updates
                    await self._process_data_updates()
                    
                    # Check WebSocket connection health
                    if not self.websocket_connected and self.reconnect_attempts < self.max_reconnect_attempts:
                        asyncio.create_task(self._reconnect_websocket())
                    
                    # Sleep for a short interval
                    await asyncio.sleep(1)
                    
                except Exception as e:
                    logger.error(f"[ERROR] Error in data processing loop: {e}")
                    await asyncio.sleep(5)
            
            logger.info("[LOOP] Data processing loop ended")
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to start data processing loop: {e}")
    
    async def _process_data_updates(self):
        """Process any pending data updates"""
        try:
            # Update 1-minute candles with real-time data
            for symbol, data_point in self.market_data.items():
                await self._update_realtime_candle(symbol, data_point)
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to process data updates: {e}")
    
    async def _update_realtime_candle(self, symbol: str, data_point: MarketDataPoint):
        """Update real-time 1-minute candle data"""
        try:
            if symbol not in self.timeframe_data["1min"]:
                return
            
            # Get current 1-minute candle timestamp
            current_minute = data_point.timestamp.replace(second=0, microsecond=0)
            
            # Update or create new candle
            df = self.timeframe_data["1min"][symbol]
            
            # Check if we need to create a new candle or update existing
            latest_candle = df.tail(1)
            if len(latest_candle) > 0:
                latest_timestamp = latest_candle.select(pl.col("timestamp")).item()
                
                if latest_timestamp == current_minute:
                    # Update existing candle
                    df = df.with_columns([
                        pl.when(pl.col("timestamp") == current_minute)
                        .then(data_point.high)
                        .otherwise(pl.col("high"))
                        .alias("high"),
                        
                        pl.when(pl.col("timestamp") == current_minute)
                        .then(pl.min_horizontal([pl.col("low"), data_point.low]))
                        .otherwise(pl.col("low"))
                        .alias("low"),
                        
                        pl.when(pl.col("timestamp") == current_minute)
                        .then(data_point.close)
                        .otherwise(pl.col("close"))
                        .alias("close"),
                        
                        pl.when(pl.col("timestamp") == current_minute)
                        .then(pl.col("volume") + data_point.volume)
                        .otherwise(pl.col("volume"))
                        .alias("volume")
                    ])
                else:
                    # Create new candle
                    new_candle = pl.DataFrame([{
                        'timestamp': current_minute,
                        'open': data_point.open,
                        'high': data_point.high,
                        'low': data_point.low,
                        'close': data_point.close,
                        'volume': data_point.volume
                    }])
                    df = pl.concat([df, new_candle])
            
            # Update stored data
            self.timeframe_data["1min"][symbol] = df
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to update realtime candle for {symbol}: {e}")
    
    async def _handle_historical_data_request(self, event: Event):
        """Handle historical data request from other agents"""
        try:
            symbol = event.data.get('symbol')
            timeframe = event.data.get('timeframe', '1min')
            
            if symbol in self.timeframe_data.get(timeframe, {}):
                data = self.timeframe_data[timeframe][symbol]
                
                # Publish response
                response_event = Event(
                    type="HISTORICAL_DATA_RESPONSE",
                    data={
                        "symbol": symbol,
                        "timeframe": timeframe,
                        "data": data,
                        "request_id": event.data.get('request_id')
                    },
                    source=self.name
                )
                self.event_bus.publish(response_event)
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to handle historical data request: {e}")
    
    async def _handle_realtime_data_request(self, event: Event):
        """Handle real-time data request from other agents"""
        try:
            symbol = event.data.get('symbol')
            
            if symbol in self.market_data:
                data_point = self.market_data[symbol]
                
                # Publish response
                response_event = Event(
                    type="REALTIME_DATA_RESPONSE",
                    data={
                        "symbol": symbol,
                        "data_point": data_point,
                        "request_id": event.data.get('request_id')
                    },
                    source=self.name
                )
                self.event_bus.publish(response_event)
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to handle realtime data request: {e}")
    
    async def stop(self):
        """Stop the market data agent"""
        try:
            logger.info("[STOP] Stopping Market Data Agent...")
            
            self.running = False
            
            # Close WebSocket connection
            if self.websocket_client:
                self.websocket_client.close_connection()
            
            logger.info("[SUCCESS] Market Data Agent stopped")
            
        except Exception as e:
            logger.error(f"[ERROR] Error stopping Market Data Agent: {e}")