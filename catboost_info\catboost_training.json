{"meta": {"test_sets": [], "test_metrics": [], "learn_metrics": [{"best_value": "Min", "name": "RMSE"}], "launch_mode": "Train", "parameters": "", "iteration_count": 701, "learn_sets": ["learn"], "name": "experiment"}, "iterations": [{"learn": [0.5787115472], "iteration": 0, "passed_time": 0.01312872587, "remaining_time": 9.190108109}, {"learn": [0.5373783063], "iteration": 1, "passed_time": 0.04194166629, "remaining_time": 14.65861237}, {"learn": [0.512737396], "iteration": 2, "passed_time": 0.08713950837, "remaining_time": 20.27445895}, {"learn": [0.4803730407], "iteration": 3, "passed_time": 0.1137341501, "remaining_time": 19.81817565}, {"learn": [0.4512814004], "iteration": 4, "passed_time": 0.1265628946, "remaining_time": 17.61755493}, {"learn": [0.4287258733], "iteration": 5, "passed_time": 0.1672905866, "remaining_time": 19.37782628}, {"learn": [0.3998262795], "iteration": 6, "passed_time": 0.1741460275, "remaining_time": 17.26533473}, {"learn": [0.3746931433], "iteration": 7, "passed_time": 0.1843432152, "remaining_time": 15.96873102}, {"learn": [0.3580791377], "iteration": 8, "passed_time": 0.2015582961, "remaining_time": 15.49759344}, {"learn": [0.3428326044], "iteration": 9, "passed_time": 0.2186447768, "remaining_time": 15.10835408}, {"learn": [0.3264819679], "iteration": 10, "passed_time": 0.2766929756, "remaining_time": 17.35619574}, {"learn": [0.3156819606], "iteration": 11, "passed_time": 0.2945519174, "remaining_time": 16.91218925}, {"learn": [0.3032133611], "iteration": 12, "passed_time": 0.3060121203, "remaining_time": 16.19510298}, {"learn": [0.2928478464], "iteration": 13, "passed_time": 0.3650852417, "remaining_time": 17.91525436}, {"learn": [0.2844951753], "iteration": 14, "passed_time": 0.4268030472, "remaining_time": 19.51912602}, {"learn": [0.2774237473], "iteration": 15, "passed_time": 0.4854420383, "remaining_time": 20.78298727}, {"learn": [0.2661477083], "iteration": 16, "passed_time": 0.5441532144, "remaining_time": 21.89416463}, {"learn": [0.2561984047], "iteration": 17, "passed_time": 0.5704692959, "remaining_time": 21.6461405}, {"learn": [0.2493668099], "iteration": 18, "passed_time": 0.6295444408, "remaining_time": 22.59733203}, {"learn": [0.2430807333], "iteration": 19, "passed_time": 0.6886967998, "remaining_time": 23.45012603}, {"learn": [0.2352414238], "iteration": 20, "passed_time": 0.7477184449, "remaining_time": 24.21183536}, {"learn": [0.227110749], "iteration": 21, "passed_time": 0.7658643323, "remaining_time": 23.63735826}, {"learn": [0.2195806303], "iteration": 22, "passed_time": 0.8252543247, "remaining_time": 24.32706227}, {"learn": [0.2127284561], "iteration": 23, "passed_time": 0.8673468227, "remaining_time": 24.46640829}, {"learn": [0.2043070303], "iteration": 24, "passed_time": 0.9269783958, "remaining_time": 25.06549582}, {"learn": [0.1995300404], "iteration": 25, "passed_time": 0.9864463323, "remaining_time": 25.6096644}, {"learn": [0.1948367291], "iteration": 26, "passed_time": 1.013622841, "remaining_time": 25.30302943}, {"learn": [0.1900752979], "iteration": 27, "passed_time": 1.073224468, "remaining_time": 25.79571668}, {"learn": [0.1857114363], "iteration": 28, "passed_time": 1.132813462, "remaining_time": 26.25002229}, {"learn": [0.1814266298], "iteration": 29, "passed_time": 1.195682606, "remaining_time": 26.74343428}, {"learn": [0.177043879], "iteration": 30, "passed_time": 1.255730375, "remaining_time": 27.13997908}, {"learn": [0.1731000197], "iteration": 31, "passed_time": 1.315490418, "remaining_time": 27.50197156}, {"learn": [0.1687745193], "iteration": 32, "passed_time": 1.375295205, "remaining_time": 27.83930899}, {"learn": [0.1645961115], "iteration": 33, "passed_time": 1.43474047, "remaining_time": 28.14623216}, {"learn": [0.161171972], "iteration": 34, "passed_time": 1.462065685, "remaining_time": 27.82102131}]}