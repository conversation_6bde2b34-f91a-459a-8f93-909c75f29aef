#!/usr/bin/env python3
"""
CLEAN SIGNAL GENERATION AGENT
Modern signal generation with proper async handling

Features:
- Clean async/await patterns
- Modern trading strategies
- Event-driven signal publishing
- Proper error handling
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import numpy as np
import polars as pl
from dataclasses import dataclass

# Import polars-talib for faster technical indicators
try:
    import polars_talib as ta
    POLARS_TALIB_AVAILABLE = True
except ImportError:
    POLARS_TALIB_AVAILABLE = False
    print("[WARN] polars-talib not available. Install with: pip install polars-talib")

from core.base_agent import BaseAgent
from core.event_system import EventBus, EventTypes

logger = logging.getLogger(__name__)

@dataclass
class TradingSignal:
    """Trading signal data structure"""
    symbol: str
    signal_type: str  # BUY, SELL, HOLD
    strength: float   # 0.0 to 1.0
    entry_price: float
    stop_loss: float
    target_price: float
    strategy_name: str
    timeframe: str
    timestamp: datetime
    confidence: float
    metadata: Dict[str, Any]

class CleanSignalAgent(BaseAgent):
    """
    Clean Signal Generation Agent
    
    Features:
    - Modern trading strategies
    - Clean async patterns
    - Event-driven architecture
    """
    
    def __init__(self, event_bus: EventBus, config: Any, session_id: str):
        super().__init__("CleanSignalAgent", event_bus, config, session_id)
        
        # Signal generation state
        self.active_signals = {}
        self.signal_history = []
        
        # Market data cache
        self.market_data_cache = {}
        self.historical_data_cache = {}
        
        # Strategy configurations
        self.strategies = {
            'rsi_reversal': {'enabled': True, 'weight': 0.3},
            'ma_crossover': {'enabled': True, 'weight': 0.25},
            'bollinger_bands': {'enabled': True, 'weight': 0.2},
            'macd_signal': {'enabled': True, 'weight': 0.15},
            'volume_breakout': {'enabled': True, 'weight': 0.1}
        }
        
        # Configuration
        self.selected_stocks = getattr(config, 'selected_stocks', [
            "RELIANCE", "HDFCBANK", "TCS", "INFY", "ICICIBANK"
        ])
        
        self.min_signal_strength = 0.6
        self.signal_cooldown_minutes = 15
    
    async def initialize(self) -> bool:
        """Initialize the signal generation agent"""
        try:
            self.log_info("Initializing Clean Signal Agent...")
            
            # Subscribe to market data events
            self.event_bus.subscribe(EventTypes.MARKET_DATA_RECEIVED, self._handle_market_data)
            self.event_bus.subscribe(EventTypes.HISTORICAL_DATA_LOADED, self._handle_historical_data)
            
            self.initialized = True
            self.log_info("Clean Signal Agent initialized successfully")
            return True
            
        except Exception as e:
            self.log_error(f"Failed to initialize: {e}")
            return False
    
    async def start(self):
        """Start the signal generation agent"""
        try:
            self.log_info("Starting Clean Signal Agent...")
            
            # Start signal generation loop
            await self._start_signal_loop()
            
        except Exception as e:
            self.log_error(f"Error starting agent: {e}")
    
    async def _start_signal_loop(self):
        """Start the main signal generation loop"""
        try:
            self.log_info("Starting signal generation loop...")
            
            while self.running:
                try:
                    # Generate signals for all stocks
                    for symbol in self.selected_stocks:
                        await self._generate_signals_for_symbol(symbol)
                    
                    # Clean up old signals
                    await self._cleanup_old_signals()
                    
                    # Sleep for interval
                    await asyncio.sleep(30)  # Generate signals every 30 seconds
                    
                except Exception as e:
                    self.log_error(f"Error in signal generation loop: {e}")
                    await asyncio.sleep(5)
            
            self.log_info("Signal generation loop ended")
            
        except Exception as e:
            self.log_error(f"Failed to start signal generation loop: {e}")
    
    async def _generate_signals_for_symbol(self, symbol: str):
        """Generate signals for a specific symbol"""
        try:
            # Check if we have recent data
            if symbol not in self.historical_data_cache:
                await self._request_historical_data(symbol)
                return
            
            # Check signal cooldown
            if self._is_in_cooldown(symbol):
                return
            
            historical_data = self.historical_data_cache[symbol]
            if historical_data is None or len(historical_data) < 50:
                return
            
            # Generate signals using different strategies
            signals = []
            
            for strategy_name, config in self.strategies.items():
                if not config['enabled']:
                    continue
                
                try:
                    signal = await self._apply_strategy(strategy_name, symbol, historical_data)
                    if signal:
                        signals.append(signal)
                except Exception as e:
                    self.log_error(f"Error applying strategy {strategy_name} for {symbol}: {e}")
            
            # Combine signals
            if signals:
                combined_signal = await self._combine_signals(symbol, signals)
                if combined_signal and combined_signal.strength >= self.min_signal_strength:
                    await self._publish_signal(combined_signal)
            
        except Exception as e:
            self.log_error(f"Failed to generate signals for {symbol}: {e}")
    
    async def _apply_strategy(self, strategy_name: str, symbol: str, data: pl.DataFrame) -> Optional[TradingSignal]:
        """Apply a specific trading strategy"""
        try:
            if strategy_name == 'rsi_reversal':
                return await self._rsi_strategy(symbol, data)
            elif strategy_name == 'ma_crossover':
                return await self._ma_crossover_strategy(symbol, data)
            elif strategy_name == 'bollinger_bands':
                return await self._bollinger_strategy(symbol, data)
            elif strategy_name == 'macd_signal':
                return await self._macd_strategy(symbol, data)
            elif strategy_name == 'volume_breakout':
                return await self._volume_strategy(symbol, data)
            
            return None
            
        except Exception as e:
            self.log_error(f"Error applying strategy {strategy_name}: {e}")
            return None
    
    async def _rsi_strategy(self, symbol: str, data: pl.DataFrame) -> Optional[TradingSignal]:
        """RSI reversal strategy"""
        try:
            # Calculate RSI using polars-talib if available, otherwise fallback
            if POLARS_TALIB_AVAILABLE:
                data = data.with_columns([
                    ta.rsi(pl.col("close"), timeperiod=14).alias("rsi")
                ])
            else:
                data = data.with_columns([
                    self._calculate_rsi(pl.col("close"), 14).alias("rsi")
                ])
            
            latest = data.tail(1).to_dicts()[0]
            current_price = latest['close']
            rsi = latest['rsi']
            
            if rsi is None:
                return None
            
            # Generate signals
            if rsi < 30:  # Oversold
                return TradingSignal(
                    symbol=symbol,
                    signal_type="BUY",
                    strength=min((30 - rsi) / 10, 1.0),
                    entry_price=current_price,
                    stop_loss=current_price * 0.98,
                    target_price=current_price * 1.04,
                    strategy_name="rsi_reversal",
                    timeframe="1min",
                    timestamp=datetime.now(),
                    confidence=0.7,
                    metadata={"rsi": rsi}
                )
            elif rsi > 70:  # Overbought
                return TradingSignal(
                    symbol=symbol,
                    signal_type="SELL",
                    strength=min((rsi - 70) / 10, 1.0),
                    entry_price=current_price,
                    stop_loss=current_price * 1.02,
                    target_price=current_price * 0.96,
                    strategy_name="rsi_reversal",
                    timeframe="1min",
                    timestamp=datetime.now(),
                    confidence=0.7,
                    metadata={"rsi": rsi}
                )
            
            return None
            
        except Exception as e:
            self.log_error(f"Error in RSI strategy: {e}")
            return None
    
    async def _ma_crossover_strategy(self, symbol: str, data: pl.DataFrame) -> Optional[TradingSignal]:
        """Moving average crossover strategy"""
        try:
            # Calculate moving averages using polars-talib if available
            if POLARS_TALIB_AVAILABLE:
                data = data.with_columns([
                    ta.sma(pl.col("close"), timeperiod=9).alias("ma_fast"),
                    ta.sma(pl.col("close"), timeperiod=21).alias("ma_slow")
                ])
            else:
                data = data.with_columns([
                    pl.col("close").rolling_mean(window_size=9).alias("ma_fast"),
                    pl.col("close").rolling_mean(window_size=21).alias("ma_slow")
                ])
            
            # Get recent data for crossover detection
            recent = data.tail(3).to_dicts()
            if len(recent) < 3:
                return None
            
            current = recent[-1]
            previous = recent[-2]
            
            current_price = current['close']
            ma_fast_current = current['ma_fast']
            ma_slow_current = current['ma_slow']
            ma_fast_prev = previous['ma_fast']
            ma_slow_prev = previous['ma_slow']
            
            if None in [ma_fast_current, ma_slow_current, ma_fast_prev, ma_slow_prev]:
                return None
            
            # Detect crossover
            if ma_fast_prev <= ma_slow_prev and ma_fast_current > ma_slow_current:
                # Bullish crossover
                strength = min(abs(ma_fast_current - ma_slow_current) / ma_slow_current * 10, 1.0)
                return TradingSignal(
                    symbol=symbol,
                    signal_type="BUY",
                    strength=strength,
                    entry_price=current_price,
                    stop_loss=current_price * 0.97,
                    target_price=current_price * 1.05,
                    strategy_name="ma_crossover",
                    timeframe="1min",
                    timestamp=datetime.now(),
                    confidence=0.6,
                    metadata={"ma_fast": ma_fast_current, "ma_slow": ma_slow_current}
                )
            elif ma_fast_prev >= ma_slow_prev and ma_fast_current < ma_slow_current:
                # Bearish crossover
                strength = min(abs(ma_fast_current - ma_slow_current) / ma_slow_current * 10, 1.0)
                return TradingSignal(
                    symbol=symbol,
                    signal_type="SELL",
                    strength=strength,
                    entry_price=current_price,
                    stop_loss=current_price * 1.03,
                    target_price=current_price * 0.95,
                    strategy_name="ma_crossover",
                    timeframe="1min",
                    timestamp=datetime.now(),
                    confidence=0.6,
                    metadata={"ma_fast": ma_fast_current, "ma_slow": ma_slow_current}
                )
            
            return None
            
        except Exception as e:
            self.log_error(f"Error in MA crossover strategy: {e}")
            return None
    
    async def _bollinger_strategy(self, symbol: str, data: pl.DataFrame) -> Optional[TradingSignal]:
        """Bollinger Bands strategy"""
        try:
            # Calculate Bollinger Bands
            data = data.with_columns([
                pl.col("close").rolling_mean(window_size=20).alias("bb_middle"),
                pl.col("close").rolling_std(window_size=20).alias("bb_std")
            ]).with_columns([
                (pl.col("bb_middle") + 2 * pl.col("bb_std")).alias("bb_upper"),
                (pl.col("bb_middle") - 2 * pl.col("bb_std")).alias("bb_lower")
            ])
            
            latest = data.tail(1).to_dicts()[0]
            current_price = latest['close']
            bb_upper = latest['bb_upper']
            bb_lower = latest['bb_lower']
            bb_middle = latest['bb_middle']
            
            if None in [bb_upper, bb_lower, bb_middle]:
                return None
            
            # Generate signals
            if current_price <= bb_lower:
                # Price at lower band
                strength = min((bb_lower - current_price) / bb_lower * 20, 1.0)
                return TradingSignal(
                    symbol=symbol,
                    signal_type="BUY",
                    strength=strength,
                    entry_price=current_price,
                    stop_loss=current_price * 0.98,
                    target_price=bb_middle,
                    strategy_name="bollinger_bands",
                    timeframe="1min",
                    timestamp=datetime.now(),
                    confidence=0.65,
                    metadata={"bb_upper": bb_upper, "bb_lower": bb_lower}
                )
            elif current_price >= bb_upper:
                # Price at upper band
                strength = min((current_price - bb_upper) / bb_upper * 20, 1.0)
                return TradingSignal(
                    symbol=symbol,
                    signal_type="SELL",
                    strength=strength,
                    entry_price=current_price,
                    stop_loss=current_price * 1.02,
                    target_price=bb_middle,
                    strategy_name="bollinger_bands",
                    timeframe="1min",
                    timestamp=datetime.now(),
                    confidence=0.65,
                    metadata={"bb_upper": bb_upper, "bb_lower": bb_lower}
                )
            
            return None
            
        except Exception as e:
            self.log_error(f"Error in Bollinger Bands strategy: {e}")
            return None
    
    async def _macd_strategy(self, symbol: str, data: pl.DataFrame) -> Optional[TradingSignal]:
        """MACD strategy"""
        try:
            # Calculate MACD
            data = data.with_columns([
                pl.col("close").ewm_mean(span=12).alias("ema_12"),
                pl.col("close").ewm_mean(span=26).alias("ema_26")
            ]).with_columns([
                (pl.col("ema_12") - pl.col("ema_26")).alias("macd_line")
            ]).with_columns([
                pl.col("macd_line").ewm_mean(span=9).alias("signal_line")
            ])
            
            # Get recent data
            recent = data.tail(3).to_dicts()
            if len(recent) < 3:
                return None
            
            current = recent[-1]
            previous = recent[-2]
            
            current_price = current['close']
            macd_current = current['macd_line']
            signal_current = current['signal_line']
            macd_prev = previous['macd_line']
            signal_prev = previous['signal_line']
            
            if None in [macd_current, signal_current, macd_prev, signal_prev]:
                return None
            
            # Detect crossover
            if macd_prev <= signal_prev and macd_current > signal_current:
                # Bullish crossover
                strength = min(abs(macd_current - signal_current) * 100, 1.0)
                return TradingSignal(
                    symbol=symbol,
                    signal_type="BUY",
                    strength=strength,
                    entry_price=current_price,
                    stop_loss=current_price * 0.97,
                    target_price=current_price * 1.06,
                    strategy_name="macd_signal",
                    timeframe="1min",
                    timestamp=datetime.now(),
                    confidence=0.7,
                    metadata={"macd": macd_current, "signal": signal_current}
                )
            elif macd_prev >= signal_prev and macd_current < signal_current:
                # Bearish crossover
                strength = min(abs(macd_current - signal_current) * 100, 1.0)
                return TradingSignal(
                    symbol=symbol,
                    signal_type="SELL",
                    strength=strength,
                    entry_price=current_price,
                    stop_loss=current_price * 1.03,
                    target_price=current_price * 0.94,
                    strategy_name="macd_signal",
                    timeframe="1min",
                    timestamp=datetime.now(),
                    confidence=0.7,
                    metadata={"macd": macd_current, "signal": signal_current}
                )
            
            return None
            
        except Exception as e:
            self.log_error(f"Error in MACD strategy: {e}")
            return None
    
    async def _volume_strategy(self, symbol: str, data: pl.DataFrame) -> Optional[TradingSignal]:
        """Volume breakout strategy"""
        try:
            # Calculate volume average
            data = data.with_columns([
                pl.col("volume").rolling_mean(window_size=20).alias("volume_ma")
            ])
            
            latest = data.tail(1).to_dicts()[0]
            current_price = latest['close']
            current_volume = latest['volume']
            volume_ma = latest['volume_ma']
            
            if volume_ma is None or volume_ma == 0:
                return None
            
            # Check for volume breakout
            volume_ratio = current_volume / volume_ma
            
            if volume_ratio > 2.0:
                # High volume - determine direction
                recent_prices = data.tail(5).select(pl.col("close")).to_series().to_list()
                if len(recent_prices) >= 2:
                    price_change = (recent_prices[-1] - recent_prices[0]) / recent_prices[0]
                    
                    if price_change > 0.01:  # 1% increase
                        strength = min(volume_ratio / 5.0, 1.0)
                        return TradingSignal(
                            symbol=symbol,
                            signal_type="BUY",
                            strength=strength,
                            entry_price=current_price,
                            stop_loss=current_price * 0.96,
                            target_price=current_price * 1.08,
                            strategy_name="volume_breakout",
                            timeframe="1min",
                            timestamp=datetime.now(),
                            confidence=0.6,
                            metadata={"volume_ratio": volume_ratio}
                        )
                    elif price_change < -0.01:  # 1% decrease
                        strength = min(volume_ratio / 5.0, 1.0)
                        return TradingSignal(
                            symbol=symbol,
                            signal_type="SELL",
                            strength=strength,
                            entry_price=current_price,
                            stop_loss=current_price * 1.04,
                            target_price=current_price * 0.92,
                            strategy_name="volume_breakout",
                            timeframe="1min",
                            timestamp=datetime.now(),
                            confidence=0.6,
                            metadata={"volume_ratio": volume_ratio}
                        )
            
            return None
            
        except Exception as e:
            self.log_error(f"Error in volume strategy: {e}")
            return None
    
    def _calculate_rsi(self, close_col, period: int = 14):
        """Calculate RSI using Polars expressions"""
        try:
            delta = close_col.diff()
            gain = delta.clip_min(0)
            loss = (-delta).clip_min(0)
            
            avg_gain = gain.rolling_mean(window_size=period)
            avg_loss = loss.rolling_mean(window_size=period)
            
            rs = avg_gain / avg_loss
            rsi = 100 - (100 / (1 + rs))
            
            return rsi
            
        except Exception as e:
            self.log_error(f"Error calculating RSI: {e}")
            return pl.lit(None)
    
    async def _combine_signals(self, symbol: str, signals: List[TradingSignal]) -> Optional[TradingSignal]:
        """Combine multiple signals"""
        try:
            if not signals:
                return None
            
            # Group by type
            buy_signals = [s for s in signals if s.signal_type == "BUY"]
            sell_signals = [s for s in signals if s.signal_type == "SELL"]
            
            # Calculate weighted scores
            buy_score = sum(s.strength * self.strategies[s.strategy_name]['weight'] for s in buy_signals)
            sell_score = sum(s.strength * self.strategies[s.strategy_name]['weight'] for s in sell_signals)
            
            # Determine final signal
            if buy_score > sell_score and buy_score > 0.3:
                strongest_buy = max(buy_signals, key=lambda s: s.strength)
                return TradingSignal(
                    symbol=symbol,
                    signal_type="BUY",
                    strength=min(buy_score, 1.0),
                    entry_price=strongest_buy.entry_price,
                    stop_loss=strongest_buy.stop_loss,
                    target_price=strongest_buy.target_price,
                    strategy_name="combined",
                    timeframe="1min",
                    timestamp=datetime.now(),
                    confidence=min(sum(s.confidence for s in buy_signals) / len(buy_signals), 1.0),
                    metadata={"strategies": [s.strategy_name for s in buy_signals]}
                )
            elif sell_score > buy_score and sell_score > 0.3:
                strongest_sell = max(sell_signals, key=lambda s: s.strength)
                return TradingSignal(
                    symbol=symbol,
                    signal_type="SELL",
                    strength=min(sell_score, 1.0),
                    entry_price=strongest_sell.entry_price,
                    stop_loss=strongest_sell.stop_loss,
                    target_price=strongest_sell.target_price,
                    strategy_name="combined",
                    timeframe="1min",
                    timestamp=datetime.now(),
                    confidence=min(sum(s.confidence for s in sell_signals) / len(sell_signals), 1.0),
                    metadata={"strategies": [s.strategy_name for s in sell_signals]}
                )
            
            return None
            
        except Exception as e:
            self.log_error(f"Error combining signals: {e}")
            return None
    
    async def _publish_signal(self, signal: TradingSignal):
        """Publish a trading signal"""
        try:
            # Store signal
            self.active_signals[signal.symbol] = signal
            self.signal_history.append(signal)
            
            # Publish signal event
            await self.event_bus.publish(
                EventTypes.SIGNAL_GENERATED,
                {
                    "signal": signal,
                    "symbol": signal.symbol,
                    "signal_type": signal.signal_type,
                    "strength": signal.strength,
                    "strategy": signal.strategy_name
                },
                source=self.name
            )
            
            self.log_info(f"Published {signal.signal_type} signal for {signal.symbol} (strength: {signal.strength:.2f})")
            
        except Exception as e:
            self.log_error(f"Failed to publish signal: {e}")
    
    def _is_in_cooldown(self, symbol: str) -> bool:
        """Check if symbol is in cooldown"""
        try:
            if symbol not in self.active_signals:
                return False
            
            last_signal_time = self.active_signals[symbol].timestamp
            cooldown_end = last_signal_time + timedelta(minutes=self.signal_cooldown_minutes)
            
            return datetime.now() < cooldown_end
            
        except Exception as e:
            self.log_error(f"Error checking cooldown for {symbol}: {e}")
            return False
    
    async def _request_historical_data(self, symbol: str):
        """Request historical data for a symbol"""
        try:
            await self.event_bus.publish(
                "REQUEST_HISTORICAL_DATA",
                {"symbol": symbol, "timeframe": "1min"},
                source=self.name
            )
            
        except Exception as e:
            self.log_error(f"Failed to request historical data for {symbol}: {e}")
    
    async def _cleanup_old_signals(self):
        """Clean up old signals"""
        try:
            cutoff_time = datetime.now() - timedelta(hours=1)
            
            # Remove old active signals
            expired_symbols = [
                symbol for symbol, signal in self.active_signals.items()
                if signal.timestamp < cutoff_time
            ]
            
            for symbol in expired_symbols:
                del self.active_signals[symbol]
            
            # Trim history
            if len(self.signal_history) > 1000:
                self.signal_history = self.signal_history[-1000:]
            
        except Exception as e:
            self.log_error(f"Failed to cleanup old signals: {e}")
    
    async def _handle_market_data(self, event):
        """Handle market data event"""
        try:
            symbol = event.data.get('symbol')
            data = event.data.get('data')
            
            if symbol and data:
                self.market_data_cache[symbol] = data
                self.increment_message_count()
            
        except Exception as e:
            self.log_error(f"Failed to handle market data: {e}")
    
    async def _handle_historical_data(self, event):
        """Handle historical data event"""
        try:
            symbol = event.data.get('symbol')
            data = event.data.get('data')
            
            if symbol and data is not None:
                # Convert data to Polars DataFrame if it's a list of dicts
                if isinstance(data, list) and len(data) > 0:
                    self.historical_data_cache[symbol] = pl.DataFrame(data)
                elif hasattr(data, 'to_dicts'):  # Already a DataFrame
                    self.historical_data_cache[symbol] = data
                else:
                    self.historical_data_cache[symbol] = data
                    
                self.increment_message_count()
                self.log_info(f"Cached historical data for {symbol}: {len(data) if isinstance(data, list) else 'DataFrame'} records")
            
        except Exception as e:
            self.log_error(f"Failed to handle historical data: {e}")
    
    async def stop(self):
        """Stop the signal generation agent"""
        try:
            self.log_info("Stopping Clean Signal Agent...")
            
            self.running = False
            
            self.log_info("Clean Signal Agent stopped")
            
        except Exception as e:
            self.log_error(f"Error stopping agent: {e}")