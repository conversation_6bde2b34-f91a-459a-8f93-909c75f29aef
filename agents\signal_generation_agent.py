#!/usr/bin/env python3
"""
Signal Generation Agent - Real-time Trading Signal Generation and Validation

Features:
📥 1. Input Management
- Live OHLCV Data from Angel One WebSocket or rolling candle aggregator
- Feature DataFrame with real-time indicators (RSI, EMA, VWAP, MACD, SuperTrend, etc.)
- Strategy Config loaded from YAML or database logic
- Market Context (regime, volatility, liquidity, RR preferences)
- Capital Info (per strategy capital, max drawdown, SL/TP constraints)

🧠 2. Signal Evaluation Engine
- Strategy Logic Evaluation using df.eval(), compiled NumExpr, or precompiled AST
- RR Condition Handler with auto-generated SL/TP levels based on RR ratio
- Regime/Condition Aware logic for different market conditions
- Signal Type Detection (Long/Short/Exit) with signal=1, -1, 0
- Position Sizing using Kelly Criterion / fixed fraction / volatility-scaling
- Trade State Tracking to avoid duplicate/flip-flop signals

⚙️ 3. Signal Structuring + Validation
- Signal Packaging with symbol, datetime, strategy, entry price, sl/tp, capital
- Risk Check (hard filters) to reject signals if volatility > X, drawdown > Y
- Liquidity Check to ensure volume or bid/ask spread is tradable
- Time Filter to only allow signals during live market (09:20 – 15:00)
- Signal Cooldown to avoid spamming with N-minute gap between signals

🧾 4. Signal Output
- Internal Database logging for record, dashboard display, backtest
- Real-time Notification push to Telegram / Slack / webhooks
- Execution Queue sending to Order Execution Agent (with Angel API)
- Strategy Monitor feeding into dashboard showing live signal activity
"""

import os
import sys
import asyncio
import logging
import json
import yaml
import polars as pl
import pyarrow as pa
import pyarrow.compute as pc
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple, Callable, Union
from dataclasses import dataclass, asdict
from collections import defaultdict, deque
import warnings
warnings.filterwarnings('ignore')

# Add current directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Technical analysis with Polars extension
try:
    import polars_talib as ta
    POLARS_TALIB_AVAILABLE = True
except ImportError:
    print("[WARN]  polars-talib not installed. Install with: pip install polars-talib")
    POLARS_TALIB_AVAILABLE = False

# Statistical functions for Kelly Criterion using PyArrow
import pyarrow.compute as pc
from scipy import stats

# Notifications
import telegram
from telegram import Bot

# SmartAPI integration
try:
    from SmartApi import SmartConnect
    from SmartApi.smartWebSocketV2 import SmartWebSocketV2
except ImportError:
    print("[WARN]  SmartAPI not installed. Install with: pip install smartapi-python")
    SmartConnect = None
    SmartWebSocketV2 = None

# Import other agents
try:
    from market_monitoring_agent import MarketMonitoringAgent, MarketTick, OHLCV, MarketIndicators, MarketRegime
except ImportError:
    print("[WARN]  Market Monitoring Agent not found. Some features will be disabled.")
    MarketMonitoringAgent = None

try:
    from ai_training_agent import AITrainingAgent
except ImportError:
    print("[WARN]  AI Training Agent not found. AI-based signal enhancement will be disabled.")
    AITrainingAgent = None

# Enhanced Model Integration
try:
    from utils.enhanced_model_integration import EnhancedModelIntegration, get_enhanced_model_integration
    ENHANCED_MODELS_AVAILABLE = True
except ImportError:
    print("[WARN]  Enhanced Model Integration not found. Advanced ML predictions will be disabled.")
    EnhancedModelIntegration = None
    ENHANCED_MODELS_AVAILABLE = False

try:
    from risk_agent import RiskManagementAgent
    from utils.risk_models import TradeRequest, TradeDirection, ProductType, OrderType
except ImportError:
    print("[WARN]  Risk Management Agent not found. Some features will be disabled.")
    RiskManagementAgent = None
    TradeRequest = None

# Import real-time data components
try:
    from utils.websocket_manager import WebSocketManager, MarketTick, SymbolSubscription
    from utils.stock_universe import StockUniverse, StockInfo
    REALTIME_AVAILABLE = True
except ImportError:
    print("[WARN] Real-time components not available")
    WebSocketManager = None
    MarketTick = None
    SymbolSubscription = None
    StockUniverse = None
    REALTIME_AVAILABLE = False

logger = logging.getLogger(__name__)

# ═══════════════════════════════════════════════════════════════════════════════
# [STATUS] DATA STRUCTURES
# ═══════════════════════════════════════════════════════════════════════════════

@dataclass
class SignalInput:
    """Input data for signal generation"""
    symbol: str
    timestamp: datetime
    ohlcv_data: List[OHLCV]
    indicators: MarketIndicators
    market_regime: Optional[MarketRegime]
    strategy_config: Dict[str, Any]
    capital_info: Dict[str, Any]
    
@dataclass
class TradingSignal:
    """Generated trading signal"""
    signal_id: str
    symbol: str
    strategy_name: str
    signal_type: int  # 1=Long, -1=Short, 0=Exit
    action: str  # 'BUY', 'SELL', 'EXIT'
    entry_price: float
    stop_loss: float
    take_profit: float
    quantity: int
    risk_reward_ratio: float
    confidence: float
    market_regime: str
    timestamp: datetime
    
    # Position sizing details
    capital_allocated: float
    risk_amount: float
    position_size_method: str  # 'kelly', 'fixed_fraction', 'volatility_scaled'
    
    # Validation flags
    liquidity_check: bool
    time_filter_check: bool
    risk_check: bool
    cooldown_check: bool
    
    # Context and metadata
    context: Dict[str, Any]
    indicators_snapshot: Dict[str, float]
    
@dataclass
class SignalValidationResult:
    """Result of signal validation"""
    is_valid: bool
    rejection_reason: Optional[str]
    validation_score: float
    risk_metrics: Dict[str, float]
    
@dataclass
class PositionSizingResult:
    """Result of position sizing calculation"""
    quantity: int
    capital_allocated: float
    risk_amount: float
    kelly_fraction: Optional[float]
    max_position_value: float
    method_used: str

@dataclass
class SignalGenerationConfig:
    """Configuration for Signal Generation Agent"""
    
    # Input Sources Configuration
    input_sources: Dict[str, Any]
    
    # Strategy Evaluation Configuration
    strategy_evaluation: Dict[str, Any]
    
    # Risk Management Configuration
    risk_management: Dict[str, Any]
    
    # Position Sizing Configuration
    position_sizing: Dict[str, Any]
    
    # Signal Validation Configuration
    signal_validation: Dict[str, Any]
    
    # Output Configuration
    output_config: Dict[str, Any]
    
    # Notifications Configuration
    notifications: Dict[str, Any]
    
    # Integration Configuration
    integrations: Dict[str, Any]
    
    # Performance Configuration
    performance: Dict[str, Any]

# ═══════════════════════════════════════════════════════════════════════════════
# [CONFIG] UTILITY FUNCTIONS
# ═══════════════════════════════════════════════════════════════════════════════

def load_signal_config(config_path: str = "config/signal_generation_config.yaml") -> SignalGenerationConfig:
    """Load signal generation configuration from YAML file"""
    try:
        with open(config_path, 'r', encoding='utf-8') as file:
            config_data = yaml.safe_load(file)
        
        return SignalGenerationConfig(
            input_sources=config_data.get('input_sources', {}),
            strategy_evaluation=config_data.get('strategy_evaluation', {}),
            risk_management=config_data.get('risk_management', {}),
            position_sizing=config_data.get('position_sizing', {}),
            signal_validation=config_data.get('signal_validation', {}),
            output_config=config_data.get('output_config', {}),
            notifications=config_data.get('notifications', {}),
            integrations=config_data.get('integrations', {}),
            performance=config_data.get('performance', {})
        )
    except Exception as e:
        logger.error(f"Failed to load signal generation configuration: {e}")
        raise

def setup_signal_logging(config: SignalGenerationConfig):
    """Setup logging configuration for signal generation"""
    log_config = config.output_config.get('logging', {})
    
    # Create logs directory
    log_dir = log_config.get('log_dir', 'logs')
    os.makedirs(log_dir, exist_ok=True)
    
    # Configure logging
    log_level = getattr(logging, log_config.get('level', 'INFO').upper())
    
    # Create formatter
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Configure logger
    signal_logger = logging.getLogger('signal_generation')
    signal_logger.setLevel(log_level)
    
    # Console handler
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    signal_logger.addHandler(console_handler)
    
    # File handler
    if log_config.get('enable_file_logging', True):
        from logging.handlers import RotatingFileHandler
        
        file_handler = RotatingFileHandler(
            filename=os.path.join(log_dir, 'signal_generation.log'),
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5
        )
        file_handler.setFormatter(formatter)
        signal_logger.addHandler(file_handler)
    
    logger.info("Signal generation logging configured successfully")

def generate_signal_id(symbol: str, strategy: str, timestamp: datetime) -> str:
    """Generate unique signal ID"""
    return f"{symbol}_{strategy}_{timestamp.strftime('%Y%m%d_%H%M%S')}"

def is_market_hours(timestamp: datetime = None) -> bool:
    """Check if current time is within market hours (09:20 - 15:00)"""
    if timestamp is None:
        timestamp = datetime.now()
    
    # Check if it's a weekday (Monday=0, Sunday=6)
    if timestamp.weekday() >= 5:  # Saturday or Sunday
        return False
    
    # Check market hours (09:20 to 15:00)
    market_open = timestamp.replace(hour=9, minute=20, second=0, microsecond=0)
    market_close = timestamp.replace(hour=15, minute=0, second=0, microsecond=0)
    
    return market_open <= timestamp <= market_close

def calculate_kelly_fraction(win_rate: float, avg_win: float, avg_loss: float) -> float:
    """Calculate Kelly Criterion fraction for position sizing"""
    try:
        if avg_loss <= 0 or win_rate <= 0 or win_rate >= 1:
            return 0.0
        
        # Kelly formula: f = (bp - q) / b
        # where b = avg_win/avg_loss, p = win_rate, q = 1 - win_rate
        b = avg_win / abs(avg_loss)
        p = win_rate
        q = 1 - win_rate
        
        kelly_fraction = (b * p - q) / b
        
        # Cap Kelly fraction to reasonable limits (max 25%)
        return max(0.0, min(kelly_fraction, 0.25))
        
    except Exception as e:
        logger.error(f"Error calculating Kelly fraction: {e}")
        return 0.0

# ═══════════════════════════════════════════════════════════════════════════════
# [INIT] SIGNAL GENERATION AGENT
# ═══════════════════════════════════════════════════════════════════════════════

class SignalGenerationAgent:
    """
    Signal Generation Agent for real-time trading signal generation and validation
    
    Features:
    - Real-time signal evaluation using polars and pyarrow for fast processing
    - Strategy logic evaluation with df.eval() and compiled expressions
    - Risk management and position sizing with Kelly Criterion
    - Signal validation and filtering
    - Multi-channel output (database, notifications, execution queue)
    - Integration with Market Monitoring and AI Training agents
    """
    
    def __init__(self, config_path: str = "config/signal_generation_config.yaml"):
        """Initialize Signal Generation Agent"""
        
        # Load configuration
        self.config = load_signal_config(config_path)
        
        # Setup logging
        setup_signal_logging(self.config)
        
        # Initialize components
        self.market_monitoring_agent = None
        self.ai_training_agent = None
        self.risk_management_agent = None
        self.telegram_bot = None
        self.enhanced_models = None
        
        # Strategy and signal management
        self.strategies = {}
        self.compiled_strategies = {}
        self.active_signals = []
        self.signal_history = deque(maxlen=10000)
        self.last_signal_time = defaultdict(lambda: defaultdict(datetime))  # symbol -> strategy -> timestamp
        
        # Performance tracking
        self.performance_metrics = {
            'signals_generated': 0,
            'signals_validated': 0,
            'signals_rejected': 0,
            'execution_time_ms': deque(maxlen=1000)
        }
        
        # State management
        self.is_running = False
        self.signal_handlers = []
        
        logger.info("Signal Generation Agent initialized")

    async def setup(self):
        """Setup all components and integrations"""
        logger.info("[CONFIG] Setting up Signal Generation Agent...")

        try:
            # Setup integrations
            await self._setup_integrations()
            
            # Setup Enhanced Model Integration
            await self._setup_enhanced_models()

            # Load strategies
            await self._load_strategies()

            # Setup notifications
            await self._setup_notifications()

            # Create storage directories
            self._create_storage_directories()

            # Initialize performance monitoring
            self._initialize_performance_monitoring()

            logger.info("[SUCCESS] Signal Generation Agent setup completed")

        except Exception as e:
            logger.error(f"[ERROR] Setup failed: {e}")
            raise

    async def _setup_integrations(self):
        """Setup integrations with other agents"""
        try:
            # Setup Market Monitoring Agent integration
            if MarketMonitoringAgent:
                market_config_path = self.config.integrations.get('market_monitoring', {}).get('config_path',
                                                                                              'config/market_monitoring_config.yaml')
                if os.path.exists(market_config_path):
                    self.market_monitoring_agent = MarketMonitoringAgent(market_config_path)
                    await self.market_monitoring_agent.setup()
                    logger.info("[STATUS] Market Monitoring Agent integration setup")
                else:
                    logger.warning("[WARN]  Market Monitoring config not found")

            # Setup AI Training Agent integration
            if AITrainingAgent:
                ai_config_path = self.config.integrations.get('ai_training', {}).get('config_path',
                                                                                     'config/ai_training_config.yaml')
                if os.path.exists(ai_config_path):
                    self.ai_training_agent = AITrainingAgent(ai_config_path)
                    logger.info("[AGENT] AI Training Agent integration setup")
                else:
                    logger.warning("[WARN]  AI Training config not found")

            # Setup Risk Management Agent integration
            if RiskManagementAgent:
                risk_config_path = self.config.integrations.get('risk_management', {}).get('config_path',
                                                                                           'config/risk_management_config.yaml')
                if os.path.exists(risk_config_path):
                    self.risk_management_agent = RiskManagementAgent(risk_config_path)
                    await self.risk_management_agent.setup()
                    logger.info("[SECURITY]  Risk Management Agent integration setup")
                else:
                    logger.warning("[WARN]  Risk Management config not found")

        except Exception as e:
            logger.error(f"[ERROR] Integration setup failed: {e}")

    async def _setup_enhanced_models(self):
        """Setup Enhanced Model Integration for advanced ML predictions"""
        if not ENHANCED_MODELS_AVAILABLE:
            logger.warning("[WARN]  Enhanced Model Integration not available")
            return

        try:
            # Initialize enhanced model integration
            self.enhanced_models = get_enhanced_model_integration()
            
            # Initialize models
            success = await self.enhanced_models.initialize()
            
            if success:
                logger.info("[SUCCESS] Enhanced Model Integration setup completed for Signal Generation")
                
                # Log model performance summary
                summary = self.enhanced_models.get_model_performance_summary()
                logger.info(f"[INFO] Signal Generation Agent using {summary['model_count']} enhanced models")
            else:
                logger.error("[ERROR] Enhanced Model Integration initialization failed")
                self.enhanced_models = None

        except Exception as e:
            logger.error(f"[ERROR] Enhanced Model Integration setup failed: {e}")
            self.enhanced_models = None

    async def _load_strategies(self):
        """Load and compile trading strategies"""
        try:
            strategy_config_path = self.config.strategy_evaluation.get('strategy_config_path', 'config/strategies.yaml')

            if not os.path.exists(strategy_config_path):
                logger.error(f"[ERROR] Strategy config file not found: {strategy_config_path}")
                return

            with open(strategy_config_path, 'r', encoding='utf-8') as file:
                strategy_data = yaml.safe_load(file)

            strategies_list = strategy_data.get('strategies', [])

            for strategy in strategies_list:
                strategy_name = strategy.get('name')
                if strategy_name:
                    self.strategies[strategy_name] = strategy

                    # Pre-compile strategy expressions for faster evaluation
                    await self._compile_strategy_expressions(strategy_name, strategy)

            logger.info(f"[LIST] Loaded {len(self.strategies)} trading strategies")

        except Exception as e:
            logger.error(f"[ERROR] Failed to load strategies: {e}")

    async def _compile_strategy_expressions(self, strategy_name: str, strategy: Dict[str, Any]):
        """Pre-compile strategy expressions for faster evaluation"""
        try:
            compiled_strategy = {
                'name': strategy_name,
                'long_expr': strategy.get('long', ''),
                'short_expr': strategy.get('short', ''),
                'capital': strategy.get('capital', 100000),
                'compiled_long': None,
                'compiled_short': None
            }

            # Store compiled strategy
            self.compiled_strategies[strategy_name] = compiled_strategy

            logger.debug(f"[COMPILE] Compiled strategy expressions for {strategy_name}")

        except Exception as e:
            logger.error(f"[ERROR] Failed to compile strategy {strategy_name}: {e}")

    async def _setup_notifications(self):
        """Setup notification systems"""
        try:
            # Setup Telegram bot
            telegram_config = self.config.notifications.get('telegram', {})
            if telegram_config.get('enable', False):
                bot_token = telegram_config.get('bot_token')
                if bot_token and bot_token != "YOUR_BOT_TOKEN":
                    self.telegram_bot = Bot(token=bot_token)
                    logger.info("[MOBILE] Telegram bot setup completed")
                else:
                    logger.warning("[WARN]  Telegram bot token not configured")

        except Exception as e:
            logger.error(f"[ERROR] Notification setup failed: {e}")

    def _create_storage_directories(self):
        """Create necessary storage directories"""
        storage_config = self.config.output_config.get('storage', {})

        directories = [
            storage_config.get('signals_path', 'data/signals'),
            storage_config.get('performance_path', 'data/performance'),
            storage_config.get('logs_path', 'logs'),
            'data/signal_generation'
        ]

        for directory in directories:
            os.makedirs(directory, exist_ok=True)

        logger.info("[FOLDER] Storage directories created")

    def _initialize_performance_monitoring(self):
        """Initialize performance monitoring"""
        self.performance_metrics.update({
            'start_time': datetime.now(),
            'signals_per_minute': deque(maxlen=60),
            'avg_processing_time_ms': 0.0,
            'memory_usage_mb': 0.0
        })

        logger.info("[METRICS] Performance monitoring initialized")

    # ═══════════════════════════════════════════════════════════════════════════════
    # 📥 INPUT MANAGEMENT
    # ═══════════════════════════════════════════════════════════════════════════════

    async def process_market_data(self, symbol: str, ohlcv_data: List[OHLCV],
                                 indicators: MarketIndicators, market_regime: Optional[MarketRegime] = None):
        """Process incoming market data and generate signals"""
        try:
            start_time = datetime.now()

            # Create signal input
            signal_input = SignalInput(
                symbol=symbol,
                timestamp=datetime.now(),
                ohlcv_data=ohlcv_data,
                indicators=indicators,
                market_regime=market_regime,
                strategy_config=self.strategies,
                capital_info=self._get_capital_info(symbol)
            )

            # Generate signals for all applicable strategies
            signals = await self._evaluate_strategies(signal_input)

            # Process each generated signal
            for signal in signals:
                await self._process_signal(signal)

            # Update performance metrics
            processing_time = (datetime.now() - start_time).total_seconds() * 1000
            self.performance_metrics['execution_time_ms'].append(processing_time)

            return signals

        except Exception as e:
            logger.error(f"[ERROR] Error processing market data for {symbol}: {e}")
            return []

    def _get_capital_info(self, symbol: str) -> Dict[str, Any]:
        """Get capital allocation information for symbol"""
        capital_config = self.config.position_sizing.get('capital_allocation', {})

        return {
            'total_capital': capital_config.get('total_capital', 100000),
            'max_position_size_percent': capital_config.get('max_position_size_percent', 2.0),
            'max_risk_per_trade_percent': capital_config.get('max_risk_per_trade_percent', 1.0),
            'max_daily_risk_percent': capital_config.get('max_daily_risk_percent', 5.0),
            'intraday_margin_multiplier': capital_config.get('intraday_margin_multiplier', 3.5)
        }

    # ═══════════════════════════════════════════════════════════════════════════════
    # 🧠 SIGNAL EVALUATION ENGINE
    # ═══════════════════════════════════════════════════════════════════════════════

    async def _evaluate_strategies(self, signal_input: SignalInput) -> List[TradingSignal]:
        """Evaluate all strategies against current market data"""
        signals = []

        try:
            # Convert OHLCV data to Polars DataFrame for fast evaluation
            df = self._create_polars_dataframe(signal_input.ohlcv_data, signal_input.indicators)

            if len(df) < 50:  # Need sufficient data for strategy evaluation
                return signals

            # Evaluate each strategy
            for strategy_name, strategy in self.compiled_strategies.items():
                try:
                    # Check cooldown period
                    if not self._check_signal_cooldown(signal_input.symbol, strategy_name):
                        continue

                    # Evaluate strategy conditions
                    signal = await self._evaluate_single_strategy(signal_input, strategy, df)

                    if signal:
                        signals.append(signal)

                except Exception as e:
                    logger.error(f"[ERROR] Error evaluating strategy {strategy_name}: {e}")

            return signals

        except Exception as e:
            logger.error(f"[ERROR] Error in strategy evaluation: {e}")
            return signals

    def _create_polars_dataframe(self, ohlcv_data: List[OHLCV], indicators: MarketIndicators) -> pl.DataFrame:
        """Create Polars DataFrame from OHLCV data and indicators"""
        try:
            # Convert OHLCV data to DataFrame
            df_data = {
                'timestamp': [candle.timestamp for candle in ohlcv_data],
                'open': [candle.open for candle in ohlcv_data],
                'high': [candle.high for candle in ohlcv_data],
                'low': [candle.low for candle in ohlcv_data],
                'close': [candle.close for candle in ohlcv_data],
                'volume': [candle.volume for candle in ohlcv_data]
            }

            df = pl.DataFrame(df_data)

            # Add indicators as columns (using latest values for all rows for simplicity)
            # In a real implementation, you'd calculate indicators for each row
            indicator_dict = asdict(indicators)
            for key, value in indicator_dict.items():
                if key not in ['symbol', 'timestamp'] and value is not None:
                    df = df.with_columns(pl.lit(value).alias(key))

            return df

        except Exception as e:
            logger.error(f"[ERROR] Error creating Polars DataFrame: {e}")
            return pl.DataFrame()

    def _check_signal_cooldown(self, symbol: str, strategy_name: str) -> bool:
        """Check if enough time has passed since last signal for this symbol-strategy combination"""
        try:
            cooldown_config = self.config.signal_validation.get('cooldown', {})
            cooldown_minutes = cooldown_config.get('minutes_between_signals', 5)

            last_signal_time = self.last_signal_time[symbol].get(strategy_name)

            if last_signal_time is None:
                return True

            time_diff = datetime.now() - last_signal_time
            return time_diff.total_seconds() >= (cooldown_minutes * 60)

        except Exception as e:
            logger.error(f"[ERROR] Error checking signal cooldown: {e}")
            return True

    async def _evaluate_single_strategy(self, signal_input: SignalInput, strategy: Dict[str, Any],
                                       df: pl.DataFrame) -> Optional[TradingSignal]:
        """Evaluate a single strategy against market data"""
        try:
            strategy_name = strategy['name']

            # Evaluate long and short conditions using Polars expressions
            long_condition = self._evaluate_strategy_condition(df, strategy['long_expr'])
            short_condition = self._evaluate_strategy_condition(df, strategy['short_expr'])

            signal_type = 0  # No signal
            action = None

            if long_condition:
                signal_type = 1
                action = 'BUY'
            elif short_condition:
                signal_type = -1
                action = 'SELL'

            if signal_type == 0:
                return None

            # Get current price
            current_price = float(df.select(pl.col('close').last()).item())

            # Calculate position sizing
            position_sizing = await self._calculate_position_sizing(
                signal_input.symbol, strategy_name, current_price, signal_input.capital_info
            )

            # Calculate stop loss and take profit
            sl_tp = self._calculate_stop_loss_take_profit(
                current_price, signal_type, signal_input.indicators, signal_input.market_regime
            )

            # Create signal
            signal = TradingSignal(
                signal_id=generate_signal_id(signal_input.symbol, strategy_name, signal_input.timestamp),
                symbol=signal_input.symbol,
                strategy_name=strategy_name,
                signal_type=signal_type,
                action=action,
                entry_price=current_price,
                stop_loss=sl_tp['stop_loss'],
                take_profit=sl_tp['take_profit'],
                quantity=position_sizing.quantity,
                risk_reward_ratio=sl_tp['risk_reward_ratio'],
                confidence=self._calculate_signal_confidence(df, strategy, signal_input.market_regime),
                market_regime=signal_input.market_regime.regime if signal_input.market_regime else 'unknown',
                timestamp=signal_input.timestamp,
                capital_allocated=position_sizing.capital_allocated,
                risk_amount=position_sizing.risk_amount,
                position_size_method=position_sizing.method_used,
                liquidity_check=False,  # Will be set during validation
                time_filter_check=False,  # Will be set during validation
                risk_check=False,  # Will be set during validation
                cooldown_check=True,  # Already checked
                context={
                    'strategy_config': strategy,
                    'market_data_length': len(df),
                    'evaluation_timestamp': datetime.now().isoformat()
                },
                indicators_snapshot=self._create_indicators_snapshot(signal_input.indicators)
            )

            return signal

        except Exception as e:
            logger.error(f"[ERROR] Error evaluating strategy {strategy.get('name', 'unknown')}: {e}")
            return None

    def _evaluate_strategy_condition(self, df: pl.DataFrame, condition_expr: str) -> bool:
        """Evaluate strategy condition using Polars expressions"""
        try:
            if not condition_expr or len(df) == 0:
                return False

            # Replace common operators for Polars compatibility
            polars_expr = condition_expr.replace('&', ' & ').replace('|', ' | ')

            # Use Polars eval-like functionality
            # For now, we'll use a simplified approach
            # In production, you'd want more sophisticated expression parsing

            # Get the last row for evaluation
            last_row = df.tail(1)

            # Simple condition evaluation (this is a simplified version)
            # In production, you'd want to implement a proper expression evaluator
            try:
                # Convert to pandas for eval (temporary solution)
                pandas_df = last_row.to_pandas()
                result = pandas_df.eval(condition_expr).iloc[0] if len(pandas_df) > 0 else False
                return bool(result)
            except:
                # Fallback: manual evaluation for common patterns
                return self._manual_condition_evaluation(last_row, condition_expr)

        except Exception as e:
            logger.error(f"[ERROR] Error evaluating condition '{condition_expr}': {e}")
            return False

    def _manual_condition_evaluation(self, df_row: pl.DataFrame, condition: str) -> bool:
        """Manual evaluation for common trading conditions"""
        try:
            if len(df_row) == 0:
                return False

            row_dict = df_row.to_dicts()[0]

            # Simple pattern matching for common conditions
            if 'close > ema_20' in condition:
                return row_dict.get('close', 0) > row_dict.get('ema_20', 0)
            elif 'rsi_14 > 70' in condition:
                return row_dict.get('rsi_14', 50) > 70
            elif 'rsi_14 < 30' in condition:
                return row_dict.get('rsi_14', 50) < 30
            elif 'ema_5 > ema_21' in condition:
                return row_dict.get('ema_5', 0) > row_dict.get('ema_21', 0)

            # Add more pattern matching as needed
            return False

        except Exception as e:
            logger.error(f"[ERROR] Error in manual condition evaluation: {e}")
            return False

    async def _calculate_position_sizing(self, symbol: str, strategy_name: str, current_price: float,
                                        capital_info: Dict[str, Any]) -> PositionSizingResult:
        """Calculate position sizing using various methods"""
        try:
            sizing_config = self.config.position_sizing
            method = sizing_config.get('default_method', 'fixed_fraction')

            total_capital = capital_info['total_capital']
            max_risk_percent = capital_info['max_risk_per_trade_percent']
            max_position_percent = capital_info['max_position_size_percent']

            # Calculate maximum position value
            max_position_value = total_capital * (max_position_percent / 100)

            # Calculate risk amount
            risk_amount = total_capital * (max_risk_percent / 100)

            if method == 'kelly':
                # Use Kelly Criterion if historical data is available
                kelly_fraction = await self._get_kelly_fraction(symbol, strategy_name)
                if kelly_fraction > 0:
                    capital_allocated = total_capital * kelly_fraction
                    quantity = int(min(capital_allocated, max_position_value) / current_price)
                    method_used = 'kelly'
                else:
                    # Fallback to fixed fraction
                    capital_allocated = max_position_value
                    quantity = int(capital_allocated / current_price)
                    method_used = 'fixed_fraction_fallback'

            elif method == 'volatility_scaled':
                # Scale position size based on volatility (ATR)
                atr_multiplier = sizing_config.get('volatility_scaling', {}).get('atr_multiplier', 2.0)
                # This would need ATR from indicators
                capital_allocated = max_position_value
                quantity = int(capital_allocated / current_price)
                method_used = 'volatility_scaled'

            else:  # fixed_fraction
                capital_allocated = max_position_value
                quantity = int(capital_allocated / current_price)
                method_used = 'fixed_fraction'

            # Apply intraday margin multiplier
            intraday_multiplier = capital_info.get('intraday_margin_multiplier', 1.0)
            max_intraday_value = total_capital * intraday_multiplier

            if capital_allocated > max_intraday_value:
                quantity = int(max_intraday_value / current_price)
                capital_allocated = quantity * current_price

            return PositionSizingResult(
                quantity=max(1, quantity),  # Minimum 1 share
                capital_allocated=capital_allocated,
                risk_amount=risk_amount,
                kelly_fraction=kelly_fraction if method == 'kelly' else None,
                max_position_value=max_position_value,
                method_used=method_used
            )

        except Exception as e:
            logger.error(f"[ERROR] Error calculating position sizing: {e}")
            return PositionSizingResult(
                quantity=1,
                capital_allocated=current_price,
                risk_amount=current_price * 0.01,
                kelly_fraction=None,
                max_position_value=current_price,
                method_used='fallback'
            )

    async def _get_kelly_fraction(self, symbol: str, strategy_name: str) -> float:
        """Get Kelly fraction from historical performance data"""
        try:
            # This would query historical performance database
            # For now, return a conservative default
            return 0.02  # 2% Kelly fraction as default

        except Exception as e:
            logger.error(f"[ERROR] Error getting Kelly fraction: {e}")
            return 0.0

    def _calculate_stop_loss_take_profit(self, current_price: float, signal_type: int,
                                        indicators: MarketIndicators, market_regime: Optional[MarketRegime]) -> Dict[str, float]:
        """Calculate stop loss and take profit levels"""
        try:
            risk_config = self.config.risk_management

            # Get ATR for dynamic SL/TP calculation
            atr = getattr(indicators, 'atr', None) or current_price * 0.02  # 2% fallback

            # Base risk-reward ratio
            base_rr_ratio = risk_config.get('default_risk_reward_ratio', 2.0)

            # Adjust RR ratio based on market regime
            if market_regime:
                regime_adjustments = risk_config.get('regime_adjustments', {})
                regime_multiplier = regime_adjustments.get(market_regime.regime, 1.0)
                rr_ratio = base_rr_ratio * regime_multiplier
            else:
                rr_ratio = base_rr_ratio

            # Calculate SL/TP based on signal type
            if signal_type == 1:  # Long position
                atr_multiplier = risk_config.get('atr_stop_loss_multiplier', 2.0)
                stop_loss = current_price - (atr * atr_multiplier)
                take_profit = current_price + (atr * atr_multiplier * rr_ratio)
            else:  # Short position
                atr_multiplier = risk_config.get('atr_stop_loss_multiplier', 2.0)
                stop_loss = current_price + (atr * atr_multiplier)
                take_profit = current_price - (atr * atr_multiplier * rr_ratio)

            return {
                'stop_loss': round(stop_loss, 2),
                'take_profit': round(take_profit, 2),
                'risk_reward_ratio': rr_ratio
            }

        except Exception as e:
            logger.error(f"[ERROR] Error calculating SL/TP: {e}")
            # Fallback to percentage-based SL/TP
            if signal_type == 1:
                return {
                    'stop_loss': round(current_price * 0.98, 2),
                    'take_profit': round(current_price * 1.04, 2),
                    'risk_reward_ratio': 2.0
                }
            else:
                return {
                    'stop_loss': round(current_price * 1.02, 2),
                    'take_profit': round(current_price * 0.96, 2),
                    'risk_reward_ratio': 2.0
                }

    def _calculate_signal_confidence(self, df: pl.DataFrame, strategy: Dict[str, Any],
                                   market_regime: Optional[MarketRegime]) -> float:
        """Calculate signal confidence score using enhanced models"""
        try:
            # Use enhanced models if available
            if self.enhanced_models:
                return self._calculate_enhanced_signal_confidence(df, strategy, market_regime)
            
            # Fallback to traditional confidence calculation
            confidence = 0.5  # Base confidence

            # Adjust based on market regime
            if market_regime:
                regime_confidence = {
                    'bull': 0.8,
                    'bear': 0.7,
                    'sideways': 0.6
                }.get(market_regime.regime, 0.5)

                confidence = (confidence + regime_confidence) / 2

            # Adjust based on volume
            if len(df) > 0:
                latest_volume = df.select(pl.col('volume').last()).item()
                avg_volume = df.select(pl.col('volume').mean()).item()

                if latest_volume > avg_volume * 1.5:
                    confidence += 0.1
                elif latest_volume < avg_volume * 0.5:
                    confidence -= 0.1

            # Ensure confidence is between 0 and 1
            return max(0.0, min(1.0, confidence))

        except Exception as e:
            logger.error(f"[ERROR] Error calculating signal confidence: {e}")
            return 0.5

    def _calculate_enhanced_signal_confidence(self, df: pl.DataFrame, strategy: Dict[str, Any],
                                            market_regime: Optional[MarketRegime]) -> float:
        """Calculate signal confidence using enhanced ML models"""
        try:
            # Prepare strategy data for enhanced models
            strategy_data = self._prepare_strategy_data_for_enhanced_models(df, strategy, market_regime)
            
            # Get predictions from enhanced models (synchronous call)
            import asyncio
            loop = asyncio.get_event_loop()
            predictions = loop.run_until_complete(self.enhanced_models.predict_all_tasks(strategy_data))
            
            if not predictions:
                logger.warning("[ENHANCED_ML] No predictions available for confidence calculation")
                return 0.5
            
            # Calculate confidence based on model predictions
            confidence_scores = []
            
            # Sharpe ratio prediction confidence
            sharpe_pred = predictions.get('sharpe_ratio_prediction')
            if sharpe_pred and sharpe_pred.prediction > 1.0:
                confidence_scores.append(min(sharpe_pred.confidence * (sharpe_pred.prediction / 2.0), 1.0))
            
            # ROI prediction confidence
            roi_pred = predictions.get('roi_prediction')
            if roi_pred and roi_pred.prediction > 5.0:
                confidence_scores.append(min(roi_pred.confidence * (roi_pred.prediction / 20.0), 1.0))
            
            # Profit factor prediction confidence
            pf_pred = predictions.get('profit_factor_prediction')
            if pf_pred and pf_pred.prediction > 1.2:
                confidence_scores.append(min(pf_pred.confidence * ((pf_pred.prediction - 1.0) / 1.0), 1.0))
            
            # Profitability classification confidence
            prof_pred = predictions.get('profitability_classification')
            if prof_pred:
                confidence_scores.append(prof_pred.confidence)
            
            # Calculate overall confidence
            if confidence_scores:
                enhanced_confidence = sum(confidence_scores) / len(confidence_scores)
                
                # Blend with traditional confidence for stability
                traditional_confidence = 0.5
                if market_regime:
                    regime_confidence = {
                        'bull': 0.8, 'bear': 0.7, 'sideways': 0.6
                    }.get(market_regime.regime, 0.5)
                    traditional_confidence = (traditional_confidence + regime_confidence) / 2
                
                # 70% enhanced, 30% traditional
                final_confidence = (enhanced_confidence * 0.7) + (traditional_confidence * 0.3)
                
                logger.debug(f"[ENHANCED_ML] Signal confidence: Enhanced={enhanced_confidence:.3f}, "
                           f"Traditional={traditional_confidence:.3f}, Final={final_confidence:.3f}")
                
                return max(0.0, min(1.0, final_confidence))
            else:
                return 0.5
                
        except Exception as e:
            logger.error(f"[ERROR] Enhanced signal confidence calculation failed: {e}")
            return 0.5

    def _prepare_strategy_data_for_enhanced_models(self, df: pl.DataFrame, strategy: Dict[str, Any],
                                                 market_regime: Optional[MarketRegime]) -> Dict[str, Any]:
        """Prepare strategy data for enhanced model predictions"""
        try:
            # Extract basic metrics from current data
            if len(df) == 0:
                return {}
            
            # Get price data
            close_prices = df.select(pl.col('close')).to_series().to_list()
            volumes = df.select(pl.col('volume')).to_series().to_list()
            
            # Calculate basic statistics
            current_price = close_prices[-1]
            price_change = (current_price - close_prices[0]) / close_prices[0] * 100 if len(close_prices) > 1 else 0
            
            # Volume analysis
            avg_volume = sum(volumes) / len(volumes) if volumes else 0
            current_volume = volumes[-1] if volumes else 0
            volume_ratio = current_volume / avg_volume if avg_volume > 0 else 1.0
            
            # Simulate strategy performance metrics based on current market conditions
            base_sharpe = max(-2.0, min(3.0, price_change / 10))
            base_roi = base_sharpe * 8
            base_profit_factor = max(0.5, 1.0 + base_sharpe * 0.3)
            base_drawdown = -abs(15 - abs(base_sharpe * 5))
            
            # Adjust based on market regime
            if market_regime:
                regime_multiplier = {
                    'bull': 1.2,
                    'bear': 0.8,
                    'sideways': 1.0
                }.get(market_regime.regime, 1.0)
                
                base_sharpe *= regime_multiplier
                base_roi *= regime_multiplier
                base_profit_factor = max(0.5, base_profit_factor * regime_multiplier)
            
            # Adjust based on volume
            volume_multiplier = min(1.5, max(0.5, volume_ratio))
            base_sharpe *= volume_multiplier
            base_roi *= volume_multiplier
            
            strategy_data = {
                # Sharpe ratio metrics
                'avg_sharpe_ratio': base_sharpe,
                'min_sharpe_ratio': base_sharpe - 0.5,
                'max_sharpe_ratio': base_sharpe + 0.5,
                'std_sharpe_ratio': 0.3,
                
                # ROI metrics
                'avg_roi': base_roi,
                'min_roi': base_roi - 5,
                'max_roi': base_roi + 5,
                'std_roi': 3.0,
                
                # Profit factor metrics
                'avg_profit_factor': base_profit_factor,
                'min_profit_factor': base_profit_factor - 0.2,
                'max_profit_factor': base_profit_factor + 0.2,
                'std_profit_factor': 0.1,
                
                # Drawdown metrics
                'avg_max_drawdown': base_drawdown,
                'min_max_drawdown': base_drawdown - 3,
                'max_max_drawdown': base_drawdown + 3,
                'std_max_drawdown': 2.0,
                
                # Expectancy metrics
                'avg_expectancy': base_roi * 0.5,
                'min_expectancy': base_roi * 0.3,
                'max_expectancy': base_roi * 0.7,
                'std_expectancy': base_roi * 0.1,
                
                # Accuracy metrics
                'avg_accuracy': 50 + (base_sharpe * 10),
                'min_accuracy': 40 + (base_sharpe * 8),
                'max_accuracy': 60 + (base_sharpe * 12),
                'std_accuracy': 5.0,
                
                # Trade metrics
                'avg_total_trades': 50,
                'std_total_trades': 10,
                'avg_winning_trades': 25,
                'std_winning_trades': 5,
                
                # PnL metrics
                'avg_total_pnl': base_roi * 100,
                'min_total_pnl': base_roi * 80,
                'max_total_pnl': base_roi * 120,
                'std_total_pnl': base_roi * 20,
                
                # Consistency metrics
                'consistency_score': max(0.1, min(0.9, 0.5 + base_sharpe * 0.2)),
                'sharpe_consistency': max(0.1, min(0.9, 0.6 + base_sharpe * 0.15)),
                'roi_drawdown_ratio': abs(base_roi / base_drawdown) if base_drawdown != 0 else 1.0,
                
                # Other metrics
                'trades_per_period': 10.0,
                'walk_forward_steps': 5
            }
            
            return strategy_data
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to prepare strategy data for enhanced models: {e}")
            return {}

    def _create_indicators_snapshot(self, indicators: MarketIndicators) -> Dict[str, float]:
        """Create snapshot of current indicators"""
        try:
            snapshot = {}
            indicator_dict = asdict(indicators)

            for key, value in indicator_dict.items():
                if key not in ['symbol', 'timestamp'] and isinstance(value, (int, float)) and value is not None:
                    snapshot[key] = value

            return snapshot

        except Exception as e:
            logger.error(f"[ERROR] Error creating indicators snapshot: {e}")
            return {}

    # ═══════════════════════════════════════════════════════════════════════════════
    # ⚙️ SIGNAL VALIDATION & PROCESSING
    # ═══════════════════════════════════════════════════════════════════════════════

    async def _process_signal(self, signal: TradingSignal):
        """Process and validate a generated signal"""
        try:
            # Validate signal
            validation_result = await self._validate_signal(signal)

            if not validation_result.is_valid:
                logger.info(f"🚫 Signal rejected for {signal.symbol}: {validation_result.rejection_reason}")
                self.performance_metrics['signals_rejected'] += 1
                return

            # Update signal with validation results
            signal.liquidity_check = True
            signal.time_filter_check = True
            signal.risk_check = True

            # Add to active signals
            self.active_signals.append(signal)
            self.signal_history.append(signal)

            # Update last signal time
            self.last_signal_time[signal.symbol][signal.strategy_name] = signal.timestamp

            # Send outputs
            await self._send_signal_outputs(signal)

            # Update performance metrics
            self.performance_metrics['signals_validated'] += 1
            self.performance_metrics['signals_generated'] += 1

            logger.info(f"[SUCCESS] Signal generated: {signal.symbol} | {signal.strategy_name} | {signal.action} | Confidence: {signal.confidence:.2f}")

        except Exception as e:
            logger.error(f"[ERROR] Error processing signal: {e}")

    async def _validate_signal(self, signal: TradingSignal) -> SignalValidationResult:
        """Validate signal against all filters including risk management"""
        try:
            validation_config = self.config.signal_validation

            # Time filter check
            if not is_market_hours(signal.timestamp):
                return SignalValidationResult(
                    is_valid=False,
                    rejection_reason="Outside market hours",
                    validation_score=0.0,
                    risk_metrics={}
                )

            # Confidence threshold check
            min_confidence = validation_config.get('min_confidence', 0.6)
            if signal.confidence < min_confidence:
                return SignalValidationResult(
                    is_valid=False,
                    rejection_reason=f"Confidence {signal.confidence:.2f} below threshold {min_confidence}",
                    validation_score=signal.confidence,
                    risk_metrics={}
                )

            # Risk Management Agent validation (if available)
            if self.risk_management_agent and TradeRequest:
                risk_validation_result = await self._validate_with_risk_agent(signal)
                if not risk_validation_result.is_valid:
                    return SignalValidationResult(
                        is_valid=False,
                        rejection_reason=f"Risk management validation failed: {risk_validation_result.rejection_reason}",
                        validation_score=0.0,
                        risk_metrics=risk_validation_result.metrics
                    )

            # Risk checks
            risk_metrics = await self._calculate_risk_metrics(signal)

            # Maximum daily risk check
            daily_risk_limit = validation_config.get('max_daily_risk_percent', 5.0)
            if risk_metrics.get('daily_risk_percent', 0) > daily_risk_limit:
                return SignalValidationResult(
                    is_valid=False,
                    rejection_reason=f"Daily risk limit exceeded: {risk_metrics['daily_risk_percent']:.2f}%",
                    validation_score=0.0,
                    risk_metrics=risk_metrics
                )

            # Liquidity check (simplified)
            if signal.capital_allocated > 1000000:  # 10L+ position needs liquidity check
                # In production, this would check actual market depth
                pass

            # Maximum concurrent signals per symbol
            max_signals_per_symbol = validation_config.get('max_signals_per_symbol', 2)
            active_signals_for_symbol = len([s for s in self.active_signals if s.symbol == signal.symbol])

            if active_signals_for_symbol >= max_signals_per_symbol:
                return SignalValidationResult(
                    is_valid=False,
                    rejection_reason=f"Too many active signals for {signal.symbol}",
                    validation_score=0.0,
                    risk_metrics=risk_metrics
                )

            return SignalValidationResult(
                is_valid=True,
                rejection_reason=None,
                validation_score=signal.confidence,
                risk_metrics=risk_metrics
            )

        except Exception as e:
            logger.error(f"[ERROR] Error validating signal: {e}")
            return SignalValidationResult(
                is_valid=False,
                rejection_reason=f"Validation error: {e}",
                validation_score=0.0,
                risk_metrics={}
            )

    async def _validate_with_risk_agent(self, signal: TradingSignal):
        """Validate signal with Risk Management Agent"""
        try:
            # Convert TradingSignal to TradeRequest for risk validation
            trade_request = TradeRequest(
                signal_id=signal.signal_id,
                symbol=signal.symbol,
                exchange="NSE",  # Default to NSE, could be configured
                strategy_name=signal.strategy_name,
                direction=TradeDirection.LONG if signal.signal_type == 1 else TradeDirection.SHORT,
                entry_price=signal.entry_price,
                stop_loss=signal.stop_loss,
                take_profit=signal.take_profit,
                quantity=signal.quantity,
                product_type=ProductType.MIS,  # Default to MIS for intraday
                order_type=OrderType.LIMIT,
                risk_amount=signal.risk_amount,
                capital_allocated=signal.capital_allocated,
                risk_reward_ratio=signal.risk_reward_ratio,
                market_regime=signal.market_regime,
                confidence=signal.confidence,
                timestamp=signal.timestamp,
                context=signal.context
            )

            # Validate with Risk Management Agent
            validation_result = await self.risk_management_agent.validate_trade(trade_request)

            logger.debug(f"Risk validation for {signal.symbol}: {'PASSED' if validation_result.is_valid else 'FAILED'}")

            return validation_result

        except Exception as e:
            logger.error(f"[ERROR] Error validating with risk agent: {e}")
            # Return a failed validation result
            from utils.risk_models import ValidationResult, ValidationStatus, RiskLevel
            return ValidationResult(
                trade_request=None,
                is_valid=False,
                validation_status=ValidationStatus.FAILED,
                risk_level=RiskLevel.HIGH,
                rejection_reason=f"Risk validation error: {str(e)}"
            )

    async def _calculate_risk_metrics(self, signal: TradingSignal) -> Dict[str, float]:
        """Calculate risk metrics for signal validation"""
        try:
            # Calculate current daily risk exposure
            today = datetime.now().date()
            today_signals = [s for s in self.active_signals if s.timestamp.date() == today]

            total_daily_risk = sum(s.risk_amount for s in today_signals)
            total_capital = self.config.position_sizing.get('capital_allocation', {}).get('total_capital', 100000)

            daily_risk_percent = (total_daily_risk / total_capital) * 100

            return {
                'daily_risk_percent': daily_risk_percent,
                'total_daily_risk': total_daily_risk,
                'signal_risk_amount': signal.risk_amount,
                'active_signals_count': len(self.active_signals)
            }

        except Exception as e:
            logger.error(f"[ERROR] Error calculating risk metrics: {e}")
            return {}

    # ═══════════════════════════════════════════════════════════════════════════════
    # 🧾 SIGNAL OUTPUT SYSTEM
    # ═══════════════════════════════════════════════════════════════════════════════

    async def _send_signal_outputs(self, signal: TradingSignal):
        """Send signal to all configured output channels"""
        try:
            # Database logging
            await self._log_signal_to_database(signal)

            # File logging
            await self._log_signal_to_file(signal)

            # Notifications
            await self._send_signal_notifications(signal)

            # Execution queue (for future Order Execution Agent)
            await self._send_to_execution_queue(signal)

            # Strategy monitoring dashboard
            await self._update_strategy_monitor(signal)

        except Exception as e:
            logger.error(f"[ERROR] Error sending signal outputs: {e}")

    async def _log_signal_to_database(self, signal: TradingSignal):
        """Log signal to database (placeholder for future implementation)"""
        try:
            # This would integrate with a database like PostgreSQL, MongoDB, etc.
            # For now, we'll just log the signal details
            logger.info(f"[STATUS] Database log: {signal.signal_id}")

        except Exception as e:
            logger.error(f"[ERROR] Error logging signal to database: {e}")

    async def _log_signal_to_file(self, signal: TradingSignal):
        """Log signal to file for record keeping"""
        try:
            storage_config = self.config.output_config.get('storage', {})
            signals_path = storage_config.get('signals_path', 'data/signals')

            # Create filename with date
            filename = f"signals_{datetime.now().strftime('%Y%m%d')}.json"
            filepath = os.path.join(signals_path, filename)

            # Convert signal to dictionary
            signal_dict = asdict(signal)
            signal_dict['timestamp'] = signal.timestamp.isoformat()

            # Append to file
            with open(filepath, 'a', encoding='utf-8') as f:
                f.write(json.dumps(signal_dict) + '\n')

        except Exception as e:
            logger.error(f"[ERROR] Error logging signal to file: {e}")

    async def _send_signal_notifications(self, signal: TradingSignal):
        """Send signal notifications via configured channels"""
        try:
            # Telegram notification
            if self.telegram_bot:
                await self._send_telegram_signal_notification(signal)

            # Add other notification channels here (Slack, Email, etc.)

        except Exception as e:
            logger.error(f"[ERROR] Error sending signal notifications: {e}")

    async def _send_telegram_signal_notification(self, signal: TradingSignal):
        """Send Telegram notification for signal"""
        try:
            telegram_config = self.config.notifications.get('telegram', {})
            chat_id = telegram_config.get('chat_id')

            if not chat_id or chat_id == "YOUR_CHAT_ID":
                return

            # Format message
            message = f"""
🚨 **Trading Signal Generated**

[STATUS] **Symbol:** {signal.symbol}
[METRICS] **Strategy:** {signal.strategy_name}
[TARGET] **Action:** {signal.action}
[MONEY] **Entry Price:** Rs.{signal.entry_price:.2f}
[STOP] **Stop Loss:** Rs.{signal.stop_loss:.2f}
[TARGET] **Take Profit:** Rs.{signal.take_profit:.2f}
📦 **Quantity:** {signal.quantity}
💵 **Capital:** Rs.{signal.capital_allocated:,.0f}
⚖️ **R:R Ratio:** {signal.risk_reward_ratio:.1f}
[TARGET] **Confidence:** {signal.confidence:.1%}
🌍 **Market Regime:** {signal.market_regime}
[TIME] **Time:** {signal.timestamp.strftime('%H:%M:%S')}
            """

            await self.telegram_bot.send_message(
                chat_id=chat_id,
                text=message,
                parse_mode='Markdown'
            )

        except Exception as e:
            logger.error(f"[ERROR] Error sending Telegram notification: {e}")

    async def _send_to_execution_queue(self, signal: TradingSignal):
        """Send signal to execution queue and notify risk agent"""
        try:
            # This would integrate with Order Execution Agent
            # For now, just log the execution intent
            logger.info(f"📤 Execution queue: {signal.signal_id} ready for order placement")

            # If Risk Management Agent is available, add position tracking
            if self.risk_management_agent and signal.action in ['BUY', 'SELL']:
                # Create trade request for position tracking
                trade_request = TradeRequest(
                    signal_id=signal.signal_id,
                    symbol=signal.symbol,
                    exchange="NSE",
                    strategy_name=signal.strategy_name,
                    direction=TradeDirection.LONG if signal.signal_type == 1 else TradeDirection.SHORT,
                    entry_price=signal.entry_price,
                    stop_loss=signal.stop_loss,
                    take_profit=signal.take_profit,
                    quantity=signal.quantity,
                    product_type=ProductType.MIS,
                    order_type=OrderType.LIMIT,
                    risk_amount=signal.risk_amount,
                    capital_allocated=signal.capital_allocated,
                    risk_reward_ratio=signal.risk_reward_ratio,
                    confidence=signal.confidence,
                    timestamp=signal.timestamp
                )

                # Add position to risk agent (simulating execution at entry price)
                position_id = await self.risk_management_agent.add_position(trade_request, signal.entry_price)
                if position_id:
                    logger.info(f"[SUCCESS] Position added to risk tracking: {position_id}")

        except Exception as e:
            logger.error(f"[ERROR] Error sending to execution queue: {e}")

    async def _update_strategy_monitor(self, signal: TradingSignal):
        """Update strategy monitoring dashboard"""
        try:
            # This would update a real-time dashboard
            # For now, just update internal metrics
            strategy_name = signal.strategy_name

            if not hasattr(self, 'strategy_performance'):
                self.strategy_performance = defaultdict(lambda: {
                    'signals_generated': 0,
                    'total_confidence': 0.0,
                    'avg_confidence': 0.0
                })

            self.strategy_performance[strategy_name]['signals_generated'] += 1
            self.strategy_performance[strategy_name]['total_confidence'] += signal.confidence
            self.strategy_performance[strategy_name]['avg_confidence'] = (
                self.strategy_performance[strategy_name]['total_confidence'] /
                self.strategy_performance[strategy_name]['signals_generated']
            )

        except Exception as e:
            logger.error(f"[ERROR] Error updating strategy monitor: {e}")

    # ═══════════════════════════════════════════════════════════════════════════════
    # [WORKFLOW] INTEGRATION & UTILITY METHODS
    # ═══════════════════════════════════════════════════════════════════════════════

    async def start(self):
        """Start the Signal Generation Agent"""
        try:
            if self.is_running:
                logger.warning("[WARN]  Signal Generation Agent is already running")
                return

            logger.info("[INIT] Starting Signal Generation Agent...")

            # Setup if not already done
            await self.setup()

            self.is_running = True

            # Start integration with Market Monitoring Agent
            if self.market_monitoring_agent:
                # Register signal handler with market monitoring agent
                self.market_monitoring_agent.signal_handlers.append(self._handle_market_monitoring_signal)

                # Start market monitoring if not already running
                if not self.market_monitoring_agent.is_running:
                    await self.market_monitoring_agent.start()

            logger.info("[SUCCESS] Signal Generation Agent started successfully")

        except Exception as e:
            logger.error(f"[ERROR] Failed to start Signal Generation Agent: {e}")
            raise

    async def stop(self):
        """Stop the Signal Generation Agent"""
        try:
            logger.info("[STOP] Stopping Signal Generation Agent...")

            self.is_running = False

            # Save performance metrics
            await self._save_performance_metrics()

            logger.info("[SUCCESS] Signal Generation Agent stopped successfully")

        except Exception as e:
            logger.error(f"[ERROR] Error stopping Signal Generation Agent: {e}")

    async def _handle_market_monitoring_signal(self, market_signal):
        """Handle signals from Market Monitoring Agent"""
        try:
            # This would be called when Market Monitoring Agent detects market events
            # For now, just log the received signal
            logger.info(f"[SIGNAL] Received market monitoring signal: {market_signal}")

        except Exception as e:
            logger.error(f"[ERROR] Error handling market monitoring signal: {e}")

    async def _save_performance_metrics(self):
        """Save performance metrics to file"""
        try:
            storage_config = self.config.output_config.get('storage', {})
            performance_path = storage_config.get('performance_path', 'data/performance')

            filename = f"signal_performance_{datetime.now().strftime('%Y%m%d')}.json"
            filepath = os.path.join(performance_path, filename)

            # Calculate final metrics
            total_execution_time = sum(self.performance_metrics['execution_time_ms'])
            avg_execution_time = total_execution_time / len(self.performance_metrics['execution_time_ms']) if self.performance_metrics['execution_time_ms'] else 0

            performance_data = {
                'session_start': self.performance_metrics.get('start_time', datetime.now()).isoformat(),
                'session_end': datetime.now().isoformat(),
                'signals_generated': self.performance_metrics['signals_generated'],
                'signals_validated': self.performance_metrics['signals_validated'],
                'signals_rejected': self.performance_metrics['signals_rejected'],
                'avg_execution_time_ms': avg_execution_time,
                'total_execution_time_ms': total_execution_time,
                'strategy_performance': dict(self.strategy_performance) if hasattr(self, 'strategy_performance') else {}
            }

            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(performance_data, f, indent=2)

            logger.info(f"[STATUS] Performance metrics saved to {filepath}")

        except Exception as e:
            logger.error(f"[ERROR] Error saving performance metrics: {e}")

    def get_active_signals(self) -> List[TradingSignal]:
        """Get list of currently active signals"""
        return self.active_signals.copy()

    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get current performance metrics"""
        return self.performance_metrics.copy()

    def add_signal_handler(self, handler: Callable):
        """Add a signal handler function"""
        self.signal_handlers.append(handler)

    def remove_signal_handler(self, handler: Callable):
        """Remove a signal handler function"""
        if handler in self.signal_handlers:
            self.signal_handlers.remove(handler)


# ═══════════════════════════════════════════════════════════════════════════════
# 🧪 MAIN EXECUTION (for testing)
# ═══════════════════════════════════════════════════════════════════════════════

async def main():
    """Main function for testing Signal Generation Agent"""
    try:
        # Initialize agent
        agent = SignalGenerationAgent()

        # Setup and start
        await agent.setup()
        await agent.start()

        logger.info("🧪 Signal Generation Agent test completed")

    except Exception as e:
        logger.error(f"[ERROR] Test failed: {e}")
    finally:
        if 'agent' in locals():
            await agent.stop()

if __name__ == "__main__":
    # Setup basic logging for testing
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    # Run the test
    asyncio.run(main())
