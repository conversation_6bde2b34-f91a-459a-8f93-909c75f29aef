#!/usr/bin/env python3
"""
🎯 Live Strategy Assignment Agent
Determines optimal strategies for selected stocks using ML models and market conditions

Features:
- Assigns primary and backup strategies for each selected stock
- Uses trained strategy performance models
- Considers current market conditions and stock characteristics
- Calculates expected performance metrics for assigned strategies
- Provides strategy confidence scores and rationale
"""

import asyncio
import logging
import polars as pl
import pandas as pd
import numpy as np
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
import yaml
import json

# Import data structures from other agents
from agents.live_ml_prediction_agent import PredictionResult
from agents.live_feature_engineering_agent import FeatureEngineering
from agents.base_agent import BaseAgent, AgentStatus # Import BaseAgent

logger = logging.getLogger(__name__)

@dataclass
class StrategyAssignment:
    """Strategy assignment for a stock"""
    symbol: str
    primary_strategy: str
    primary_confidence: float
    backup_strategy: str
    backup_confidence: float
    expected_performance: Dict[str, float]
    market_condition_match: float
    assignment_rationale: str
    risk_warnings: List[str]

@dataclass
class StrategyAssignmentResult:
    """Complete strategy assignment result"""
    assignments: Dict[str, StrategyAssignment]
    strategy_distribution: Dict[str, int]
    performance_summary: Dict[str, Any]
    market_condition_analysis: Dict[str, Any]
    recommendations: List[str]

class LiveStrategyAssignmentAgent(BaseAgent): # Inherit from BaseAgent
    """
    Agent responsible for assigning optimal strategies to selected stocks
    """
    
    def __init__(self, event_bus: Any, config: Any, session_id: str): # Modified constructor
        super().__init__("LiveStrategyAssignmentAgent", event_bus, config, session_id) # Call super constructor
        self.strategy_config = self.config['strategy_assignment']
        self.available_strategies = self.strategy_config['available_strategies']
        self.selection_logic = self.strategy_config['selection_logic']
        self.performance_expectations = self.strategy_config['performance_expectations']
        self.market_matching = self.strategy_config['market_condition_matching']
        
        # Strategy assignments
        self.assignments: Dict[str, StrategyAssignment] = {}
        self.assignment_result: Optional[StrategyAssignmentResult] = None
        
    async def initialize(self) -> bool:
        """Initialize the agent"""
        self.log_info("🎯 Initializing Live Strategy Assignment Agent...")
        try:
            self.initialized = True
            self.log_info("✓ Live Strategy Assignment Agent initialized")
            return True
        except Exception as e:
            self.log_error(f"Failed to initialize Live Strategy Assignment Agent: {e}")
            self.initialized = False
            return False
            
    async def start(self):
        """Start the agent - currently no continuous operations"""
        self.log_info("Live Strategy Assignment Agent started. Ready to assign strategies on demand.")
        self.running = True

    async def stop(self):
        """Stop the agent and clean up resources"""
        self.log_info("Stopping Live Strategy Assignment Agent...")
        self.running = False
        self.log_info("🧹 Live Strategy Assignment Agent stopped.")
            
    async def assign_strategies(self, 
                              selected_stocks: List[str],
                              predictions: Dict[str, PredictionResult],
                              features: Dict[str, FeatureEngineering],
                              current_market_conditions: Optional[Dict[str, Any]] = None) -> StrategyAssignmentResult:
        """
        Assign strategies to selected stocks
        
        Args:
            selected_stocks: List of selected stock symbols
            predictions: ML predictions for each stock
            features: Feature engineering results for each stock
            current_market_conditions: Current market condition analysis
            
        Returns:
            StrategyAssignmentResult with strategy assignments
        """
        self.log_info(f"🎯 Starting strategy assignment for {len(selected_stocks)} stocks...")
        self.update_activity()
        
        try:
            # Analyze current market conditions if not provided
            if current_market_conditions is None:
                current_market_conditions = self._analyze_current_market_conditions(features)
                
            # Assign strategies for each stock
            for symbol in selected_stocks:
                try:
                    assignment = await self._assign_stock_strategy(
                        symbol, predictions.get(symbol), features.get(symbol), current_market_conditions
                    )
                    
                    if assignment:
                        self.assignments[symbol] = assignment
                        self.log_debug(f"✓ Strategy assigned for {symbol}: {assignment.primary_strategy}")
                    else:
                        self.log_warning(f"⚠️ Failed to assign strategy for {symbol}")
                        
                except Exception as e:
                    self.log_error(f"❌ Error assigning strategy for {symbol}: {e}")
                    continue
                    
            # Generate comprehensive result
            assignment_result = self._generate_assignment_result(current_market_conditions)
            self.assignment_result = assignment_result
            
            self.log_info(f"🎯 Strategy assignment complete: {len(self.assignments)} assignments made")
            return assignment_result
            
        except Exception as e:
            self.log_error(f"Strategy assignment failed: {e}")
            raise
            
    def _analyze_current_market_conditions(self, features: Dict[str, FeatureEngineering]) -> Dict[str, Any]:
        """Analyze current market conditions from stock features"""
        try:
            if not features:
                return {'regime': 'unknown', 'volatility': 'normal', 'trend': 'neutral'}
                
            # Aggregate market conditions across all stocks
            volatilities = []
            trend_strengths = []
            
            for symbol, feature_eng in features.items():
                if feature_eng.market_conditions:
                    # Extract volatility regime
                    vol_regime = feature_eng.market_conditions.get('volatility_regime', 'normal_volatility')
                    if vol_regime == 'high_volatility':
                        volatilities.append(1.0)
                    elif vol_regime == 'low_volatility':
                        volatilities.append(-1.0)
                    else:
                        volatilities.append(0.0)
                        
                    # Extract trend strength
                    trend_data = feature_eng.market_conditions.get('trend_strength', {})
                    if isinstance(trend_data, dict):
                        slope = trend_data.get('linear_regression_slope', 0)
                        trend_strengths.append(slope)
                        
            # Determine overall market conditions
            avg_volatility = np.mean(volatilities) if volatilities else 0
            avg_trend = np.mean(trend_strengths) if trend_strengths else 0
            
            # Classify market regime
            if avg_volatility > 0.3:
                volatility_regime = 'high_volatility'
            elif avg_volatility < -0.3:
                volatility_regime = 'low_volatility'
            else:
                volatility_regime = 'normal_volatility'
                
            if avg_trend > 0.01:
                trend_regime = 'trending_up'
            elif avg_trend < -0.01:
                trend_regime = 'trending_down'
            else:
                trend_regime = 'ranging'
                
            return {
                'volatility_regime': volatility_regime,
                'trend_regime': trend_regime,
                'avg_volatility_score': avg_volatility,
                'avg_trend_strength': avg_trend,
                'confidence': min(1.0, len(features) / 10)  # Higher confidence with more stocks
            }
            
        except Exception as e:
            self.log_error(f"Market condition analysis failed: {e}")
            return {'regime': 'unknown', 'volatility': 'normal', 'trend': 'neutral'}
            
    async def _assign_stock_strategy(self, 
                                   symbol: str,
                                   prediction: Optional[PredictionResult],
                                   features: Optional[FeatureEngineering],
                                   market_conditions: Dict[str, Any]) -> Optional[StrategyAssignment]:
        """Assign strategy to a single stock"""
        try:
            if not prediction or not prediction.is_valid:
                self.log_warning(f"Invalid prediction for {symbol}")
                return None
                
            # Calculate strategy suitability scores
            strategy_scores = self._calculate_strategy_suitability(prediction, features, market_conditions)
            
            if not strategy_scores:
                self.log_warning(f"No strategy scores calculated for {symbol}")
                return None
                
            # Select primary strategy
            primary_strategy, primary_confidence = self._select_primary_strategy(strategy_scores)
            
            # Select backup strategy
            backup_strategy, backup_confidence = self._select_backup_strategy(strategy_scores, primary_strategy)
            
            # Calculate expected performance
            expected_performance = self._calculate_expected_performance(
                symbol, primary_strategy, prediction, features
            )
            
            # Calculate market condition match
            market_condition_match = self._calculate_market_condition_match(
                primary_strategy, market_conditions
            )
            
            # Generate assignment rationale
            assignment_rationale = self._generate_assignment_rationale(
                primary_strategy, primary_confidence, market_conditions, strategy_scores
            )
            
            # Generate risk warnings
            risk_warnings = self._generate_risk_warnings(symbol, prediction, primary_strategy)
            
            return StrategyAssignment(
                symbol=symbol,
                primary_strategy=primary_strategy,
                primary_confidence=primary_confidence,
                backup_strategy=backup_strategy,
                backup_confidence=backup_confidence,
                expected_performance=expected_performance,
                market_condition_match=market_condition_match,
                assignment_rationale=assignment_rationale,
                risk_warnings=risk_warnings
            )
            
        except Exception as e:
            self.log_error(f"Strategy assignment failed for {symbol}: {e}")
            return None
            
    def _calculate_strategy_suitability(self, 
                                      prediction: PredictionResult,
                                      features: Optional[FeatureEngineering],
                                      market_conditions: Dict[str, Any]) -> Dict[str, float]:
        """Calculate suitability scores for each strategy"""
        try:
            strategy_scores = {}
            
            # Start with ML prediction scores
            if prediction.strategy_suitability:
                strategy_scores.update(prediction.strategy_suitability)
                
            # Adjust scores based on market conditions
            current_volatility = market_conditions.get('volatility_regime', 'normal_volatility')
            current_trend = market_conditions.get('trend_regime', 'ranging')
            
            for strategy_name, strategy_config in self.available_strategies.items():
                base_score = strategy_scores.get(strategy_name, 0.5)  # Default score
                
                # Market condition matching
                suitable_conditions = strategy_config.get('market_conditions', [])
                condition_match = 0.0
                
                if current_volatility in suitable_conditions:
                    condition_match += 0.3
                if current_trend in suitable_conditions:
                    condition_match += 0.3
                    
                # Combine base score with condition match
                if self.market_matching['enabled']:
                    current_weight = self.market_matching['current_market_weight']
                    historical_weight = self.market_matching['historical_performance_weight']
                    
                    adjusted_score = (base_score * historical_weight + 
                                    (base_score + condition_match) * current_weight)
                else:
                    adjusted_score = base_score
                    
                # Apply minimum confidence threshold
                min_confidence = strategy_config.get('min_confidence', 0.5)
                if adjusted_score >= min_confidence:
                    strategy_scores[strategy_name] = adjusted_score
                else:
                    strategy_scores[strategy_name] = 0.0  # Below threshold
                    
            return strategy_scores
            
        except Exception as e:
            self.log_error(f"Strategy suitability calculation failed: {e}")
            return {}
            
    def _select_primary_strategy(self, strategy_scores: Dict[str, float]) -> Tuple[str, float]:
        """Select primary strategy based on selection logic"""
        try:
            method = self.selection_logic['primary_strategy_method']
            
            if method == 'highest_confidence':
                # Select strategy with highest score
                best_strategy = max(strategy_scores.items(), key=lambda x: x[1])
                return best_strategy[0], best_strategy[1]
                
            elif method == 'best_fit':
                # More complex logic could be implemented here
                # For now, fall back to highest confidence
                best_strategy = max(strategy_scores.items(), key=lambda x: x[1])
                return best_strategy[0], best_strategy[1]
                
            else:
                # Default to highest confidence
                best_strategy = max(strategy_scores.items(), key=lambda x: x[1])
                return best_strategy[0], best_strategy[1]
                
        except Exception as e:
            self.log_error(f"Primary strategy selection failed: {e}")
            return 'momentum', 0.5  # Default fallback
            
    def _select_backup_strategy(self, strategy_scores: Dict[str, float], primary_strategy: str) -> Tuple[str, float]:
        """Select backup strategy"""
        try:
            if not self.selection_logic['backup_strategy_required']:
                return '', 0.0
                
            # Remove primary strategy from consideration
            backup_scores = {k: v for k, v in strategy_scores.items() if k != primary_strategy}
            
            if not backup_scores:
                return '', 0.0
                
            method = self.selection_logic['backup_strategy_method']
            
            if method == 'second_best':
                # Select strategy with second highest score
                best_backup = max(backup_scores.items(), key=lambda x: x[1])
                
                # Check minimum confidence gap
                min_gap = self.selection_logic['min_confidence_gap']
                primary_confidence = strategy_scores[primary_strategy]
                
                if primary_confidence - best_backup[1] >= min_gap:
                    return best_backup[0], best_backup[1]
                else:
                    return '', 0.0  # Gap too small
                    
            else:
                # Default to second best
                best_backup = max(backup_scores.items(), key=lambda x: x[1])
                return best_backup[0], best_backup[1]
                
        except Exception as e:
            self.log_error(f"Backup strategy selection failed: {e}")
            return '', 0.0

    def _calculate_expected_performance(self,
                                      symbol: str,
                                      strategy: str,
                                      prediction: PredictionResult,
                                      features: Optional[FeatureEngineering]) -> Dict[str, float]:
        """Calculate expected performance metrics for assigned strategy"""
        try:
            if not self.performance_expectations['calculate_expected_metrics']:
                return {}

            expected_metrics = {}

            # Expected return (from ML prediction)
            if prediction.expected_return is not None:
                expected_metrics['expected_return'] = prediction.expected_return

            # Expected Sharpe ratio (simplified calculation)
            if prediction.expected_return is not None and prediction.risk_metrics:
                volatility = prediction.risk_metrics.get('volatility', 0.02)
                risk_free_rate = 0.05  # 5% risk-free rate

                if volatility > 0:
                    expected_sharpe = (prediction.expected_return - risk_free_rate) / volatility
                    expected_metrics['expected_sharpe'] = expected_sharpe

            # Expected drawdown (from risk metrics)
            if prediction.risk_metrics:
                expected_drawdown = prediction.risk_metrics.get('expected_drawdown', 0.1)
                expected_metrics['expected_drawdown'] = expected_drawdown

            # Strategy-specific adjustments
            strategy_config = self.available_strategies.get(strategy, {})
            min_confidence = strategy_config.get('min_confidence', 0.5)

            # Adjust metrics based on strategy confidence
            confidence_factor = min(1.0, prediction.expected_return_confidence or 0.5)
            for metric in expected_metrics:
                if metric != 'expected_drawdown':  # Don't adjust drawdown upward
                    expected_metrics[metric] *= confidence_factor

            return expected_metrics

        except Exception as e:
            self.log_error(f"Expected performance calculation failed: {e}")
            return {}

    def _calculate_market_condition_match(self, strategy: str, market_conditions: Dict[str, Any]) -> float:
        """Calculate how well strategy matches current market conditions"""
        try:
            strategy_config = self.available_strategies.get(strategy, {})
            suitable_conditions = strategy_config.get('market_conditions', [])

            if not suitable_conditions:
                return 0.5  # Neutral match

            current_volatility = market_conditions.get('volatility_regime', 'normal_volatility')
            current_trend = market_conditions.get('trend_regime', 'ranging')

            match_score = 0.0

            # Check volatility match
            if current_volatility in suitable_conditions:
                match_score += 0.5

            # Check trend match
            if current_trend in suitable_conditions:
                match_score += 0.5

            return min(1.0, match_score)

        except Exception as e:
            self.log_error(f"Market condition match calculation failed: {e}")
            return 0.5

    def _generate_assignment_rationale(self,
                                     strategy: str,
                                     confidence: float,
                                     market_conditions: Dict[str, Any],
                                     strategy_scores: Dict[str, float]) -> str:
        """Generate human-readable rationale for strategy assignment"""
        try:
            rationale_parts = []

            # Primary reason
            rationale_parts.append(f"Selected {strategy} strategy with {confidence:.1%} confidence")

            # Market condition reasoning
            volatility_regime = market_conditions.get('volatility_regime', 'normal')
            trend_regime = market_conditions.get('trend_regime', 'ranging')

            rationale_parts.append(f"Current market: {volatility_regime} volatility, {trend_regime} trend")

            # Strategy comparison
            sorted_strategies = sorted(strategy_scores.items(), key=lambda x: x[1], reverse=True)
            if len(sorted_strategies) > 1:
                second_best = sorted_strategies[1]
                margin = confidence - second_best[1]
                rationale_parts.append(f"Outperformed {second_best[0]} by {margin:.1%}")

            return ". ".join(rationale_parts)

        except Exception as e:
            self.log_error(f"Rationale generation failed: {e}")
            return f"Selected {strategy} strategy based on ML analysis"

    def _generate_risk_warnings(self, symbol: str, prediction: PredictionResult, strategy: str) -> List[str]:
        """Generate risk warnings for strategy assignment"""
        try:
            warnings = []

            # Low confidence warning
            if prediction.expected_return_confidence and prediction.expected_return_confidence < 0.6:
                warnings.append("Low prediction confidence - monitor closely")

            # High volatility warning
            if prediction.risk_metrics:
                volatility = prediction.risk_metrics.get('volatility', 0)
                if volatility > 0.04:  # 4% daily volatility
                    warnings.append("High volatility detected - consider position sizing")

                # High drawdown warning
                expected_drawdown = prediction.risk_metrics.get('expected_drawdown', 0)
                if expected_drawdown > 0.15:  # 15% drawdown
                    warnings.append("High expected drawdown - implement strict stop-loss")

            # Strategy-specific warnings
            strategy_config = self.available_strategies.get(strategy, {})
            min_confidence = strategy_config.get('min_confidence', 0.5)

            if prediction.expected_return_confidence and prediction.expected_return_confidence < min_confidence + 0.1:
                warnings.append(f"Strategy confidence near minimum threshold for {strategy}")

            return warnings

        except Exception as e:
            self.log_error(f"Risk warning generation failed: {e}")
            return []

    def _generate_assignment_result(self, market_conditions: Dict[str, Any]) -> StrategyAssignmentResult:
        """Generate comprehensive assignment result"""
        try:
            # Strategy distribution
            strategy_distribution = {}
            for assignment in self.assignments.values():
                strategy = assignment.primary_strategy
                strategy_distribution[strategy] = strategy_distribution.get(strategy, 0) + 1

            # Performance summary
            expected_returns = [a.expected_performance.get('expected_return', 0)
                              for a in self.assignments.values()]
            expected_sharpes = [a.expected_performance.get('expected_sharpe', 0)
                              for a in self.assignments.values()]

            performance_summary = {
                'avg_expected_return': np.mean(expected_returns) if expected_returns else 0,
                'avg_expected_sharpe': np.mean(expected_sharpes) if expected_sharpes else 0,
                'total_assignments': len(self.assignments),
                'avg_primary_confidence': np.mean([a.primary_confidence for a in self.assignments.values()]),
                'avg_market_condition_match': np.mean([a.market_condition_match for a in self.assignments.values()])
            }

            # Generate recommendations
            recommendations = self._generate_recommendations(strategy_distribution, performance_summary, market_conditions)

            return StrategyAssignmentResult(
                assignments=self.assignments.copy(),
                strategy_distribution=strategy_distribution,
                performance_summary=performance_summary,
                market_condition_analysis=market_conditions,
                recommendations=recommendations
            )

        except Exception as e:
            self.log_error(f"Assignment result generation failed: {e}")
            return StrategyAssignmentResult(
                assignments={},
                strategy_distribution={},
                performance_summary={},
                market_condition_analysis={},
                recommendations=[]
            )

    def _generate_recommendations(self,
                                strategy_distribution: Dict[str, int],
                                performance_summary: Dict[str, Any],
                                market_conditions: Dict[str, Any]) -> List[str]:
        """Generate actionable recommendations"""
        try:
            recommendations = []

            # Strategy diversification recommendations
            total_assignments = sum(strategy_distribution.values())
            if total_assignments > 0:
                max_concentration = max(strategy_distribution.values()) / total_assignments
                if max_concentration > 0.6:
                    recommendations.append("Consider diversifying strategy allocation - high concentration detected")

            # Performance-based recommendations
            avg_return = performance_summary.get('avg_expected_return', 0)
            if avg_return < 0.02:  # Less than 2% expected return
                recommendations.append("Low expected returns - consider increasing selectivity or adjusting criteria")

            avg_sharpe = performance_summary.get('avg_expected_sharpe', 0)
            if avg_sharpe < 0.5:
                recommendations.append("Low risk-adjusted returns - review risk management parameters")

            # Market condition recommendations
            volatility_regime = market_conditions.get('volatility_regime', 'normal')
            if volatility_regime == 'high_volatility':
                recommendations.append("High volatility environment - consider reducing position sizes")
            elif volatility_regime == 'low_volatility':
                recommendations.append("Low volatility environment - opportunity for higher position sizes")

            # Confidence-based recommendations
            avg_confidence = performance_summary.get('avg_primary_confidence', 0)
            if avg_confidence < 0.7:
                recommendations.append("Low average confidence - consider additional validation or smaller positions")

            return recommendations

        except Exception as e:
            self.log_error(f"Recommendation generation failed: {e}")
            return []

    def get_assignment_result(self) -> Optional[StrategyAssignmentResult]:
        """Get the latest assignment result"""
        return self.assignment_result

    def get_strategy_distribution(self) -> Dict[str, int]:
        """Get distribution of assigned strategies"""
        distribution = {}
        for assignment in self.assignments.values():
            strategy = assignment.primary_strategy
            distribution[strategy] = distribution.get(strategy, 0) + 1
        return distribution

# Example usage and testing
async def main():
    """Example usage of Live Strategy Assignment Agent"""
    try:
        # For testing, create a dummy config and event bus
        class DummyEventBus:
            async def publish(self, event_type: str, payload: Dict):
                print(f"Event Published: {event_type} - {payload}")

        dummy_config = {
            'strategy_assignment': {
                'available_strategies': {
                    'momentum': {'min_confidence': 0.6, 'market_conditions': ['trending_up', 'high_volatility']},
                    'mean_reversion': {'min_confidence': 0.5, 'market_conditions': ['ranging', 'low_volatility']},
                    'breakout': {'min_confidence': 0.7, 'market_conditions': ['high_volatility']},
                    'trend_following': {'min_confidence': 0.65, 'market_conditions': ['trending_up', 'trending_down']}
                },
                'selection_logic': {
                    'primary_strategy_method': 'highest_confidence',
                    'backup_strategy_required': True,
                    'backup_strategy_method': 'second_best',
                    'min_confidence_gap': 0.1
                },
                'performance_expectations': {
                    'calculate_expected_metrics': True
                },
                'market_condition_matching': {
                    'enabled': True,
                    'current_market_weight': 0.4,
                    'historical_performance_weight': 0.6
                }
            }
        }
        event_bus = DummyEventBus()
        session_id = "test_session_123"

        agent = LiveStrategyAssignmentAgent(event_bus, dummy_config, session_id)
        await agent.initialize()
        await agent.start()

        # Create sample data for testing
        selected_stocks = ["RELIANCE", "TCS", "INFY", "HDFC", "ICICIBANK"]

        # Create sample predictions
        sample_predictions = {}
        sample_features = {}

        for symbol in selected_stocks:
            # Sample prediction
            sample_predictions[symbol] = PredictionResult(
                symbol=symbol,
                expected_return=np.random.normal(0.05, 0.03),
                expected_return_confidence=np.random.uniform(0.6, 0.9),
                risk_metrics={
                    'volatility': np.random.uniform(0.01, 0.04),
                    'expected_drawdown': np.random.uniform(0.05, 0.15)
                },
                strategy_suitability={
                    'momentum': np.random.uniform(0.4, 0.9),
                    'mean_reversion': np.random.uniform(0.3, 0.8),
                    'breakout': np.random.uniform(0.3, 0.9),
                    'trend_following': np.random.uniform(0.4, 0.8)
                },
                confidence_intervals={},
                model_versions={},
                prediction_quality=np.random.uniform(0.7, 0.95),
                is_valid=True
            )

            # Sample features (simplified)
            sample_features[symbol] = FeatureEngineering(
                symbol=symbol,
                features={},
                target_return=np.random.normal(0.05, 0.03),
                market_conditions={
                    'volatility_regime': np.random.choice(['low_volatility', 'normal_volatility', 'high_volatility']),
                    'trend_strength': {'linear_regression_slope': np.random.normal(0, 0.01)}
                },
                feature_count=50,
                is_valid=True,
                quality_score=0.85
            )

        # Assign strategies
        result = await agent.assign_strategies(selected_stocks, sample_predictions, sample_features)

        # Print results
        print(f"\n🎯 Strategy Assignment Results:")
        print(f"Assigned strategies for {len(result.assignments)} stocks")

        print(f"\n📊 Strategy Distribution:")
        for strategy, count in result.strategy_distribution.items():
            percentage = count / len(result.assignments) * 100
            print(f"  {strategy:15s}: {count:2d} stocks ({percentage:4.1f}%)")

        print(f"\n📈 Performance Summary:")
        print(f"  Average expected return: {result.performance_summary['avg_expected_return']:.2%}")
        print(f"  Average expected Sharpe: {result.performance_summary['avg_expected_sharpe']:.2f}")
        print(f"  Average confidence: {result.performance_summary['avg_primary_confidence']:.1%}")

        print(f"\n🎯 Individual Assignments:")
        for symbol, assignment in result.assignments.items():
            print(f"  {symbol:10s}: {assignment.primary_strategy:15s} ({assignment.primary_confidence:.1%})")
            if assignment.backup_strategy:
                print(f"             Backup: {assignment.backup_strategy:15s} ({assignment.backup_confidence:.1%})")

        print(f"\n💡 Recommendations:")
        for rec in result.recommendations:
            print(f"  • {rec}")

    except Exception as e:
        logger.error(f"Example failed: {e}")

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    asyncio.run(main())
