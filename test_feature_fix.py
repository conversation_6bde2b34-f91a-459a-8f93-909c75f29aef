#!/usr/bin/env python3
"""
Test script to verify the feature engineering fixes
"""

import sys
import polars as pl
import numpy as np
from pathlib import Path
from datetime import datetime, timedelta
import asyncio
import logging

# Add project root to path
sys.path.append(str(Path(__file__).parent))

from agents.live_feature_engineering_agent import LiveFeatureEngineeringAgent
from core.event_system import EventBus
import yaml

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_feature_engineering():
    """Test the feature engineering with sample data"""
    
    # Create sample OHLCV data
    start_date = datetime.now() - timedelta(days=30)
    end_date = datetime.now()
    
    # Create minute-by-minute timestamps
    timestamps = []
    current = start_date
    while current <= end_date:
        timestamps.append(current)
        current += timedelta(minutes=1)
    
    dates = pl.Series("timestamp", timestamps)
    
    n_rows = len(dates)
    np.random.seed(42)  # For reproducible results
    
    # Generate realistic OHLCV data
    base_price = 100.0
    price_changes = np.random.normal(0, 0.01, n_rows).cumsum()
    close_prices = base_price * (1 + price_changes)
    
    # Generate OHLC from close prices
    high_prices = close_prices * (1 + np.abs(np.random.normal(0, 0.005, n_rows)))
    low_prices = close_prices * (1 - np.abs(np.random.normal(0, 0.005, n_rows)))
    open_prices = np.roll(close_prices, 1)
    open_prices[0] = base_price
    
    # Generate volume
    volumes = np.random.randint(1000, 10000, n_rows)
    
    sample_data = pl.DataFrame({
        'timestamp': dates,
        'open': open_prices,
        'high': high_prices,
        'low': low_prices,
        'close': close_prices,
        'volume': volumes
    })
    
    logger.info(f"Created sample data with {len(sample_data)} rows")
    
    # Create minimal configuration for testing
    config = {
        'feature_engineering': {
            'technical_indicators': {
                'volatility': {
                    'enabled': True,
                    'rolling_window': 20,
                    'methods': ['std', 'atr']
                },
                'rsi': {
                    'enabled': True,
                    'periods': [14],
                    'overbought': 70,
                    'oversold': 30
                },
                'macd': {
                    'enabled': True,
                    'fast_period': 12,
                    'slow_period': 26,
                    'signal_period': 9
                },
                'bollinger_bands': {
                    'enabled': True,
                    'period': 20,
                    'std_dev': 2.0,
                    'calculate_percent_b': True
                },
                'ema': {
                    'enabled': True,
                    'periods': [5, 20]
                },
                'stochastic': {
                    'enabled': True,
                    'k_period': 14,
                    'smooth_k': 3,
                    'd_period': 3
                },
                'volume': {
                    'enabled': True,
                    'sma_period': 20,
                    'volume_ratio_period': 10
                },
                'momentum': {
                    'enabled': True,
                    'roc_period': 10,
                    'williams_r_period': 14
                }
            },
            'market_conditions': {
                'trend_strength': {'enabled': False},
                'volatility_regime': {'enabled': False}
            },
            'target_variable': {
                'forward_return_days': 5
            }
        }
    }
    
    # Create event bus and agent
    event_bus = EventBus()
    agent = LiveFeatureEngineeringAgent(event_bus, config, "test_session")
    
    # Initialize agent
    await agent.initialize()
    
    # Test feature calculation
    logger.info("Testing feature calculation...")
    
    stock_data = {"TEST_SYMBOL": sample_data}
    results = await agent.calculate_features_for_stocks(stock_data)
    
    if results:
        logger.info(f"✅ Feature calculation successful!")
        for symbol, features in results.items():
            logger.info(f"Symbol: {symbol}")
            logger.info(f"Features calculated: {features.feature_count}")
            logger.info(f"Quality score: {features.quality_score:.2f}")
            logger.info(f"Is valid: {features.is_valid}")
            logger.info(f"Sample features: {list(features.features.keys())[:10]}")
    else:
        logger.error("❌ Feature calculation failed!")
        
    return len(results) > 0

if __name__ == "__main__":
    success = asyncio.run(test_feature_engineering())
    if success:
        print("✅ All tests passed! Feature engineering fixes are working.")
    else:
        print("❌ Tests failed! There are still issues with feature engineering.")
        sys.exit(1)