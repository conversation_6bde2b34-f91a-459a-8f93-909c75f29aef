#!/usr/bin/env python3
"""
Runner script for AI Training Agent
Demonstrates usage and provides command-line interface
"""

import argparse
import asyncio
import logging
import sys
import os
from pathlib import Path

# Add current directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ai_training_agent import AITrainingAgent, AITrainingConfig
from ai_training_utils import load_config, setup_logging, get_system_info, check_dependencies
import pandas as pd
import tempfile
import shutil

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def main():
    """Main execution function"""
    parser = argparse.ArgumentParser(
        description='AI Training Agent - Multi-Target Strategy Performance Prediction',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Train with default settings
  python run_ai_training.py
  
  # Train with custom data file
  python run_ai_training.py --data-file data/backtest/custom_results.parquet
  
  # Train without hyperparameter optimization (faster)
  python run_ai_training.py --no-optimize
  
  # Use custom configuration
  python run_ai_training.py --config config/custom_ai_config.yaml
  
  # Test mode with limited trials
  python run_ai_training.py --test-mode
  
  # Load existing models and make predictions
  python run_ai_training.py --predict-only --load-models ai_training_ensemble
        """
    )
    
    # Data arguments
    parser.add_argument(
        '--backtest-dir', 
        type=str, 
        default='data/backtest',
        help='Path to directory containing backtesting output files (parquet)'
    )
    
    parser.add_argument(
        '--config', 
        type=str, 
        default='config/ai_training_config.yaml',
        help='Path to configuration file'
    )
    
    # Training arguments
    parser.add_argument(
        '--no-optimize', 
        action='store_true',
        help='Skip hyperparameter optimization (faster training)'
    )
    
    parser.add_argument(
        '--test-mode', 
        action='store_true',
        help='Run in test mode with reduced trials and epochs'
    )
    
    parser.add_argument(
        '--optuna-trials', 
        type=int, 
        help='Number of Optuna trials for hyperparameter optimization'
    )
    
    # Model arguments
    parser.add_argument(
        '--predict-only', 
        action='store_true',
        help='Only make predictions, skip training'
    )
    
    parser.add_argument(
        '--load-models', 
        type=str,
        help='Load existing models by name'
    )
    
    parser.add_argument(
        '--save-models', 
        type=str,
        default='ai_training_ensemble',
        help='Name to save trained models'
    )
    
    # Hardware arguments
    parser.add_argument(
        '--cpu-only', 
        action='store_true',
        help='Force CPU-only training (disable GPU)'
    )
    
    parser.add_argument(
        '--gpu-memory', 
        type=float,
        help='GPU memory fraction to use (0.0-1.0)'
    )
    
    # Logging arguments
    parser.add_argument(
        '--log-level', 
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
        default='INFO',
        help='Logging level'
    )
    
    parser.add_argument(
        '--log-file', 
        type=str,
        help='Log file path'
    )
    
    # Output arguments
    parser.add_argument(
        '--output-dir', 
        type=str,
        help='Output directory for results'
    )
    
    parser.add_argument(
        '--verbose', 
        action='store_true',
        help='Enable verbose output'
    )
    
    args = parser.parse_args()
    
    # Setup logging
    setup_logging(args.log_level, args.log_file)
    
    logger.info("[INIT] Starting AI Training Agent...")
    
    # Check system info
    system_info = get_system_info()
    dependencies = check_dependencies()
    
    logger.info(f"[SYSTEM] System Info:")
    logger.info(f"   CPU cores: {system_info['cpu_count']}")
    logger.info(f"   Memory: {system_info['memory_total_gb']:.1f} GB")
    logger.info(f"   GPU available: {system_info['gpu_available']}")
    if system_info['gpu_available']:
        logger.info(f"   GPU memory: {system_info['gpu_memory_gb']:.1f} GB")
    
    # Check dependencies
    missing_deps = [dep for dep, available in dependencies.items() if not available]
    if missing_deps:
        logger.warning(f"[WARN]  Missing dependencies: {missing_deps}")
    
    try:
        # Load configuration
        if os.path.exists(args.config):
            config_dict = load_config(args.config)
            config = AITrainingConfig()
            
            # Override with command line arguments
            # Data loading will be handled directly in main, so these config adjustments are removed
            # if args.data_file:
            #     config.input_file = os.path.basename(args.data_file)
            #     config.data_dir = os.path.dirname(args.data_file)
            
            if args.optuna_trials:
                config.optuna_trials = args.optuna_trials
            
            if args.cpu_only:
                config.use_gpu = False
            
            if args.output_dir:
                config.models_dir = args.output_dir
            
            # Test mode adjustments
            if args.test_mode:
                config.optuna_trials = 5
                config.lgb_params['num_boost_round'] = 50
                config.tabnet_params['max_epochs'] = 10
                logger.info("🧪 Running in test mode with reduced parameters")
        
        else:
            logger.warning(f"[WARN]  Config file not found: {args.config}, using defaults")
            config = AITrainingConfig()
        
        # Initialize agent
        agent = AITrainingAgent(config)
        
        # Load existing models if requested
        if args.load_models:
            try:
                agent.load_models(args.load_models)
                logger.info(f"[SUCCESS] Loaded existing models: {args.load_models}")
            except FileNotFoundError:
                logger.error(f"[ERROR] Models not found: {args.load_models}")
                if args.predict_only:
                    return
        
        # Training or prediction
        if args.predict_only:
            if not agent.is_trained:
                logger.error("[ERROR] No trained models available for prediction")
                return
            
            logger.info("🔮 Making sample predictions...")
            
            # Create sample data for demonstration
            import numpy as np
            sample_features = np.random.randn(5, len(config.feature_columns))
            predictions, confidence = agent.predict(sample_features)
            
            # Rank strategies
            strategy_names = [f'Sample_Strategy_{i}' for i in range(5)]
            rankings = agent.rank_strategies(predictions, strategy_names)
            
            print("\n" + "="*80)
            print("[TARGET] SAMPLE PREDICTIONS")
            print("="*80)
            for i, ranking in enumerate(rankings):
                print(f"\n{i+1}. {ranking['strategy_name']}")
                print(f"   Composite Score: {ranking['composite_score']:.4f}")
                print(f"   Predicted Metrics:")
                for metric, value in ranking['predicted_metrics'].items():
                    print(f"      {metric}: {value:.4f}")
        
        else:
            # Load backtesting data
            backtest_data_path = Path(args.backtest_dir)
            if not backtest_data_path.exists():
                logger.error(f"[ERROR] Backtesting data directory not found: {backtest_data_path}")
                return
            
            parquet_files = list(backtest_data_path.glob("backtest_*.parquet"))
            if not parquet_files:
                logger.error(f"[ERROR] No backtest_*.parquet files found in {backtest_data_path}")
                return
            
            logger.info(f"[INFO] Loading {len(parquet_files)} backtesting files from {backtest_data_path}...")
            all_data = []
            for f in parquet_files:
                try:
                    df = pd.read_parquet(f)
                    all_data.append(df)
                except Exception as e:
                    logger.warning(f"[WARN] Could not load {f}: {e}")
            
            if not all_data:
                logger.error("[ERROR] No valid backtesting data could be loaded.")
                return

            combined_df = pd.concat(all_data, ignore_index=True)
            logger.info(f"[INFO] Combined backtesting data shape: {combined_df.shape}")

            temp_dir = None
            temp_parquet_path = None
            try:
                # Save combined_df to a temporary parquet file
                temp_dir = tempfile.mkdtemp()
                temp_parquet_path = os.path.join(temp_dir, "combined_backtest_data.parquet")
                combined_df.to_parquet(temp_parquet_path, index=False)
                logger.info(f"[INFO] Saved combined data to temporary file: {temp_parquet_path}")

                # Train models
                logger.info("[TARGET] Starting model training...")
                
                optimize_hyperparams = not args.no_optimize
                results = await agent.train_async(temp_parquet_path, optimize_hyperparams)
                
                # Save models
                agent.save_models(args.save_models)
            finally:
                if temp_dir and os.path.exists(temp_dir):
                    shutil.rmtree(temp_dir)
                    logger.info(f"[INFO] Cleaned up temporary directory: {temp_dir}")
            
            # Print results
            print("\n" + "="*80)
            print("[TARGET] AI TRAINING AGENT RESULTS")
            print("="*80)
            print(f"[STATUS] Data processed: {results['data_shape'][0]:,} rows, {results['data_shape'][1]} columns")
            print(f"[CONFIG] Features used: {results['feature_count']}")
            print(f"[TARGET] Targets predicted: {results['target_count']}")
            print(f"[METRICS] Training samples: {results['train_samples']:,}")
            print(f"[STATUS] Validation samples: {results['val_samples']:,}")
            print(f"🧪 Test samples: {results['test_samples']:,}")
            
            print("\n[STATUS] EVALUATION METRICS:")
            for target, metrics in results['evaluation_metrics'].items():
                if target != 'overall':
                    print(f"   {target}:")
                    print(f"      RMSE: {metrics['rmse']:.4f}")
                    print(f"      R²: {metrics['r2']:.4f}")
            
            print(f"\n[TARGET] OVERALL PERFORMANCE:")
            overall = results['evaluation_metrics']['overall']
            print(f"   RMSE: {overall['rmse']:.4f}")
            print(f"   R²: {overall['r2']:.4f}")
            print(f"   Mean Confidence: {overall['mean_confidence']:.4f}")
            
            # Feature importance
            if results['feature_importance']:
                print("\n[DEBUG] TOP FEATURE IMPORTANCE:")
                from ai_training_utils import calculate_feature_importance_summary
                avg_importance = calculate_feature_importance_summary(results['feature_importance'])
                for i, (feature, importance) in enumerate(list(avg_importance.items())[:10]):
                    print(f"   {i+1:2d}. {feature}: {importance:.4f}")
            
            print(f"\n💾 Models saved as: {args.save_models}")
            print("\n[SUCCESS] Training completed successfully!")
    
    except Exception as e:
        logger.error(f"[ERROR] Error: {str(e)}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
