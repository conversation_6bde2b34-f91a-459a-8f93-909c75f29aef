#!/usr/bin/env python3
"""
ENHANCED CONTINUOUS LIVE TRADING SYSTEM
AI-Driven Continuous Market Monitoring and Trading with Real WebSocket Data

Features:
🚀 Multi-Mode Trading Support:
   - LIVE: Real trading with real money using SmartAPI
   - PAPER: Virtual trading with real market data (1 Lac initial balance)
   - DEMO: Simulation mode with mock data

📊 Enhanced Market Monitoring:
   - Real-time WebSocket data streaming from Angel One SmartAPI 2.0
   - 40-day historical data download for pre-market preparation
   - Multi-timeframe candle generation (1min → 3min, 5min, 15min)
   - Intelligent batch processing with API rate limit handling

🧠 AI-Powered Decision Making:
   - Signal Generation, Risk Management, and Execution agents
   - Continuous market monitoring during trading hours
   - Maximum 5 trades per day with intelligent position management
   - Auto square-off before market close

💰 Paper Trading Features:
   - Virtual account with ₹1,00,000 initial balance
   - Real-time P&L tracking with realistic brokerage and taxes
   - Commission simulation (₹20 flat + 0.03% + taxes)
   - Position sizing and risk management

🔒 Safety & Risk Management:
   - Pre-trade risk validation
   - Position monitoring and trailing stop-loss
   - Market hours validation
   - Graceful shutdown handling

📋 Usage Examples:
   # Paper Trading (Recommended for testing)
   python run_continuous_live_trading.py --mode paper --max-trades 5

   # Live Trading (Real money - Use with caution!)
   python run_continuous_live_trading.py --mode live --max-trades 3

   # Demo Mode (Simulation with mock data)
   python run_continuous_live_trading.py --mode demo --max-trades 10

🔧 Prerequisites:
   - SmartAPI credentials in .env file:
     SMARTAPI_API_KEY=your_api_key
     SMARTAPI_USERNAME=your_username
     SMARTAPI_PASSWORD=your_password
     SMARTAPI_TOTP_TOKEN=your_totp_token
   
   - Required packages:
     pip install smartapi-python pyotp polars python-dotenv

⚠️  Important Notes:
   - Paper mode uses real market data but virtual money
   - Live mode places actual orders on NSE - USE WITH EXTREME CAUTION
   - System automatically stops at market close (3:30 PM)
   - Maximum 5 trades per day limit (configurable)
   - All trades are intraday (MIS) and auto-squared off before close
"""

import os
import sys
import asyncio
import logging
import argparse
import signal
from pathlib import Path
from datetime import datetime, time as dt_time, timedelta
from typing import Dict, List, Any, Optional, Tuple
import json
import polars as pl
import numpy as np
from dataclasses import dataclass, asdict
from enum import Enum
import warnings
warnings.filterwarnings('ignore')

# Add project root to path
sys.path.append(str(Path(__file__).parent))

# Import agents and utilities
from agents.signal_generation_agent import SignalGenerationAgent
from agents.risk_agent import RiskManagementAgent
from agents.execution_agent import ExecutionAgent
from agents.market_monitoring_agent import MarketMonitoringAgent
from run_enhanced_paper_trading import EnhancedPaperTradingWorkflow
from utils.nse_500_universe import NSE500Universe
from utils.config_loader import load_config_for_agent, ConfigurationLoader
from utils.paper_trading import VirtualAccount, PaperTrade, OrderStatus, TransactionType

# Import enhanced WebSocket manager
try:
    from utils.enhanced_websocket_manager import EnhancedWebSocketManager
    WEBSOCKET_AVAILABLE = True
except ImportError:
    WEBSOCKET_AVAILABLE = False

# Import SmartAPI for historical data
try:
    from SmartApi import SmartConnect
    from SmartApi.smartWebSocketV2 import SmartWebSocketV2
    import pyotp
    SMARTAPI_AVAILABLE = True
except ImportError:
    SMARTAPI_AVAILABLE = False

# Import data models
try:
    from utils.execution_models import SignalPayload
except ImportError:
    # Fallback if execution_models doesn't exist
    @dataclass
    class SignalPayload:
        symbol: str
        exchange: str
        symbol_token: str
        action: str
        entry_price: float
        sl_price: float
        target_price: float
        quantity: int
        strategy_name: str
        signal_id: str

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'logs/continuous_trading_{datetime.now().strftime("%Y%m%d")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# ═══════════════════════════════════════════════════════════════════════════════
# [MODELS] Enhanced Data Models
# ═══════════════════════════════════════════════════════════════════════════════

class TradingMode(Enum):
    """Trading modes"""
    LIVE = "live"      # Real trading with real money
    PAPER = "paper"    # Virtual trading with real data
    DEMO = "demo"      # Simulation with mock data

@dataclass
class MarketDataPoint:
    """Real-time market data point"""
    symbol: str
    timestamp: datetime
    open: float
    high: float
    low: float
    close: float
    volume: int
    ltp: float  # Last traded price
    
@dataclass
class HistoricalDataRequest:
    """Historical data request parameters"""
    symbol: str
    exchange: str
    symboltoken: str
    interval: str  # ONE_MINUTE, THREE_MINUTE, FIVE_MINUTE, FIFTEEN_MINUTE
    fromdate: str  # YYYY-MM-DD HH:MM
    todate: str    # YYYY-MM-DD HH:MM

class EnhancedContinuousLiveTradingSystem:
    """
    Enhanced Continuous Live Trading System with Multi-Mode Support
    
    Features:
    - Real WebSocket data streaming
    - Paper trading with virtual account
    - Live trading with real money
    - Historical data pre-loading
    - Multi-timeframe analysis
    """
    
    def __init__(self, mode: str = "paper", max_daily_trades: int = 5, initial_balance: float = 100000):
        """Initialize enhanced continuous trading system"""
        
        # Validate mode
        if mode not in ["live", "paper", "demo"]:
            raise ValueError("Mode must be 'live', 'paper', or 'demo'")
            
        self.mode = TradingMode(mode)
        self.max_daily_trades = max_daily_trades
        self.trades_today = 0
        self.running = False
        self.shutdown_event = asyncio.Event()
        
        # Agent instances
        self.signal_agent = None
        self.risk_agent = None
        self.execution_agent = None
        self.market_agent = None
        self.websocket_manager = None
        self.smartapi_client = None
        
        # Paper trading account (for paper mode)
        self.paper_account = None
        if self.mode == TradingMode.PAPER:
            # Create config for VirtualAccount
            paper_config = {
                'paper_trading': {
                    'initial_balance': initial_balance,
                    'commission_rate': 0.0003,  # 0.03%
                    'flat_brokerage': 20.0,     # ₹20 per trade
                    'stt_rate': 0.001,          # 0.1%
                    'gst_rate': 0.18,           # 18%
                    'stamp_duty_rate': 0.00003  # 0.003%
                }
            }
            
            # Set environment variables for VirtualAccount
            import os
            os.environ['PAPER_TRADING_INITIAL_BALANCE'] = str(initial_balance)
            os.environ['PAPER_TRADING_COMMISSION_RATE'] = '0.0003'
            os.environ['PAPER_TRADING_BROKERAGE_FLAT'] = '20'
            os.environ['PAPER_TRADING_STT_RATE'] = '0.001'
            os.environ['PAPER_TRADING_GST_RATE'] = '0.18'
            os.environ['PAPER_TRADING_STAMP_DUTY_RATE'] = '0.00003'
            
            self.paper_account = VirtualAccount(paper_config)
        
        # Trading state
        self.active_positions = {}
        self.selected_stocks = []
        self.last_signal_check = None
        self.trading_session_id = f"session_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # Market data storage
        self.market_data = {}  # Real-time data
        self.historical_data = {}  # Pre-loaded historical data
        self.timeframe_data = {  # Multi-timeframe data
            "1min": {},
            "3min": {},
            "5min": {},
            "15min": {}
        }
        
        # Performance tracking
        self.session_stats = {
            "signals_generated": 0,
            "trades_executed": 0,
            "trades_rejected": 0,
            "total_pnl": 0.0,
            "start_time": datetime.now(),
            "last_activity": datetime.now(),
            "websocket_messages": 0,
            "historical_data_points": 0,
            "paper_trades": 0 if self.mode != TradingMode.PAPER else 0
        }
        
        # Load environment variables
        self._load_credentials()
        
        logger.info(f"ENHANCED CONTINUOUS LIVE TRADING SYSTEM initialized")
        logger.info(f"[MODE] Trading mode: {self.mode.value.upper()}")
        logger.info(f"[TRADES] Max daily trades: {max_daily_trades}")
        logger.info(f"[SESSION] Session ID: {self.trading_session_id}")
        
        if self.mode == TradingMode.PAPER:
            logger.info(f"[PAPER] Initial balance: ₹{initial_balance:,.2f}")
        elif self.mode == TradingMode.LIVE:
            logger.warning("[LIVE] REAL MONEY TRADING MODE - USE WITH CAUTION!")
    
    def _load_credentials(self):
        """Load SmartAPI credentials from environment"""
        try:
            from dotenv import load_dotenv
            load_dotenv()
            
            self.api_key = os.getenv('SMARTAPI_API_KEY')
            self.username = os.getenv('SMARTAPI_USERNAME')
            self.password = os.getenv('SMARTAPI_PASSWORD')
            self.totp_token = os.getenv('SMARTAPI_TOTP_TOKEN')
            
            if not all([self.api_key, self.username, self.password, self.totp_token]):
                logger.warning("[WARN] SmartAPI credentials not found in .env file")
                self.credentials_available = False
            else:
                self.credentials_available = True
                logger.info("[SUCCESS] SmartAPI credentials loaded")
                
        except ImportError:
            logger.warning("[WARN] python-dotenv not available. Install with: pip install python-dotenv")
            self.credentials_available = False
        except Exception as e:
            logger.error(f"[ERROR] Failed to load credentials: {e}")
            self.credentials_available = False
    
    async def initialize_smartapi_client(self) -> bool:
        """Initialize SmartAPI client for data and trading"""
        try:
            if not SMARTAPI_AVAILABLE:
                logger.error("[ERROR] SmartAPI not available. Install with: pip install smartapi-python")
                return False
                
            if not self.credentials_available:
                logger.error("[ERROR] SmartAPI credentials not available")
                return False
            
            print("   [API] Initializing SmartAPI client...")
            
            # Initialize SmartConnect
            self.smartapi_client = SmartConnect(api_key=self.api_key)
            
            # Generate TOTP
            import pyotp
            totp = pyotp.TOTP(self.totp_token)
            totp_code = totp.now()
            
            # Login
            data = self.smartapi_client.generateSession(
                clientCode=self.username,
                password=self.password,
                totp=totp_code
            )
            
            if data['status']:
                self.auth_token = data['data']['jwtToken']
                self.refresh_token = data['data']['refreshToken']
                self.feed_token = self.smartapi_client.getfeedToken()
                
                print(f"   [SUCCESS] SmartAPI authenticated successfully")
                print(f"   [INFO] Feed token: {self.feed_token[:10]}...")
                return True
            else:
                logger.error(f"[ERROR] SmartAPI authentication failed: {data.get('message', 'Unknown error')}")
                return False
                
        except Exception as e:
            logger.error(f"[ERROR] Failed to initialize SmartAPI client: {e}")
            return False
    
    async def download_historical_data(self, symbols: List[str], days: int = 40) -> bool:
        """Download historical data for selected symbols"""
        try:
            if not self.smartapi_client:
                logger.error("[ERROR] SmartAPI client not initialized")
                return False
            
            print(f"   [DATA] Downloading {days}-day historical data for {len(symbols)} symbols...")
            
            # Load instrument master for symbol tokens
            try:
                from utils.instrument_master import InstrumentMaster
                instrument_master = InstrumentMaster()
                await instrument_master.load_instruments()
            except ImportError:
                # Fallback instrument master
                class SimpleInstrumentMaster:
                    def __init__(self):
                        self.instruments = {}
                    
                    async def load_instruments(self):
                        # This would normally load from SmartAPI or file
                        # For now, we'll populate with common symbols
                        common_symbols = {
                            'RELIANCE': '2885',
                            'TCS': '11536',
                            'HDFCBANK': '1333',
                            'INFY': '1594',
                            'ICICIBANK': '4963',
                            'SBIN': '3045',
                            'BHARTIARTL': '10604',
                            'ITC': '424',
                            'KOTAKBANK': '1922',
                            'LT': '11483'
                        }
                        for symbol, token in common_symbols.items():
                            self.instruments[symbol] = {'token': token, 'symbol': symbol, 'exchange': 'NSE'}
                    
                    def get_instrument_by_symbol(self, symbol: str, exchange: str = "NSE"):
                        return self.instruments.get(symbol)
                
                instrument_master = SimpleInstrumentMaster()
                await instrument_master.load_instruments()
            
            # Calculate date range
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)
            
            # API rate limits: Max 30 days per call, so we need to batch
            batch_size = 30
            total_downloaded = 0
            
            for symbol in symbols:
                try:
                    # Get symbol token
                    instrument = instrument_master.get_instrument_by_symbol(symbol, "NSE")
                    if not instrument:
                        logger.warning(f"[WARN] Instrument not found for {symbol}")
                        continue
                    
                    symbol_token = instrument['token']
                    
                    # Download in batches due to 30-day limit
                    current_start = start_date
                    symbol_data = []
                    
                    while current_start < end_date:
                        batch_end = min(current_start + timedelta(days=batch_size), end_date)
                        
                        # Format dates for API
                        from_date = current_start.strftime("%Y-%m-%d %H:%M")
                        to_date = batch_end.strftime("%Y-%m-%d %H:%M")
                        
                        try:
                            # Download 1-minute data
                            hist_data = self.smartapi_client.getCandleData({
                                "exchange": "NSE",
                                "symboltoken": symbol_token,
                                "interval": "ONE_MINUTE",
                                "fromdate": from_date,
                                "todate": to_date
                            })
                            
                            if hist_data['status'] and hist_data['data']:
                                batch_data = hist_data['data']
                                symbol_data.extend(batch_data)
                                total_downloaded += len(batch_data)
                                
                                print(f"   [SUCCESS] Downloaded {len(batch_data)} candles for {symbol} ({from_date} to {to_date})")
                            else:
                                logger.warning(f"[WARN] No data for {symbol} in batch {from_date} to {to_date}")
                            
                            # Rate limiting - wait between requests
                            await asyncio.sleep(0.5)
                            
                        except Exception as e:
                            logger.error(f"[ERROR] Failed to download batch for {symbol}: {e}")
                            continue
                        
                        current_start = batch_end
                    
                    if symbol_data:
                        # Convert to DataFrame and store
                        df_data = []
                        for candle in symbol_data:
                            df_data.append({
                                'timestamp': datetime.strptime(candle[0], "%Y-%m-%dT%H:%M:%S%z"),
                                'open': float(candle[1]),
                                'high': float(candle[2]),
                                'low': float(candle[3]),
                                'close': float(candle[4]),
                                'volume': int(candle[5])
                            })
                        
                        # Store 1-minute data
                        self.timeframe_data["1min"][symbol] = pl.DataFrame(df_data)
                        
                        # Generate higher timeframes
                        await self._generate_higher_timeframes(symbol)
                        
                        print(f"   [SUCCESS] Processed {len(symbol_data)} 1-min candles for {symbol}")
                    
                except Exception as e:
                    logger.error(f"[ERROR] Failed to download data for {symbol}: {e}")
                    continue
            
            self.session_stats["historical_data_points"] = total_downloaded
            print(f"   [SUCCESS] Downloaded {total_downloaded} total data points")
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to download historical data: {e}")
            return False
    
    async def _generate_higher_timeframes(self, symbol: str):
        """Generate 3min, 5min, 15min timeframes from 1min data"""
        try:
            if symbol not in self.timeframe_data["1min"]:
                return
            
            df_1min = self.timeframe_data["1min"][symbol]
            
            # Generate 3-minute candles
            df_3min = df_1min.group_by_dynamic(
                "timestamp",
                every="3m",
                closed="left"
            ).agg([
                pl.col("open").first(),
                pl.col("high").max(),
                pl.col("low").min(),
                pl.col("close").last(),
                pl.col("volume").sum()
            ])
            self.timeframe_data["3min"][symbol] = df_3min
            
            # Generate 5-minute candles
            df_5min = df_1min.group_by_dynamic(
                "timestamp",
                every="5m",
                closed="left"
            ).agg([
                pl.col("open").first(),
                pl.col("high").max(),
                pl.col("low").min(),
                pl.col("close").last(),
                pl.col("volume").sum()
            ])
            self.timeframe_data["5min"][symbol] = df_5min
            
            # Generate 15-minute candles
            df_15min = df_1min.group_by_dynamic(
                "timestamp",
                every="15m",
                closed="left"
            ).agg([
                pl.col("open").first(),
                pl.col("high").max(),
                pl.col("low").min(),
                pl.col("close").last(),
                pl.col("volume").sum()
            ])
            self.timeframe_data["15min"][symbol] = df_15min
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to generate higher timeframes for {symbol}: {e}")
    
    async def initialize_websocket(self, symbols: List[str]) -> bool:
        """Initialize WebSocket connection for real-time data"""
        try:
            if not SMARTAPI_AVAILABLE:
                logger.error("[ERROR] SmartAPI WebSocket not available")
                return False
            
            if not self.auth_token or not self.feed_token:
                logger.error("[ERROR] Authentication tokens not available")
                return False
            
            print("   [WEBSOCKET] Initializing WebSocket connection...")
            
            # Load instrument master for symbol tokens
            try:
                from utils.instrument_master import InstrumentMaster
                instrument_master = InstrumentMaster()
                await instrument_master.load_instruments()
            except ImportError:
                # Use the same fallback as above
                class SimpleInstrumentMaster:
                    def __init__(self):
                        self.instruments = {}
                    
                    async def load_instruments(self):
                        common_symbols = {
                            'RELIANCE': '2885', 'TCS': '11536', 'HDFCBANK': '1333',
                            'INFY': '1594', 'ICICIBANK': '4963', 'SBIN': '3045',
                            'BHARTIARTL': '10604', 'ITC': '424', 'KOTAKBANK': '1922', 'LT': '11483'
                        }
                        for symbol, token in common_symbols.items():
                            self.instruments[symbol] = {'token': token, 'symbol': symbol, 'exchange': 'NSE'}
                    
                    def get_instrument_by_symbol(self, symbol: str, exchange: str = "NSE"):
                        return self.instruments.get(symbol)
                
                instrument_master = SimpleInstrumentMaster()
                await instrument_master.load_instruments()
            
            # Prepare subscription data
            subscription_data = []
            for symbol in symbols:
                instrument = instrument_master.get_instrument_by_symbol(symbol, "NSE")
                if instrument:
                    subscription_data.append({
                        "exchangeType": 1,  # NSE
                        "tokens": [instrument['token']]
                    })
            
            if not subscription_data:
                logger.error("[ERROR] No valid instruments found for WebSocket subscription")
                return False
            
            # Initialize WebSocket
            self.websocket_client = SmartWebSocketV2(
                auth_token=self.auth_token,
                api_key=self.api_key,
                client_code=self.username,
                feed_token=self.feed_token
            )
            
            # Set up callbacks
            self.websocket_client.on_open = self._on_websocket_open
            self.websocket_client.on_data = self._on_websocket_data
            self.websocket_client.on_error = self._on_websocket_error
            self.websocket_client.on_close = self._on_websocket_close
            
            # Connect WebSocket
            self.websocket_client.connect()
            
            # Subscribe to symbols
            await asyncio.sleep(2)  # Wait for connection
            self.websocket_client.subscribe("mw", subscription_data)
            
            print(f"   [SUCCESS] WebSocket connected and subscribed to {len(symbols)} symbols")
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to initialize WebSocket: {e}")
            return False
    
    def _on_websocket_open(self, ws):
        """WebSocket open callback"""
        print("[WEBSOCKET] Connection opened")
    
    def _on_websocket_data(self, ws, message):
        """WebSocket data callback"""
        try:
            self.session_stats["websocket_messages"] += 1
            
            # Process real-time market data
            if isinstance(message, dict):
                symbol_token = message.get('tk')
                ltp = message.get('lp')
                
                if symbol_token and ltp:
                    # Find symbol by token
                    symbol = self._get_symbol_by_token(symbol_token)
                    if symbol:
                        # Update real-time data
                        self.market_data[symbol] = MarketDataPoint(
                            symbol=symbol,
                            timestamp=datetime.now(),
                            open=message.get('o', ltp),
                            high=message.get('h', ltp),
                            low=message.get('l', ltp),
                            close=ltp,
                            volume=message.get('v', 0),
                            ltp=ltp
                        )
                        
                        # Update 1-minute candle data
                        self._update_realtime_candle(symbol, message)
            
        except Exception as e:
            logger.error(f"[ERROR] WebSocket data processing error: {e}")
    
    def _on_websocket_error(self, ws, error):
        """WebSocket error callback"""
        logger.error(f"[WEBSOCKET] Error: {error}")
    
    def _on_websocket_close(self, ws):
        """WebSocket close callback"""
        print("[WEBSOCKET] Connection closed")
    
    def _get_symbol_by_token(self, token: str) -> Optional[str]:
        """Get symbol by token (requires instrument master lookup)"""
        # Simple reverse lookup for common symbols
        token_to_symbol = {
            '2885': 'RELIANCE',
            '11536': 'TCS',
            '1333': 'HDFCBANK',
            '1594': 'INFY',
            '4963': 'ICICIBANK',
            '3045': 'SBIN',
            '10604': 'BHARTIARTL',
            '424': 'ITC',
            '1922': 'KOTAKBANK',
            '11483': 'LT'
        }
        return token_to_symbol.get(str(token))
    
    def _update_realtime_candle(self, symbol: str, data: dict):
        """Update real-time 1-minute candle data"""
        try:
            # This would update the current 1-minute candle
            # Implementation depends on candle building logic
            pass
        except Exception as e:
            logger.error(f"[ERROR] Failed to update real-time candle for {symbol}: {e}")

    async def initialize_agents(self) -> bool:
        """Initialize all trading agents with enhanced capabilities"""
        try:
            print("\n[AI] Initializing Enhanced AI Trading Agents...")
            
            # Step 1: Initialize SmartAPI client (for all modes)
            if self.mode != TradingMode.DEMO:
                print("   [API] Step 1: Initializing SmartAPI client...")
                if not await self.initialize_smartapi_client():
                    if self.mode == TradingMode.LIVE:
                        logger.error("[ERROR] SmartAPI required for live trading")
                        return False
                    else:
                        logger.warning("[WARN] SmartAPI not available, using fallback for paper trading")
            
            # Step 2: Initialize Market Monitoring Agent
            print("   [MONITOR] Step 2: Initializing Enhanced Market Monitoring Agent...")
            self.market_agent = MarketMonitoringAgent()
            await self.market_agent.setup()
            
            # Pass real-time data capabilities to market agent
            if hasattr(self.market_agent, 'set_data_source'):
                self.market_agent.set_data_source(self.timeframe_data)
            
            print("   [SUCCESS] Market Monitoring Agent ready with real-time capabilities")
            
            # Step 3: Initialize Signal Generation Agent
            print("   [SIGNAL] Step 3: Initializing AI Signal Generation Agent...")
            self.signal_agent = SignalGenerationAgent()
            await self.signal_agent.setup()
            print("   [SUCCESS] Signal Generation Agent ready")
            
            # Step 4: Initialize Risk Management Agent
            print("   [RISK] Step 4: Initializing Risk Management Agent...")
            self.risk_agent = RiskManagementAgent()
            await self.risk_agent.setup()
            print("   [SUCCESS] Risk Management Agent ready")
            
            # Step 5: Initialize Execution Agent (mode-specific)
            print(f"   [EXEC] Step 5: Initializing Execution Agent ({self.mode.value} mode)...")
            try:
                if self.mode == TradingMode.PAPER:
                    # For paper trading, use paper trading execution
                    self.execution_agent = ExecutionAgent(trading_mode="paper")
                    # Pass paper account to execution agent
                    if hasattr(self.execution_agent, 'set_paper_account'):
                        self.execution_agent.set_paper_account(self.paper_account)
                elif self.mode == TradingMode.LIVE:
                    # For live trading, use real execution
                    self.execution_agent = ExecutionAgent(trading_mode="live")
                    # Pass SmartAPI client to execution agent
                    if hasattr(self.execution_agent, 'set_smartapi_client'):
                        self.execution_agent.set_smartapi_client(self.smartapi_client)
                else:  # DEMO mode
                    self.execution_agent = ExecutionAgent(trading_mode="demo")
                
                success = await self.execution_agent.initialize()
                
                if not success:
                    logger.error("[ERROR] Failed to initialize Execution Agent")
                    return False
                    
            except Exception as e:
                logger.error(f"[ERROR] Exception during Execution Agent initialization: {e}")
                print(f"   [ERROR] Execution Agent error: {e}")
                return False
            
            # Display mode-specific success message
            if self.mode == TradingMode.LIVE:
                print("   [SUCCESS] Execution Agent ready for LIVE TRADING")
                print("   [ALERT] REAL MONEY WILL BE USED!")
            elif self.mode == TradingMode.PAPER:
                print("   [SUCCESS] Execution Agent ready for PAPER TRADING")
                print(f"   [BALANCE] Virtual balance: ₹{self.paper_account.current_balance:,.2f}")
            else:
                print("   [SUCCESS] Execution Agent ready for DEMO MODE")
            
            # Step 6: Connect agents for coordination
            self.signal_agent.risk_management_agent = self.risk_agent
            
            # Pass market data to signal agent
            if hasattr(self.signal_agent, 'set_market_data'):
                self.signal_agent.set_market_data(self.timeframe_data)
            
            print("   [CONNECT] Agent coordination established")
            print("   [SUCCESS] All agents initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to initialize agents: {e}")
            return False
    
    async def execute_paper_trade(self, signal: SignalPayload) -> Tuple[bool, str, Optional[Dict]]:
        """Execute paper trade using virtual account"""
        try:
            if not self.paper_account:
                return False, "Paper account not initialized", None
            
            # Get current market price (from real-time data or historical)
            current_price = signal.entry_price
            if signal.symbol in self.market_data:
                current_price = self.market_data[signal.symbol].get('ltp', signal.entry_price)
            
            # Execute trade through paper account using correct signature
            success, message, trade_record = await self.paper_account.execute_trade(
                symbol=signal.symbol,
                exchange=signal.exchange,
                quantity=signal.quantity,
                price=current_price,
                transaction_type=signal.action,
                order_type="MARKET",  # Simplified for paper trading
                product_type="MIS",   # Intraday
                strategy_name=signal.strategy_name,
                signal_id=signal.signal_id
            )
            
            if success:
                self.session_stats["paper_trades"] = self.session_stats.get("paper_trades", 0) + 1
                
                # Calculate P&L impact
                pnl_impact = self.paper_account.unrealized_pnl
                self.session_stats["total_pnl"] = pnl_impact
                
                trade_execution = {
                    "trade_id": trade_record.trade_id if trade_record else f"PAPER_{signal.signal_id}",
                    "symbol": signal.symbol,
                    "action": signal.action,
                    "quantity": signal.quantity,
                    "price": current_price,
                    "timestamp": datetime.now().isoformat(),
                    "account_balance": self.paper_account.current_balance,
                    "unrealized_pnl": pnl_impact
                }
                
                print(f"   [PAPER] Trade executed: {signal.action} {signal.quantity} {signal.symbol} @ ₹{current_price:.2f}")
                print(f"   [BALANCE] Account balance: ₹{self.paper_account.current_balance:,.2f}")
                print(f"   [PNL] Unrealized P&L: ₹{pnl_impact:,.2f}")
                
                return True, message, trade_execution
            else:
                return False, message, None
                
        except Exception as e:
            logger.error(f"[ERROR] Paper trade execution failed: {e}")
            return False, f"Paper trade execution error: {e}", None
    
    async def select_trading_universe(self) -> bool:
        """Select stocks for continuous monitoring with enhanced data preparation"""
        try:
            print("\n[TARGET] Selecting Enhanced Trading Universe...")
            
            # Use enhanced stock selection with continuous trading optimizations
            enhanced_workflow = EnhancedPaperTradingWorkflow(mode=self.mode.value)
            await enhanced_workflow._initialize_universe()

            # Override some parameters for continuous trading
            print("   [TARGET] Optimizing for continuous trading (more stocks, relaxed filters)...")
            await enhanced_workflow._select_trading_stocks()
            
            self.selected_stocks = enhanced_workflow.selected_stocks

            # Ensure minimum stock count for continuous trading
            if len(self.selected_stocks) < 5:
                print(f"   [WARNING] Only {len(self.selected_stocks)} stocks selected, adding more for continuous trading...")

                # Use the same universe as the enhanced workflow
                universe = enhanced_workflow.universe
                large_cap_stocks = universe.get_stocks_by_market_cap("Large")
                nifty_50_stocks = [s for s in large_cap_stocks if s.nifty_50]

                # Add stocks not already selected
                existing_symbols = {s.symbol for s in self.selected_stocks}
                additional_stocks = [s for s in nifty_50_stocks if s.symbol not in existing_symbols]

                print(f"   [INFO] Found {len(additional_stocks)} additional Nifty 50 stocks to add")

                # Add up to 15 total stocks for better opportunities
                needed = min(15 - len(self.selected_stocks), len(additional_stocks))
                if needed > 0:
                    self.selected_stocks.extend(additional_stocks[:needed])
                    print(f"   [SUCCESS] Added {needed} stocks: {', '.join([s.symbol for s in additional_stocks[:needed]])}")

                print(f"   [SUCCESS] Enhanced to {len(self.selected_stocks)} stocks for better trading opportunities")

            if not self.selected_stocks:
                logger.error("[ERROR] No stocks selected for trading")
                return False
            
            print(f"   [SUCCESS] Selected {len(self.selected_stocks)} stocks for monitoring")
            print(f"   [STOCKS] Top stocks: {', '.join([stock.symbol for stock in self.selected_stocks[:5]])}")

            # Extract symbol list for data operations
            symbol_list = [stock.symbol for stock in self.selected_stocks]
            
            # Pre-market data preparation
            await self._prepare_premarket_data(symbol_list)
            
            # Initialize real-time data streaming
            await self._initialize_realtime_streaming(symbol_list)

            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to select trading universe: {e}")
            return False
    
    async def _prepare_premarket_data(self, symbols: List[str]):
        """Prepare pre-market data with historical download and timeframe generation"""
        try:
            print("\n   [PREMARKET] Starting Pre-Market Data Preparation...")
            
            # Step 1: Download 40-day historical data
            if self.mode != TradingMode.DEMO and self.smartapi_client:
                print("   [DATA] Downloading 40-day historical data...")
                success = await self.download_historical_data(symbols, days=40)
                if success:
                    print(f"   [SUCCESS] Historical data downloaded for {len(symbols)} symbols")
                    print(f"   [INFO] Total data points: {self.session_stats['historical_data_points']:,}")
                else:
                    logger.warning("[WARN] Historical data download failed, using fallback")
            else:
                print("   [DEMO] Using demo mode - skipping historical data download")
                # Generate mock historical data for demo mode
                await self._generate_mock_historical_data(symbols)
            
            # Step 2: Generate multi-timeframe data
            print("   [TIMEFRAME] Generating multi-timeframe candles...")
            timeframes_generated = 0
            for symbol in symbols:
                if symbol in self.timeframe_data["1min"]:
                    await self._generate_higher_timeframes(symbol)
                    timeframes_generated += 1
            
            print(f"   [SUCCESS] Generated timeframes for {timeframes_generated} symbols")
            print("   [INFO] Available timeframes: 1min, 3min, 5min, 15min")
            
            # Step 3: Pass data to market monitoring agent
            if self.market_agent and hasattr(self.market_agent, 'set_historical_data'):
                self.market_agent.set_historical_data(self.timeframe_data)
                print("   [SUCCESS] Historical data passed to market monitoring agent")
            
            print("   [SUCCESS] Pre-market data preparation completed")
            
        except Exception as e:
            logger.error(f"[ERROR] Pre-market data preparation failed: {e}")
    
    async def _initialize_realtime_streaming(self, symbols: List[str]):
        """Initialize real-time data streaming"""
        try:
            print("\n   [STREAM] Initializing Real-Time Data Streaming...")
            
            if self.mode == TradingMode.DEMO:
                print("   [DEMO] Using demo mode - real-time streaming disabled")
                return
            
            # Initialize WebSocket for real-time data
            if self.smartapi_client and hasattr(self, 'auth_token'):
                print("   [WEBSOCKET] Setting up WebSocket connection...")
                success = await self.initialize_websocket(symbols)
                if success:
                    print(f"   [SUCCESS] WebSocket streaming active for {len(symbols)} symbols")
                else:
                    logger.warning("[WARN] WebSocket initialization failed")
            
            # Pass selected stocks to market monitoring agent
            if self.market_agent and hasattr(self.market_agent, 'set_selected_stocks'):
                self.market_agent.set_selected_stocks(self.selected_stocks)
                print(f"   [SUCCESS] Passed {len(self.selected_stocks)} stocks to market monitoring agent")

            # Start market monitoring background tasks
            if self.market_agent and hasattr(self.market_agent, 'start_background_tasks'):
                print("   [MONITOR] Starting market monitoring background tasks...")
                await self.market_agent.start_background_tasks()
                print("   [SUCCESS] Market monitoring active")
            
            print("   [SUCCESS] Real-time streaming initialization completed")
            
        except Exception as e:
            logger.error(f"[ERROR] Real-time streaming initialization failed: {e}")
    
    async def _generate_mock_historical_data(self, symbols: List[str]):
        """Generate mock historical data for demo mode"""
        try:
            print("   [MOCK] Generating mock historical data for demo mode...")
            
            import random
            from datetime import timedelta
            
            # Generate 40 days of 1-minute mock data
            end_time = datetime.now().replace(hour=15, minute=30, second=0, microsecond=0)
            start_time = end_time - timedelta(days=40)
            
            for symbol in symbols:
                mock_data = []
                current_time = start_time
                base_price = random.uniform(100, 3000)  # Random base price
                
                while current_time <= end_time:
                    # Skip weekends and non-trading hours
                    if current_time.weekday() < 5 and 9 <= current_time.hour < 16:
                        # Generate realistic OHLCV data
                        price_change = random.uniform(-0.02, 0.02)  # ±2% change
                        open_price = base_price * (1 + price_change)
                        high_price = open_price * (1 + random.uniform(0, 0.01))
                        low_price = open_price * (1 - random.uniform(0, 0.01))
                        close_price = random.uniform(low_price, high_price)
                        volume = random.randint(1000, 100000)
                        
                        mock_data.append({
                            'timestamp': current_time,
                            'open': open_price,
                            'high': high_price,
                            'low': low_price,
                            'close': close_price,
                            'volume': volume
                        })
                        
                        base_price = close_price  # Update base price
                    
                    current_time += timedelta(minutes=1)
                
                # Store mock data
                if mock_data:
                    self.timeframe_data["1min"][symbol] = pl.DataFrame(mock_data)
            
            print(f"   [SUCCESS] Generated mock data for {len(symbols)} symbols")
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to generate mock historical data: {e}")
    
    def is_market_hours(self) -> bool:
        """Check if current time is during market hours"""
        try:
            import pytz
            
            # Get current time in IST
            ist = pytz.timezone('Asia/Kolkata')
            now = datetime.now(ist)
            current_time = now.time()
            
            # Market hours: 9:15 AM to 3:30 PM
            market_open = dt_time(9, 15)
            market_close = dt_time(15, 30)
            
            # Check if it's a weekday
            is_weekday = now.weekday() < 5
            
            return is_weekday and market_open <= current_time <= market_close
            
        except Exception as e:
            logger.warning(f"Error checking market hours: {e}")
            return False
    
    def is_trading_window(self) -> bool:
        """Check if current time is within active trading window"""
        try:
            current_time = datetime.now().time()
            
            # Active trading window: 9:20 AM to 2:30 PM
            trading_start = dt_time(9, 20)
            trading_end = dt_time(14, 30)
            
            return trading_start <= current_time <= trading_end
            
        except Exception as e:
            logger.warning(f"Error checking trading window: {e}")
            return False
    
    async def wait_for_market_open(self):
        """Wait for market to open"""
        while not self.is_market_hours() and not self.shutdown_event.is_set():
            current_time = datetime.now().time()
            market_open = dt_time(9, 15)
            
            if current_time < market_open:
                wait_minutes = (datetime.combine(datetime.now().date(), market_open) - 
                              datetime.combine(datetime.now().date(), current_time)).total_seconds() / 60
                print(f"[TIME] Market opens in {wait_minutes:.0f} minutes. Waiting...")
            else:
                print("[MARKET] Market is closed (weekend/holiday). Waiting for next trading day...")
            
            await asyncio.sleep(60)  # Check every minute

    async def continuous_trading_loop(self):
        """Main continuous trading loop"""
        try:
            print("\n[LOOP] Starting Continuous Trading Loop...")
            print(f"[INFO] Mode: {self.mode.upper()}")
            print(f"[TARGET] Max daily trades: {self.max_daily_trades}")
            print(f"[TIME] Trading window: 09:20 - 14:30")

            self.running = True
            signal_check_interval = 30  # Check for signals every 30 seconds
            position_check_interval = 10  # Check positions every 10 seconds

            last_signal_check = datetime.now()
            last_position_check = datetime.now()

            while self.running and not self.shutdown_event.is_set():
                try:
                    current_time = datetime.now()

                    # Check if market is still open
                    if not self.is_market_hours():
                        print("[MARKET] Market closed. Ending trading session...")
                        break

                    # Update session stats
                    self.session_stats["last_activity"] = current_time

                    # Check for new trading signals
                    if (current_time - last_signal_check).total_seconds() >= signal_check_interval:
                        if self.is_trading_window() and self.trades_today < self.max_daily_trades:
                            await self._check_for_trading_signals()
                        last_signal_check = current_time

                    # Monitor existing positions
                    if (current_time - last_position_check).total_seconds() >= position_check_interval:
                        await self._monitor_positions()
                        last_position_check = current_time

                    # Auto square-off near market close
                    if current_time.time() >= dt_time(15, 20):
                        await self._auto_square_off_positions()
                        break

                    # Short sleep to prevent excessive CPU usage
                    await asyncio.sleep(5)

                except Exception as e:
                    logger.error(f"[ERROR] Error in trading loop: {e}")
                    await asyncio.sleep(10)  # Wait before retrying

            print("[SUCCESS] Continuous trading loop completed")

        except Exception as e:
            logger.error(f"[ERROR] Critical error in trading loop: {e}")
        finally:
            self.running = False

    async def _check_for_trading_signals(self):
        """Check for new trading signals from AI agents"""
        try:
            if not self.signal_agent or not self.selected_stocks:
                return

            # Rotate through selected stocks for signal generation
            for stock in self.selected_stocks:
                try:
                    # Generate signal using AI agent
                    # Get recent market data for the symbol
                    if self.market_agent and stock.symbol in self.market_agent.market_data:
                        # Get OHLCV data from market agent's stored data
                        ohlcv_data = list(self.market_agent.market_data[stock.symbol]['5min'])[-100:]

                        # Get indicators for the symbol
                        indicators = self.market_agent.get_indicators(stock.symbol)

                        # Get current market regime
                        market_regime = self.market_agent.get_market_regime()

                        if ohlcv_data and indicators:
                            # Process market data to generate signals
                            signals = await self.signal_agent.process_market_data(
                                symbol=stock.symbol,
                                ohlcv_data=ohlcv_data,
                                indicators=indicators,
                                market_regime=market_regime
                            )

                            # Get the first valid signal
                            signal = signals[0] if signals else None
                        else:
                            signal = None
                    else:
                        signal = None

                    if signal and signal.action in ["BUY", "SELL"]:
                        self.session_stats["signals_generated"] += 1

                        print(f"[TARGET] Signal generated: {signal.action} {signal.symbol} @ {signal.entry_price:.2f}")
                        print(f"   [INFO] Confidence: {signal.confidence:.2f}")
                        print(f"   [RISK] Stop Loss: {signal.stop_loss:.2f}")
                        print(f"   [TARGET] Take Profit: {signal.take_profit:.2f}")

                        # Process signal through risk management and execution
                        success = await self._process_trading_signal(signal)

                        if success:
                            self.trades_today += 1
                            print(f"[SUCCESS] Trade executed. Daily trades: {self.trades_today}/{self.max_daily_trades}")

                            # Stop if we've reached daily limit
                            if self.trades_today >= self.max_daily_trades:
                                print(f"[STOP] Daily trade limit reached ({self.max_daily_trades})")
                                break
                        else:
                            self.session_stats["trades_rejected"] += 1

                    # Small delay between stocks to avoid overwhelming the system
                    await asyncio.sleep(2)

                except Exception as e:
                    logger.error(f"[ERROR] Error generating signal for {stock.symbol}: {e}")
                    continue

        except Exception as e:
            logger.error(f"[ERROR] Error checking for trading signals: {e}")

    async def _process_trading_signal(self, signal) -> bool:
        """Process trading signal through risk management and execution"""
        try:
            # Step 1: Risk Management Validation
            if not self.risk_agent:
                logger.error("[ERROR] Risk agent not available")
                return False

            # Convert signal to trade request for risk validation
            try:
                from utils.risk_models import TradeRequest, TradeDirection, ProductType, OrderType
            except ImportError:
                # Fallback if risk_models doesn't exist - create basic classes
                from dataclasses import dataclass
                from enum import Enum

                class TradeDirection(Enum):
                    LONG = "LONG"
                    SHORT = "SHORT"

                class ProductType(Enum):
                    MIS = "MIS"
                    CNC = "CNC"

                class OrderType(Enum):
                    LIMIT = "LIMIT"
                    MARKET = "MARKET"

                @dataclass
                class TradeRequest:
                    signal_id: str
                    symbol: str
                    exchange: str
                    strategy_name: str
                    direction: TradeDirection
                    entry_price: float
                    stop_loss: float
                    take_profit: float
                    quantity: int
                    product_type: ProductType
                    order_type: OrderType
                    risk_amount: float
                    capital_allocated: float
                    risk_reward_ratio: float
                    market_regime: str
                    confidence: float
                    timestamp: any
                    context: dict

            trade_request = TradeRequest(
                signal_id=signal.signal_id,
                symbol=signal.symbol,
                exchange="NSE",
                strategy_name=signal.strategy_name,
                direction=TradeDirection.LONG if signal.signal_type == 1 else TradeDirection.SHORT,
                entry_price=signal.entry_price,
                stop_loss=signal.stop_loss,
                take_profit=signal.take_profit,
                quantity=signal.quantity,
                product_type=ProductType.MIS,
                order_type=OrderType.LIMIT,
                risk_amount=signal.risk_amount,
                capital_allocated=signal.capital_allocated,
                risk_reward_ratio=signal.risk_reward_ratio,
                market_regime=signal.market_regime,
                confidence=signal.confidence,
                timestamp=signal.timestamp,
                context=signal.context
            )

            # Validate with risk agent
            validation_result = await self.risk_agent.validate_trade(trade_request)

            if not validation_result.is_valid:
                print(f"[ERROR] Risk validation failed for {signal.symbol}: {validation_result.rejection_reason}")
                return False

            print(f"[SUCCESS] Risk validation passed for {signal.symbol}")

            # Step 2: Execute trade based on mode
            if self.mode == TradingMode.PAPER:
                # Use paper trading execution
                execution_signal = SignalPayload(
                    symbol=signal.symbol,
                    exchange="NSE",
                    symbol_token="",
                    action=signal.action,
                    entry_price=signal.entry_price,
                    sl_price=signal.stop_loss,
                    target_price=signal.take_profit,
                    quantity=signal.quantity,
                    strategy_name=signal.strategy_name,
                    signal_id=signal.signal_id
                )
                
                success, message, trade_execution = await self.execute_paper_trade(execution_signal)
                
            else:
                # Use real execution agent for live/demo trading
                if not self.execution_agent:
                    logger.error("[ERROR] Execution agent not available")
                    return False

                execution_signal = SignalPayload(
                    symbol=f"{signal.symbol}-EQ",
                    exchange="NSE",
                    symbol_token="",  # Will be resolved by execution agent
                    action=signal.action,
                    entry_price=signal.entry_price,
                    sl_price=signal.stop_loss,
                    target_price=signal.take_profit,
                    quantity=signal.quantity,
                    strategy_name=signal.strategy_name,
                    signal_id=signal.signal_id
                )

                success, message, trade_execution = await self.execution_agent.process_signal(execution_signal)

            if success:
                print(f"[SUCCESS] Trade executed successfully: {message}")

                # Track position
                self.active_positions[signal.symbol] = {
                    "signal": signal,
                    "trade_execution": trade_execution,
                    "entry_time": datetime.now(),
                    "status": "ACTIVE"
                }

                self.session_stats["trades_executed"] += 1
                
                # Update paper trading stats if applicable
                if self.mode == TradingMode.PAPER and self.paper_account:
                    print(f"   [PAPER] Account Balance: ₹{self.paper_account.current_balance:,.2f}")
                    print(f"   [PAPER] Unrealized P&L: ₹{self.paper_account.unrealized_pnl:,.2f}")
                    print(f"   [PAPER] Total P&L: ₹{(self.paper_account.realized_pnl + self.paper_account.unrealized_pnl):,.2f}")
                
                return True
            else:
                print(f"[ERROR] Trade execution failed: {message}")
                return False

        except Exception as e:
            logger.error(f"[ERROR] Error processing trading signal: {e}")
            return False

    async def _monitor_positions(self):
        """Monitor existing positions for exit signals"""
        try:
            if not self.active_positions:
                return

            for symbol, position_data in list(self.active_positions.items()):
                try:
                    signal = position_data["signal"]

                    # Check for exit signal from AI agent
                    # Get current market data for exit analysis
                    if self.market_agent and symbol in self.market_agent.market_data:
                        # Get recent OHLCV data from market agent's stored data
                        ohlcv_data = list(self.market_agent.market_data[symbol]['5min'])[-50:]

                        # Get indicators for the symbol
                        indicators = self.market_agent.get_indicators(symbol)

                        # Get current market regime
                        market_regime = self.market_agent.get_market_regime()

                        if ohlcv_data and indicators:
                            # Process market data to check for exit signals
                            signals = await self.signal_agent.process_market_data(
                                symbol=symbol,
                                ohlcv_data=ohlcv_data,
                                indicators=indicators,
                                market_regime=market_regime
                            )

                            # Look for exit signals or opposite direction signals
                            exit_signal = None
                            for sig in signals:
                                if (sig.action == "EXIT" or
                                    (signal.action == "BUY" and sig.action == "SELL") or
                                    (signal.action == "SELL" and sig.action == "BUY")):
                                    exit_signal = sig
                                    break
                        else:
                            exit_signal = None
                    else:
                        exit_signal = None

                    if exit_signal and exit_signal.action == "EXIT":
                        print(f"[EXIT] Exit signal generated for {symbol}")

                        # Execute exit through execution agent
                        success = await self._execute_position_exit(symbol, exit_signal)

                        if success:
                            # Remove from active positions
                            del self.active_positions[symbol]
                            print(f"[SUCCESS] Position closed for {symbol}")

                except Exception as e:
                    logger.error(f"[ERROR] Error monitoring position {symbol}: {e}")
                    continue

        except Exception as e:
            logger.error(f"[ERROR] Error monitoring positions: {e}")

    async def _execute_position_exit(self, symbol: str, exit_signal) -> bool:
        """Execute position exit"""
        try:
            if not self.execution_agent:
                return False

            # Create exit signal

            execution_signal = SignalPayload(
                symbol=f"{symbol}-EQ",
                exchange="NSE",
                symbol_token="",
                action="SELL" if exit_signal.original_action == "BUY" else "BUY",  # Opposite action
                entry_price=exit_signal.exit_price,
                sl_price=0,  # No SL for exit
                target_price=0,  # No target for exit
                quantity=exit_signal.quantity,
                strategy_name=exit_signal.strategy_name,
                signal_id=f"EXIT_{exit_signal.signal_id}"
            )

            success, message, trade_execution = await self.execution_agent.process_signal(execution_signal)

            if success:
                print(f"[SUCCESS] Position exit executed: {message}")
                return True
            else:
                print(f"[ERROR] Position exit failed: {message}")
                return False

        except Exception as e:
            logger.error(f"[ERROR] Error executing position exit: {e}")
            return False

    async def _auto_square_off_positions(self):
        """Auto square-off all positions near market close"""
        try:
            if not self.active_positions:
                return

            print("[LOOP] Auto square-off: Closing all positions before market close...")

            for symbol, position_data in list(self.active_positions.items()):
                try:
                    signal = position_data["signal"]

                    # Create square-off signal

                    execution_signal = SignalPayload(
                        symbol=f"{symbol}-EQ",
                        exchange="NSE",
                        symbol_token="",
                        action="SELL" if signal.action == "BUY" else "BUY",
                        entry_price=0,  # Market price
                        sl_price=0,
                        target_price=0,
                        quantity=signal.quantity,
                        strategy_name="AUTO_SQUARE_OFF",
                        signal_id=f"SQUARE_OFF_{signal.signal_id}"
                    )

                    success, message, _ = await self.execution_agent.process_signal(execution_signal)

                    if success:
                        print(f"[SUCCESS] Auto square-off completed for {symbol}")
                        del self.active_positions[symbol]
                    else:
                        print(f"[ERROR] Auto square-off failed for {symbol}: {message}")

                except Exception as e:
                    logger.error(f"[ERROR] Error in auto square-off for {symbol}: {e}")
                    continue

        except Exception as e:
            logger.error(f"[ERROR] Error in auto square-off: {e}")

    async def generate_session_report(self) -> Dict[str, Any]:
        """Generate comprehensive session report"""
        try:
            end_time = datetime.now()
            session_duration = end_time - self.session_stats["start_time"]

            # Base report structure
            report = {
                "session_id": self.trading_session_id,
                "mode": self.mode.value,
                "start_time": self.session_stats["start_time"].isoformat(),
                "end_time": end_time.isoformat(),
                "duration_minutes": session_duration.total_seconds() / 60,
                "trading_stats": {
                    "signals_generated": self.session_stats["signals_generated"],
                    "trades_executed": self.session_stats["trades_executed"],
                    "trades_rejected": self.session_stats["trades_rejected"],
                    "success_rate": (self.session_stats["trades_executed"] /
                                   max(self.session_stats["signals_generated"], 1)) * 100,
                    "daily_trade_limit": self.max_daily_trades,
                    "trades_remaining": max(0, self.max_daily_trades - self.trades_today)
                },
                "positions": {
                    "active_positions": len(self.active_positions),
                    "position_details": list(self.active_positions.keys())
                },
                "selected_stocks": [stock.symbol for stock in self.selected_stocks] if self.selected_stocks else [],
                "data_stats": {
                    "historical_data_points": self.session_stats.get("historical_data_points", 0),
                    "websocket_messages": self.session_stats.get("websocket_messages", 0),
                    "timeframes_available": ["1min", "3min", "5min", "15min"]
                },
                "system_status": {
                    "signal_agent": "ACTIVE" if self.signal_agent else "INACTIVE",
                    "risk_agent": "ACTIVE" if self.risk_agent else "INACTIVE",
                    "execution_agent": "ACTIVE" if self.execution_agent else "INACTIVE",
                    "market_agent": "ACTIVE" if self.market_agent else "INACTIVE",
                    "smartapi_client": "ACTIVE" if self.smartapi_client else "INACTIVE",
                    "websocket_client": "ACTIVE" if hasattr(self, 'websocket_client') and self.websocket_client else "INACTIVE"
                }
            }
            
            # Add paper trading specific stats
            if self.mode == TradingMode.PAPER and self.paper_account:
                account_summary = self.paper_account.get_account_summary()
                report["paper_trading"] = {
                    "initial_balance": self.paper_account.initial_balance,
                    "current_balance": self.paper_account.current_balance,
                    "total_trades": len(self.paper_account.trades),
                    "unrealized_pnl": self.paper_account.unrealized_pnl,
                    "realized_pnl": self.paper_account.realized_pnl,
                    "total_pnl": self.paper_account.realized_pnl + self.paper_account.unrealized_pnl,
                    "total_value": account_summary.get("total_value", 0),
                    "return_percent": account_summary.get("return_percent", 0),
                    "active_positions": len(self.paper_account.positions),
                    "today_trades": account_summary.get("today_trades", 0),
                    "trades_remaining_today": account_summary.get("trades_remaining_today", 0),
                    "position_details": [
                        {
                            "symbol": pos.symbol,
                            "quantity": pos.quantity,
                            "avg_price": pos.average_price,
                            "current_price": pos.current_price,
                            "unrealized_pnl": pos.unrealized_pnl
                        }
                        for pos in self.paper_account.positions.values()
                    ]
                }
            
            # Add live trading specific stats
            elif self.mode == TradingMode.LIVE:
                report["live_trading"] = {
                    "real_money_used": True,
                    "broker": "Angel One SmartAPI",
                    "warning": "REAL MONEY TRADING - ACTUAL PROFITS/LOSSES INCURRED"
                }

            return report

        except Exception as e:
            logger.error(f"[ERROR] Error generating session report: {e}")
            return {"error": str(e)}

    async def cleanup(self):
        """Cleanup resources and save final data"""
        try:
            print("\n[CLEANUP] Cleaning up resources...")

            # Close WebSocket connection first
            if hasattr(self, 'websocket_client') and self.websocket_client:
                try:
                    self.websocket_client.close()
                    print("   [SUCCESS] WebSocket connection closed")
                except Exception as e:
                    logger.error(f"[ERROR] Error closing WebSocket: {e}")

            # Stop all agents
            if self.signal_agent and hasattr(self.signal_agent, 'cleanup'):
                await self.signal_agent.cleanup()
            elif self.signal_agent and hasattr(self.signal_agent, 'stop'):
                await self.signal_agent.stop()

            if self.risk_agent and hasattr(self.risk_agent, 'cleanup'):
                await self.risk_agent.cleanup()
            elif self.risk_agent and hasattr(self.risk_agent, 'shutdown'):
                await self.risk_agent.shutdown()

            if self.execution_agent and hasattr(self.execution_agent, 'cleanup'):
                await self.execution_agent.cleanup()

            if self.market_agent and hasattr(self.market_agent, 'cleanup'):
                await self.market_agent.cleanup()
            elif self.market_agent and hasattr(self.market_agent, 'stop'):
                await self.market_agent.stop()

            # Save paper trading final state
            if self.mode == TradingMode.PAPER and self.paper_account:
                try:
                    paper_state_dir = Path("reports/paper_trading")
                    paper_state_dir.mkdir(parents=True, exist_ok=True)
                    
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    paper_state_file = paper_state_dir / f"paper_account_state_{timestamp}.json"
                    
                    paper_state = {
                        "session_id": self.trading_session_id,
                        "final_balance": self.paper_account.current_balance,
                        "total_pnl": self.paper_account.realized_pnl + self.paper_account.unrealized_pnl,
                        "total_trades": len(self.paper_account.trades),
                        "active_positions": len(self.paper_account.positions),
                        "realized_pnl": self.paper_account.realized_pnl,
                        "unrealized_pnl": self.paper_account.unrealized_pnl,
                        "trade_history": [asdict(trade) for trade in self.paper_account.trades],
                        "positions": [asdict(pos) for pos in self.paper_account.positions.values()]
                    }
                    
                    with open(paper_state_file, 'w') as f:
                        json.dump(paper_state, f, indent=2, default=str)
                    
                    print(f"   [SAVE] Paper trading state saved to: {paper_state_file}")
                except Exception as e:
                    logger.error(f"[ERROR] Error saving paper trading state: {e}")

            # Generate final report
            final_report = await self.generate_session_report()

            # Save report
            reports_dir = Path("reports/continuous_trading")
            reports_dir.mkdir(parents=True, exist_ok=True)

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            report_file = reports_dir / f"continuous_trading_{self.mode.value}_{timestamp}.json"

            with open(report_file, 'w') as f:
                json.dump(final_report, f, indent=2, default=str)

            print(f"[SAVE] Final report saved to: {report_file}")
            
            # Display final summary
            if self.mode == TradingMode.PAPER and self.paper_account:
                account_summary = self.paper_account.get_account_summary()
                print("\n" + "="*60)
                print("[PAPER] PAPER TRADING SESSION SUMMARY")
                print("="*60)
                print(f"[BALANCE] Final Balance: ₹{self.paper_account.current_balance:,.2f}")
                print(f"[PNL] Total P&L: ₹{(self.paper_account.realized_pnl + self.paper_account.unrealized_pnl):,.2f}")
                print(f"[RETURN] Return %: {account_summary.get('return_percent', 0):.2f}%")
                print(f"[TRADES] Total Trades: {len(self.paper_account.trades)}")
                print(f"[POSITIONS] Active Positions: {len(self.paper_account.positions)}")
                print("="*60)
            
            print("[SUCCESS] Cleanup completed")

        except Exception as e:
            logger.error(f"[ERROR] Error during cleanup: {e}")

    def setup_signal_handlers(self):
        """Setup signal handlers for graceful shutdown"""
        def signal_handler(signum, frame):
            print(f"\n[STOP] Received signal {signum}. Initiating graceful shutdown...")
            self.shutdown_event.set()
            self.running = False

        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)

    async def run_continuous_trading(self) -> Dict[str, Any]:
        """Main method to run continuous trading"""
        try:
            print("\n" + "="*80)
            print("[SYSTEM] CONTINUOUS LIVE TRADING SYSTEM")
            print("="*80)
            print(f"[INFO] Mode: {self.mode.value.upper()}")
            print(f"[TARGET] Max daily trades: {self.max_daily_trades}")
            print(f"[SESSION] Session: {self.trading_session_id}")
            
            # Display mode-specific information
            if self.mode == TradingMode.LIVE:
                print("[ALERT] ⚠️  LIVE TRADING MODE - REAL MONEY WILL BE USED!")
            elif self.mode == TradingMode.PAPER:
                print(f"[PAPER] 📊 Paper Trading Mode - Virtual Balance: ₹{self.paper_account.current_balance:,.2f}")
            else:
                print("[DEMO] 🎮 Demo Mode - Simulation Only")
            
            print("="*80)

            # Setup signal handlers
            self.setup_signal_handlers()

            # Step 1: Initialize all agents
            print("\n[AI] Step 1: Initializing Enhanced AI Trading Agents...")
            if not await self.initialize_agents():
                return {"error": "Failed to initialize agents"}

            # Step 2: Select trading universe with data preparation
            print("\n[TARGET] Step 2: Selecting Trading Universe & Preparing Data...")
            if not await self.select_trading_universe():
                return {"error": "Failed to select trading universe"}

            # Step 3: Wait for market to open (if needed)
            if not self.is_market_hours():
                print("\n[TIME] Step 3: Waiting for Market to Open...")
                await self.wait_for_market_open()

            if self.shutdown_event.is_set():
                print("[STOP] Shutdown requested before trading started")
                return {"status": "SHUTDOWN_BEFORE_START"}

            # Step 4: Start continuous trading
            print("\n[LOOP] Step 4: Starting Enhanced Continuous Trading...")

            # Display trading mode warnings
            if self.mode == TradingMode.LIVE:
                print("[ALERT] 🚨 LIVE TRADING MODE - REAL MONEY WILL BE USED!")
                print("[ALERT] 🚨 REAL ORDERS WILL BE PLACED ON NSE!")
            elif self.mode == TradingMode.PAPER:
                print("[PAPER] 📊 Paper Trading Mode - Real data, Virtual money")
                print(f"[BALANCE] 💰 Starting balance: ₹{self.paper_account.current_balance:,.2f}")
            else:
                print("[DEMO] 🎮 Demo Mode - Simulation with mock data")

            print(f"[TIME] ⏰ Current time: {datetime.now().strftime('%H:%M:%S')}")
            print(f"[STOCKS] 📈 Monitoring {len(self.selected_stocks)} stocks")
            print(f"[TRADES] 🎯 Ready to execute up to {self.max_daily_trades} trades")
            print(f"[DATA] 📊 Historical data points: {self.session_stats.get('historical_data_points', 0):,}")
            print(f"[WEBSOCKET] 🔄 Real-time messages: {self.session_stats.get('websocket_messages', 0):,}")

            # Run the enhanced continuous trading loop
            await self.continuous_trading_loop()

            # Step 5: Generate final report
            print("\n[INFO] Step 5: Generating Final Report...")
            final_report = await self.generate_session_report()

            print("\n" + "="*80)
            print("[INFO] TRADING SESSION SUMMARY")
            print("="*80)
            print(f"[DURATION] Duration: {final_report.get('duration_minutes', 0):.1f} minutes")
            print(f"[STOCKS] Signals Generated: {final_report['trading_stats']['signals_generated']}")
            print(f"[SUCCESS] Trades Executed: {final_report['trading_stats']['trades_executed']}")
            print(f"[ERROR] Trades Rejected: {final_report['trading_stats']['trades_rejected']}")
            print(f"[INFO] Success Rate: {final_report['trading_stats']['success_rate']:.1f}%")
            print(f"[TARGET] Trades Remaining: {final_report['trading_stats']['trades_remaining']}")
            print("="*80)

            return {
                "status": "SUCCESS",
                "session_report": final_report,
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"[ERROR] Continuous trading failed: {e}")
            return {"error": str(e)}
        finally:
            await self.cleanup()


async def main():
    """Main execution function"""
    parser = argparse.ArgumentParser(description="Continuous Live Trading System")
    parser.add_argument("--mode", choices=["live", "paper", "demo"],
                       default="paper", help="Trading mode")
    parser.add_argument("--max-trades", "--max-trade", type=int, default=5,
                       help="Maximum trades per day (default: 5)")
    parser.add_argument("--stocks", type=int, default=20,
                       help="Number of stocks to monitor (default: 20)")

    args = parser.parse_args()

    # Create logs directory
    Path("logs").mkdir(exist_ok=True)

    try:
        print("[SYSTEM] Starting Continuous Live Trading System...")

        # Safety confirmation for live trading
        if args.mode == "live":
            print("\n" + "="*60)
            print("[ALERT] CRITICAL WARNING: LIVE TRADING MODE")
            print("="*60)
            print("[WARNING]  REAL MONEY WILL BE USED!")
            print("[WARNING]  REAL ORDERS WILL BE PLACED ON NSE!")
            print("[WARNING]  YOU CAN LOSE MONEY!")
            print("[WARNING]  This system will run continuously during market hours!")
            print("[WARNING]  Maximum trades per day:", args.max_trades)
            print("="*60)

            print("\nThis system will:")
            print("[CHECK] Monitor market continuously during trading hours")
            print("[CHECK] Use AI agents for all trading decisions")
            print("[CHECK] Place real BUY/SELL orders on NSE")
            print("[CHECK] Apply stop-loss and take-profit automatically")
            print("[CHECK] Auto square-off positions before market close")
            print(f"[CHECK] Limit to maximum {args.max_trades} trades per day")

            # Multiple confirmations
            confirm1 = input("\nType 'I UNDERSTAND THE RISKS' to confirm: ")
            if confirm1 != "I UNDERSTAND THE RISKS":
                print("[ERROR] Confirmation failed. Exiting...")
                return

            confirm2 = input("Type 'START LIVE TRADING' to begin: ")
            if confirm2 != "START LIVE TRADING":
                print("[ERROR] Confirmation failed. Exiting...")
                return

            print("\n[ALERT] LIVE TRADING CONFIRMED - Starting continuous trading...")
            print("[MONEY] Real money will be used for trading!")

        elif args.mode == "paper":
            print("[PAPER] Paper trading mode - No real money will be used")
        else:
            print("[DEMO] Demo mode - Simulation only")

        # Initialize and run enhanced system
        system = EnhancedContinuousLiveTradingSystem(
            mode=args.mode,
            max_daily_trades=args.max_trades,
            initial_balance=100000  # ₹1 Lac for paper trading
        )

        results = await system.run_continuous_trading()

        if results.get("status") == "SUCCESS":
            print("\n[COMPLETE] Continuous trading session completed successfully!")
        else:
            print(f"\n[ERROR] Trading session ended with error: {results.get('error', 'Unknown error')}")

        return results

    except KeyboardInterrupt:
        print("\n[STOP] Trading interrupted by user")
    except Exception as e:
        logger.error(f"[ERROR] Critical error in main: {e}")
        print(f"[ERROR] Critical error: {e}")


if __name__ == "__main__":
    # Ensure proper event loop handling
    try:
        results = asyncio.run(main())
    except KeyboardInterrupt:
        print("\n[STOP] System shutdown by user")
    except Exception as e:
        print(f"[ERROR] System error: {e}")
        logger.error(f"System error: {e}")
