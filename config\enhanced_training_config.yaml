# Enhanced Model Training Configuration
# Optimized for backtesting results from enhanced_backtesting_improved.py

# Data Configuration
data_config:
  data_dir: "data/backtest_improved"
  input_file: "enhanced_strategy_results.parquet"
  models_dir: "data/models/enhanced"
  registry_dir: "data/models/registry/enhanced"

# Prediction Tasks Configuration
prediction_tasks:
  strategy_performance:
    type: "regression"
    target_columns: 
      - "avg_sharpe_ratio"
      - "avg_roi" 
      - "avg_profit_factor"
    description: "Predict strategy performance metrics"
    weight: 0.25
    min_samples: 100
    
  risk_metrics:
    type: "regression"
    target_columns:
      - "avg_max_drawdown"
      - "avg_var_95"
      - "avg_cvar_95"
    description: "Predict risk-adjusted metrics"
    weight: 0.20
    min_samples: 100
    
  strategy_consistency:
    type: "regression"
    target_columns:
      - "consistency_score"
      - "avg_accuracy"
    description: "Predict strategy consistency over time"
    weight: 0.15
    min_samples: 50
    
  profitability_classification:
    type: "classification"
    target_columns:
      - "passes_risk_criteria"
    description: "Binary classification of profitable strategies"
    weight: 0.20
    min_samples: 50
    classes: [true, false]
    
  market_regime_suitability:
    type: "classification"
    target_columns:
      - "best_regime"
    description: "Predict optimal market regime for strategy"
    weight: 0.20
    min_samples: 30
    classes: ["trending_up", "trending_down", "ranging", "high_volatility", "low_volatility"]

# Feature Engineering Configuration
feature_engineering:
  enabled: true
  create_interaction_features: true
  create_polynomial_features: false  # Disabled for financial data
  polynomial_degree: 2
  create_lag_features: true
  lag_periods: [1, 2, 3, 5, 10]
  
  # Feature selection
  feature_selection_enabled: true
  feature_importance_threshold: 0.001
  max_features: 100
  
  # Outlier handling
  remove_outliers: true
  outlier_method: "iqr"  # "iqr", "zscore", "isolation_forest"
  outlier_threshold: 1.5

# Model Configuration
models:
  enabled_models: ["lightgbm", "xgboost", "catboost", "tabnet"]
  ensemble_method: "weighted_average"  # "voting", "stacking", "weighted_average"
  
  # LightGBM Configuration
  lightgbm:
    default_params:
      objective: "regression"
      metric: "rmse"
      boosting_type: "gbdt"
      num_leaves: 31
      learning_rate: 0.05
      feature_fraction: 0.9
      bagging_fraction: 0.8
      bagging_freq: 5
      verbose: -1
      n_estimators: 1000
      early_stopping_rounds: 100
    
    optuna_search_space:
      n_estimators: [100, 2000]
      max_depth: [3, 12]
      learning_rate: [0.01, 0.3]
      num_leaves: [10, 200]
      feature_fraction: [0.5, 1.0]
      bagging_fraction: [0.5, 1.0]
      min_child_samples: [5, 100]
      reg_alpha: [0, 10]
      reg_lambda: [0, 10]
  
  # XGBoost Configuration
  xgboost:
    default_params:
      objective: "reg:squarederror"
      eval_metric: "rmse"
      max_depth: 6
      learning_rate: 0.05
      n_estimators: 1000
      subsample: 0.8
      colsample_bytree: 0.9
      tree_method: "hist"
      early_stopping_rounds: 100
    
    optuna_search_space:
      n_estimators: [100, 2000]
      max_depth: [3, 12]
      learning_rate: [0.01, 0.3]
      subsample: [0.5, 1.0]
      colsample_bytree: [0.5, 1.0]
      min_child_weight: [1, 10]
      reg_alpha: [0, 10]
      reg_lambda: [0, 10]
  
  # CatBoost Configuration
  catboost:
    default_params:
      iterations: 1000
      learning_rate: 0.05
      depth: 6
      l2_leaf_reg: 3
      verbose: false
      early_stopping_rounds: 100
    
    optuna_search_space:
      iterations: [100, 2000]
      depth: [3, 10]
      learning_rate: [0.01, 0.3]
      l2_leaf_reg: [1, 10]
      border_count: [32, 255]
  
  # TabNet Configuration
  tabnet:
    default_params:
      n_d: 32
      n_a: 32
      n_steps: 3
      gamma: 1.3
      lambda_sparse: 1e-3
      mask_type: "sparsemax"
      verbose: 0
      max_epochs: 200
      patience: 20
      batch_size: 1024
    
    optuna_search_space:
      n_d: [8, 64]
      n_a: [8, 64]
      n_steps: [3, 10]
      gamma: [1.0, 2.0]
      lambda_sparse: [1e-6, 1e-2]

# Training Configuration
training:
  test_size: 0.2
  validation_size: 0.2
  random_state: 42
  
  # Cross-validation
  cv_folds: 5
  cv_strategy: "time_series"  # "kfold", "time_series", "stratified"
  
  # Hyperparameter Optimization
  optuna_enabled: true
  optuna_trials: 100
  optuna_timeout: 1800  # 30 minutes
  optuna_pruning: true
  
  # Early stopping
  early_stopping_enabled: true
  early_stopping_rounds: 100
  
  # Model selection criteria
  model_selection_metric: "test_score"
  model_selection_mode: "max"  # "max" or "min"

# Performance Thresholds
performance_thresholds:
  regression:
    min_r2_score: 0.1
    max_rmse: 1.0
    max_mae: 0.5
  
  classification:
    min_accuracy: 0.55
    min_auc_score: 0.6
    min_f1_score: 0.5

# Model Explainability
explainability:
  shap_enabled: true
  generate_feature_importance: true
  generate_partial_dependence: false
  generate_model_summary: true
  
  # SHAP configuration
  shap_sample_size: 1000
  shap_max_display: 20

# Model Registry and Versioning
model_registry:
  enabled: true
  auto_save_models: true
  max_model_versions: 5
  model_metadata_tracking: true
  
  # Model validation before saving
  validate_before_save: true
  validation_metrics: ["test_score", "cv_score"]

# Hardware Configuration
hardware:
  n_jobs: -1
  use_gpu: false  # Will be auto-detected
  memory_limit_gb: 8
  
  # Parallel processing
  parallel_training: true
  max_parallel_jobs: 4

# Logging Configuration
logging:
  level: "INFO"
  log_to_file: true
  log_file: "logs/enhanced_training.log"
  log_format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  
  # Progress tracking
  progress_bar: true
  detailed_metrics: true

# Data Validation
data_validation:
  enabled: true
  min_samples_per_task: 50
  max_missing_percentage: 0.1
  check_target_distribution: true
  
  # Data quality checks
  check_duplicates: true
  check_outliers: true
  check_feature_correlation: true
  max_correlation_threshold: 0.95

# Advanced Features
advanced:
  # Incremental learning
  incremental_learning: false
  incremental_batch_size: 1000
  
  # Model interpretation
  generate_model_reports: true
  create_prediction_intervals: false
  
  # Experiment tracking
  experiment_tracking: false
  mlflow_enabled: false
  
  # Model serving
  create_prediction_api: false
  api_port: 8000