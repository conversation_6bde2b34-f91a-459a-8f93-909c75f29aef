import asyncio
import logging
from pathlib import Path
from typing import Dict, Any, List, Optional
import polars as pl
import yaml
from datetime import datetime

# Assuming these agents are already implemented and available
from agents.live_data_management_agent import LiveDataManagementAgent
from agents.live_feature_engineering_agent import LiveFeatureEngineeringAgent
from agents.live_ml_prediction_agent import LiveMLPredictionAgent
from agents.live_stock_selection_agent import LiveStockSelectionAgent
from agents.live_strategy_assignment_agent import LiveStrategyAssignmentAgent

logger = logging.getLogger(__name__)

class WorkflowResult:
    """Data class to hold the results of the workflow execution."""
    def __init__(self):
        self.selected_stocks: List[str] = []
        self.strategy_assignments: Dict[str, Any] = {}
        self.reports: Dict[str, Any] = {}
        self.warnings: List[str] = []
        self.recommendations: List[str] = []
        self.execution_summary: Dict[str, Any] = {}

class LiveStockSelectionWorkflow:
    """
    Orchestrates the entire stock selection and strategy assignment workflow
    for live trading applications.
    """
    def __init__(self, event_bus: Any, session_id: str, config_path: Optional[Path] = None, skip_download: bool = False):
        self.config_path = config_path if config_path else Path(__file__).parent.parent / "config" / "live_stock_selection_config.yaml"
        self.config: Dict[str, Any] = {}
        self.agents: Dict[str, Any] = {}
        self.workflow_result = WorkflowResult()
        self.event_bus = event_bus
        self.session_id = session_id
        self.start_time: Optional[datetime] = None
        self.skip_download = skip_download

    async def initialize(self) -> bool:
        """
        Initializes the workflow by loading configuration and setting up agents.
        """
        logger.info("[WORKFLOW] Initializing LiveStockSelectionWorkflow...")
        try:
            self._load_config()
            self._setup_agents()
            
            # Initialize all agents
            await self._initialize_agents()
            
            logger.info("[WORKFLOW] LiveStockSelectionWorkflow initialized successfully.")
            return True
        except Exception as e:
            logger.error(f"[WORKFLOW] Initialization failed: {e}", exc_info=True)
            return False

    def _load_config(self):
        """Loads configuration from the YAML file."""
        if not self.config_path.exists():
            raise FileNotFoundError(f"Configuration file not found: {self.config_path}")
        with open(self.config_path, 'r', encoding='utf-8') as f:
            self.config = yaml.safe_load(f)
        logger.info(f"[WORKFLOW] Configuration loaded from {self.config_path}")

    def _setup_agents(self):
        """Sets up instances of all required agents."""
        base_data_path = Path(__file__).parent.parent / "data"

        self.agents['data_management'] = LiveDataManagementAgent(
            event_bus=self.event_bus,
            session_id=self.session_id
        )
        self.agents['feature_engineering'] = LiveFeatureEngineeringAgent(
            event_bus=self.event_bus,
            config=self.config,
            session_id=self.session_id
        )
        self.agents['ml_prediction'] = LiveMLPredictionAgent(
            event_bus=self.event_bus,
            config=self.config,
            session_id=self.session_id
        )
        self.agents['stock_selection'] = LiveStockSelectionAgent(
            event_bus=self.event_bus,
            config=self.config,
            session_id=self.session_id
        )
        self.agents['strategy_assignment'] = LiveStrategyAssignmentAgent(
            event_bus=self.event_bus,
            config=self.config,
            session_id=self.session_id
        )
        logger.info("[WORKFLOW] All agents instantiated.")
    
    async def _initialize_agents(self):
        """Initialize all agents that require async initialization"""
        logger.info("[WORKFLOW] Initializing agents...")
        
        # Initialize agents that have async initialize methods
        for agent_name, agent in self.agents.items():
            if hasattr(agent, 'initialize'):
                try:
                    logger.info(f"[WORKFLOW] Initializing {agent_name} agent...")
                    success = await agent.initialize()
                    if success:
                        logger.info(f"[WORKFLOW] ✓ {agent_name} agent initialized successfully")
                    else:
                        logger.warning(f"[WORKFLOW] ⚠️ {agent_name} agent initialization returned False")
                except Exception as e:
                    logger.error(f"[WORKFLOW] ❌ Failed to initialize {agent_name} agent: {e}")
                    raise
        
        logger.info("[WORKFLOW] All agents initialized.")

    async def execute_workflow(self, stocks_to_process: List[str]) -> WorkflowResult:
        """
        Executes the full stock selection and strategy assignment workflow.
        """
        self.start_time = datetime.now()
        logger.info(f"[WORKFLOW] Starting workflow execution for {len(stocks_to_process)} stocks...")
        self.workflow_result = WorkflowResult() # Reset results for new run

        try:
            # 1. Data Management Agent: Download historical data (if not skipped)
            if not self.skip_download:
                logger.info("[WORKFLOW] Step 1: Data Management - Downloading historical data...")
                downloaded_data_paths = await self.agents['data_management'].download_historical_data(stocks_to_process)
                if not downloaded_data_paths:
                    self.workflow_result.warnings.append("No data downloaded by Data Management Agent.")
                    logger.warning("[WORKFLOW] No data downloaded. Aborting workflow.")
                    return self.workflow_result
                logger.info(f"[WORKFLOW] Downloaded data for {len(downloaded_data_paths)} stocks.")
                
                # Retrieve data quality metrics from Data Management Agent
                data_quality_metrics = self.agents['data_management'].get_quality_metrics()
                if not data_quality_metrics:
                    self.workflow_result.warnings.append("No data quality metrics retrieved from Data Management Agent.")
                    logger.warning("[WORKFLOW] No data quality metrics. Aborting workflow.")
                    return self.workflow_result
                logger.info(f"[WORKFLOW] Retrieved data quality metrics for {len(data_quality_metrics)} stocks.")
            else:
                logger.info("[WORKFLOW] Step 1: Data Management - Skipping historical data download as requested")
                # Load existing data from data/live folder
                downloaded_data_paths = {}
                data_quality_metrics = {}
                
                data_dir = Path(__file__).parent.parent / "data" / "live"
                for symbol in stocks_to_process:
                    data_file = data_dir / f"{symbol}_live_data.parquet"
                    if data_file.exists():
                        try:
                            # Load the actual data
                            data = pl.read_parquet(data_file)
                            downloaded_data_paths[symbol] = data
                            # Create dummy quality metrics for existing data
                            data_quality_metrics[symbol] = type('QualityMetrics', (), {'is_valid': True, 'quality_score': 0.8})()
                        except Exception as e:
                            logger.warning(f"[WORKFLOW] Failed to load data for {symbol}: {e}")
                    else:
                        logger.warning(f"[WORKFLOW] Data file not found for {symbol}: {data_file}")
                
                logger.info(f"[WORKFLOW] Loaded existing data for {len(downloaded_data_paths)} stocks")

            # 2. Feature Engineering Agent: Calculate technical indicators and market features
            logger.info("[WORKFLOW] Step 2: Feature Engineering - Calculating features...")
            features_dict = await self.agents['feature_engineering'].calculate_features_for_stocks(downloaded_data_paths)
            if not features_dict:
                self.workflow_result.warnings.append("No features engineered by Feature Engineering Agent.")
                logger.warning("[WORKFLOW] No features engineered. Aborting workflow.")
                return self.workflow_result
            logger.info(f"[WORKFLOW] Engineered features for {len(features_dict)} stocks.")
            
            # Convert features dict to DataFrame for ML prediction
            features_df = self._convert_features_to_dataframe(features_dict)

            # 3. ML Prediction Agent: Apply models to predict returns, risk, strategy suitability
            logger.info("[WORKFLOW] Step 3: ML Prediction - Generating predictions...")
            predictions_dict = await self.agents['ml_prediction'].generate_predictions(features_df)
            if not predictions_dict:
                self.workflow_result.warnings.append("No predictions generated by ML Prediction Agent.")
                logger.warning("[WORKFLOW] No predictions generated. Aborting workflow.")
                return self.workflow_result
            logger.info(f"[WORKFLOW] Generated predictions for {len(predictions_dict)} stocks.")

            # 4. Stock Selection Agent: Score and rank stocks
            logger.info("[WORKFLOW] Step 4: Stock Selection - Scoring and ranking stocks...")
            selection_result = await self.agents['stock_selection'].select_stocks(predictions_dict, data_quality_metrics)
            
            # Extract selected stocks from SelectionResult object
            if hasattr(selection_result, 'selected_stocks') and selection_result.selected_stocks:
                processed_stocks = []
                for item in selection_result.selected_stocks:
                    if isinstance(item, dict) and 'symbol' in item:
                        processed_stocks.append(item['symbol'])
                    elif isinstance(item, str):
                        processed_stocks.append(item)
                self.workflow_result.selected_stocks = processed_stocks
            else:
                self.workflow_result.selected_stocks = []
            if not self.workflow_result.selected_stocks:
                self.workflow_result.warnings.append("No stocks selected by Stock Selection Agent.")
                logger.warning("[WORKFLOW] No stocks selected. Aborting workflow.")
                return self.workflow_result
            logger.info(f"[WORKFLOW] Selected {len(self.workflow_result.selected_stocks)} stocks.")

            # 5. Strategy Assignment Agent: Determine optimal strategy for each selected stock
            logger.info("[WORKFLOW] Step 5: Strategy Assignment - Assigning strategies...")
            if hasattr(selection_result, 'selected_stocks') and selection_result.selected_stocks:
                self.workflow_result.strategy_assignments = await self.agents['strategy_assignment'].assign_strategies(
                    selection_result.selected_stocks,
                    predictions_dict,
                    features_dict
                )
            else:
                self.workflow_result.strategy_assignments = {}
            # Check if strategy_assignments is a StrategyAssignmentResult object or dict
            if hasattr(self.workflow_result.strategy_assignments, 'assignments'):
                assignments_count = len(self.workflow_result.strategy_assignments.assignments)
            else:
                assignments_count = len(self.workflow_result.strategy_assignments) if self.workflow_result.strategy_assignments else 0
            logger.info(f"[WORKFLOW] Assigned strategies for {assignments_count} stocks.")

            # Generate comprehensive reports
            self._generate_reports()

        except Exception as e:
            logger.error(f"[WORKFLOW] Workflow execution failed: {e}", exc_info=True)
            self.workflow_result.warnings.append(f"Workflow execution failed: {e}")
        finally:
            self._finalize_execution_summary()
            logger.info("[WORKFLOW] Workflow execution completed.")
            return self.workflow_result

    def _generate_reports(self):
        """Generates comprehensive reports based on workflow results."""
        logger.info("[WORKFLOW] Generating comprehensive reports...")
        
        # Report 1: Selected Stocks with Rationale
        selected_stocks_report = []
        for symbol in self.workflow_result.selected_stocks:
            # Find the full info for the selected stock from the stock_selection agent's output
            # This would ideally be passed through the workflow_result or retrieved from a central store
            # For now, we'll just use the symbol and strategy assignment
            
            # Handle both StrategyAssignmentResult object and dict cases
            assignment = {}
            if hasattr(self.workflow_result.strategy_assignments, 'assignments'):
                # It's a StrategyAssignmentResult object
                assignment = self.workflow_result.strategy_assignments.assignments.get(symbol, {})
                if hasattr(assignment, '__dict__'):
                    # Convert StrategyAssignment object to dict for easier access
                    assignment_dict = {
                        'primary_strategy': getattr(assignment, 'primary_strategy', 'N/A'),
                        'expected_performance': getattr(assignment, 'expected_performance', {}),
                        'assignment_rationale': getattr(assignment, 'assignment_rationale', 'N/A')
                    }
                    assignment = assignment_dict
            elif isinstance(self.workflow_result.strategy_assignments, dict):
                # It's already a dict
                assignment = self.workflow_result.strategy_assignments.get(symbol, {})
            
            selected_stocks_report.append({
                "symbol": symbol,
                "primary_strategy": assignment.get('primary_strategy', 'N/A'),
                "expected_performance": assignment.get('expected_performance', 'N/A'),
                "rationale": assignment.get('assignment_rationale', "Based on composite score from ML predictions and risk metrics.")
            })
        self.workflow_result.reports['selected_stocks_report'] = selected_stocks_report
        
        # Report 2: Strategy Assignments with Expected Performance
        # Convert StrategyAssignmentResult to dict format for reporting
        if hasattr(self.workflow_result.strategy_assignments, 'assignments'):
            # Convert StrategyAssignmentResult to dict format
            assignments_dict = {}
            for symbol, assignment in self.workflow_result.strategy_assignments.assignments.items():
                if hasattr(assignment, '__dict__'):
                    assignments_dict[symbol] = {
                        'primary_strategy': getattr(assignment, 'primary_strategy', 'N/A'),
                        'backup_strategy': getattr(assignment, 'backup_strategy', 'N/A'),
                        'confidence': getattr(assignment, 'confidence', 0.0),
                        'expected_performance': getattr(assignment, 'expected_performance', {}),
                        'assignment_rationale': getattr(assignment, 'assignment_rationale', 'N/A')
                    }
                else:
                    assignments_dict[symbol] = assignment
            self.workflow_result.reports['strategy_assignments_report'] = assignments_dict
        else:
            self.workflow_result.reports['strategy_assignments_report'] = self.workflow_result.strategy_assignments

        # Report 3: Risk Warnings and Recommendations (from agents or derived)
        # For now, these are populated directly from workflow_result.warnings/recommendations
        self.workflow_result.reports['risk_warnings'] = self.workflow_result.warnings
        self.workflow_result.reports['recommendations'] = self.workflow_result.recommendations

        # Report 4: Data Quality Assessment (from Data Management Agent)
        # This would ideally come from the data_management agent's output
        self.workflow_result.reports['data_quality_assessment'] = {
            "overall_quality": "Good", # Placeholder
            "details": "Data downloaded successfully for most stocks. Some gaps handled by FE agent." # Placeholder
        }
        logger.info("[WORKFLOW] Reports generated.")

    def _finalize_execution_summary(self):
        """Calculates and stores the execution summary."""
        if self.start_time:
            end_time = datetime.now()
            total_time = (end_time - self.start_time).total_seconds()
            self.workflow_result.execution_summary['total_execution_time'] = total_time
            self.workflow_result.execution_summary['end_time'] = end_time.isoformat()
        self.workflow_result.execution_summary['status'] = "Completed" if not self.workflow_result.warnings else "Completed with Warnings"
        logger.info(f"[WORKFLOW] Execution Summary: {self.workflow_result.execution_summary}")

    def _convert_features_to_dataframe(self, features_dict: Dict) -> pl.DataFrame:
        """Convert features dictionary to Polars DataFrame for ML prediction"""
        try:
            import polars as pl
            
            # Extract features from the dictionary
            rows = []
            for symbol, feature_eng in features_dict.items():
                if feature_eng.is_valid:
                    row = {'symbol': symbol}
                    row.update(feature_eng.features)
                    if feature_eng.target_return is not None:
                        row['target_return'] = feature_eng.target_return
                    rows.append(row)
            
            if not rows:
                logger.warning("[WORKFLOW] No valid features to convert to DataFrame")
                return pl.DataFrame()
            
            # Create DataFrame
            features_df = pl.DataFrame(rows)
            logger.info(f"[WORKFLOW] Converted {len(rows)} feature records to DataFrame with {len(features_df.columns)} columns")
            return features_df
            
        except Exception as e:
            logger.error(f"[WORKFLOW] Error converting features to DataFrame: {e}")
            return pl.DataFrame()

    async def cleanup(self):
        """Cleans up resources used by the workflow and its agents."""
        logger.info("[WORKFLOW] Cleaning up LiveStockSelectionWorkflow resources...")
        # Individual agents might have their own cleanup methods
        # For now, just log
        logger.info("[WORKFLOW] Cleanup complete.")
