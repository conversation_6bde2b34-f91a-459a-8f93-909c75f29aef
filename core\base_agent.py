#!/usr/bin/env python3
"""
BASE AGENT CLASS
Foundation for all trading system agents with modern async patterns

Based on best practices:
- Event-driven architecture
- Async/await patterns
- Proper lifecycle management
- Comprehensive logging
- Health monitoring
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, Any, Optional, Tuple
from abc import ABC, abstractmethod
import time

from .event_system import EventBus, EventTypes

logger = logging.getLogger(__name__)

class BaseAgent(ABC):
    """
    Base class for all trading system agents
    
    Features:
    - Async lifecycle management
    - Event-driven communication
    - Health monitoring
    - Performance tracking
    - Graceful error handling
    """
    
    def __init__(self, name: str, event_bus: EventBus, config: Any, session_id: str):
        self.name = name
        self.event_bus = event_bus
        self.config = config
        self.session_id = session_id
        
        # Agent state
        self.initialized = False
        self.running = False
        self.start_time = None
        self.stop_time = None
        
        # Performance tracking
        self.stats = {
            'messages_processed': 0,
            'errors_count': 0,
            'last_activity': None,
            'processing_times': [],
            'health_status': 'unknown'
        }
        
        # Logger for this agent
        self.logger = logging.getLogger(f"{__name__}.{name}")
        
        self.logger.info(f"[INIT] {name} agent created")
    
    @abstractmethod
    async def initialize(self) -> bool:
        """Initialize the agent - must be implemented by subclasses"""
        pass
    
    @abstractmethod
    async def start(self):
        """Start the agent - must be implemented by subclasses"""
        pass
    
    @abstractmethod
    async def stop(self):
        """Stop the agent - must be implemented by subclasses"""
        pass
    
    async def startup(self) -> bool:
        """Complete startup sequence"""
        try:
            self.logger.info(f"[STARTUP] Starting {self.name} agent...")
            
            # Initialize
            if not await self.initialize():
                self.logger.error(f"[ERROR] Failed to initialize {self.name}")
                return False
            
            # Publish startup event
            await self.event_bus.publish(
                EventTypes.AGENT_STARTED,
                {
                    'agent_name': self.name,
                    'session_id': self.session_id,
                    'timestamp': datetime.now().isoformat()
                },
                source=self.name
            )
            
            # Start the agent
            self.start_time = datetime.now()
            self.running = True
            self.stats['health_status'] = 'healthy'
            
            # Start in background
            asyncio.create_task(self._run_with_error_handling())
            
            self.logger.info(f"[SUCCESS] {self.name} agent started successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"[ERROR] Failed to start {self.name}: {e}")
            self.stats['health_status'] = 'failed'
            return False
    
    async def shutdown(self):
        """Complete shutdown sequence"""
        try:
            self.logger.info(f"[SHUTDOWN] Stopping {self.name} agent...")
            
            self.running = False
            self.stop_time = datetime.now()
            
            # Stop the agent
            await self.stop()
            
            # Publish shutdown event
            await self.event_bus.publish(
                EventTypes.AGENT_STOPPED,
                {
                    'agent_name': self.name,
                    'session_id': self.session_id,
                    'uptime_seconds': self.get_uptime_seconds(),
                    'timestamp': datetime.now().isoformat()
                },
                source=self.name
            )
            
            self.stats['health_status'] = 'stopped'
            self.logger.info(f"[SUCCESS] {self.name} agent stopped")
            
        except Exception as e:
            self.logger.error(f"[ERROR] Failed to stop {self.name}: {e}")
            self.stats['health_status'] = 'error'
    
    async def _run_with_error_handling(self):
        """Run the agent with comprehensive error handling"""
        try:
            await self.start()
        except Exception as e:
            self.logger.error(f"[ERROR] {self.name} agent crashed: {e}")
            self.stats['errors_count'] += 1
            self.stats['health_status'] = 'error'
            
            # Publish error event
            await self.event_bus.publish(
                EventTypes.AGENT_ERROR,
                {
                    'agent_name': self.name,
                    'error': str(e),
                    'timestamp': datetime.now().isoformat()
                },
                source=self.name
            )
    
    def log_info(self, message: str):
        """Log info message with agent context"""
        self.logger.info(f"[{self.name}] {message}")
        self.stats['last_activity'] = datetime.now()
    
    def log_warning(self, message: str):
        """Log warning message with agent context"""
        self.logger.warning(f"[{self.name}] {message}")
        self.stats['last_activity'] = datetime.now()
    
    def log_error(self, message: str):
        """Log error message with agent context"""
        self.logger.error(f"[{self.name}] {message}")
        self.stats['errors_count'] += 1
        self.stats['last_activity'] = datetime.now()
    
    def log_debug(self, message: str):
        """Log debug message with agent context"""
        self.logger.debug(f"[{self.name}] {message}")
        self.stats['last_activity'] = datetime.now()
    
    def increment_message_count(self):
        """Increment processed message count"""
        self.stats['messages_processed'] += 1
        self.stats['last_activity'] = datetime.now()
    
    def record_processing_time(self, processing_time: float):
        """Record processing time for performance monitoring"""
        self.stats['processing_times'].append(processing_time)
        
        # Keep only last 1000 processing times
        if len(self.stats['processing_times']) > 1000:
            self.stats['processing_times'] = self.stats['processing_times'][-1000:]
    
    def get_uptime_seconds(self) -> Optional[float]:
        """Get agent uptime in seconds"""
        if not self.start_time:
            return None
        
        end_time = self.stop_time or datetime.now()
        return (end_time - self.start_time).total_seconds()
    
    def get_average_processing_time(self) -> Optional[float]:
        """Get average processing time"""
        if not self.stats['processing_times']:
            return None
        
        return sum(self.stats['processing_times']) / len(self.stats['processing_times'])
    
    def get_health_status(self) -> Dict[str, Any]:
        """Get comprehensive health status"""
        return {
            'name': self.name,
            'initialized': self.initialized,
            'running': self.running,
            'health_status': self.stats['health_status'],
            'uptime_seconds': self.get_uptime_seconds(),
            'messages_processed': self.stats['messages_processed'],
            'errors_count': self.stats['errors_count'],
            'last_activity': self.stats['last_activity'].isoformat() if self.stats['last_activity'] else None,
            'average_processing_time': self.get_average_processing_time(),
            'session_id': self.session_id
        }
    
    def is_healthy(self) -> Tuple[bool, str]:
        """
        Check if agent is healthy.
        Returns a tuple of (bool, str) indicating health status and reason.
        """
        if not self.running:
            return False, "not_running"
        
        # Check if agent has been active recently (within last 10 minutes for selectors)
        timeout_seconds = 600 if 'Selector' in self.name else 300 # 10 mins for selectors, 5 for others
        if self.stats['last_activity']:
            time_since_activity = (datetime.now() - self.stats['last_activity']).total_seconds()
            if time_since_activity > timeout_seconds:
                return False, f"inactive_for_{int(time_since_activity)}s"
        elif (datetime.now() - self.start_time).total_seconds() > timeout_seconds:
             # If there's no activity at all after the timeout period
            return False, "no_activity_since_start"

        # Check error rate
        if self.stats['messages_processed'] > 10: # Only check after a few messages
            error_rate = self.stats['errors_count'] / self.stats['messages_processed']
            if error_rate > 0.1:  # More than 10% error rate
                return False, f"high_error_rate_{error_rate:.2f}"
        
        if self.stats['health_status'] != 'healthy':
             return False, self.stats['health_status']

        return True, 'healthy'

    async def health_check(self) -> Dict[str, Any]:
        """Perform health check and update status string."""
        try:
            start_time = time.time()
            
            # Get health status and reason
            is_healthy, health_reason = self.is_healthy()
            
            # Update the persistent health status string
            if not is_healthy:
                self.stats['health_status'] = health_reason
            else:
                # If it was unhealthy before and now it's not, mark it as healthy again
                if self.stats['health_status'] != 'healthy':
                    self.stats['health_status'] = 'healthy'

            # Additional checks can be implemented by subclasses
            additional_checks = await self._perform_additional_health_checks()
            
            processing_time = time.time() - start_time
            
            return {
                'agent_name': self.name,
                'healthy': is_healthy,
                'health_status': self.stats['health_status'], # This is now consistent
                'check_duration': processing_time,
                'additional_checks': additional_checks,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.log_error(f"Health check failed: {e}")
            return {
                'agent_name': self.name,
                'healthy': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    async def _perform_additional_health_checks(self) -> Dict[str, Any]:
        """Override this method for agent-specific health checks"""
        return {}
    
    def get_configuration(self) -> Dict[str, Any]:
        """Get agent configuration (safe for logging)"""
        # Return a safe version of config without sensitive data
        return {
            'agent_name': self.name,
            'session_id': self.session_id,
            'initialized': self.initialized,
            'running': self.running
        }