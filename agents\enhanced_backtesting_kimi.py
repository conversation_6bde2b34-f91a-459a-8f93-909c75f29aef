
        #!/usr/bin/env python3
"""
Enhanced Backtesting System with Polars and AsyncIO - PERFORMANCE OPTIMIZED
- Fixed major performance bottlenecks in signal processing
- Optimized memory usage and reduced excessive result generation
- Added proper batching and vectorized operations
- Improved trade simulation logic to reduce false signals
- FIXED: generate_strategy_signals function name error
- UPDATED: Removed MAX_TRADES_PER_STRATEGY to allow complete strategy analysis
- UPDATED: Removed stock_name and timeframe columns from output (present in filename)
- UPDATED: Create output files immediately after each feature file is processed
- OPTIMIZED: Enhanced immediate file writing with timing metrics and memory cleanup
- OPTIMIZED: More frequent garbage collection to handle immediate file writing efficiently
"""

import os
import re 
import logging
import yaml
import polars as pl
import asyncio
from pathlib import Path
import gc
import time
from typing import List, Dict, Any, Optional, Tuple, Union
import math
import random
import sys
import concurrent.futures # Added for ProcessPoolExecutor
import numpy # Added for vectorbt
import vectorbt as vbt # Added for GPU vectorization
import numexpr # Added for vectorbt performance
from numba import jit # Added for Numba optimization
os.environ["OMP_NUM_THREADS"] = "1"

# Path to the GPU optimization configuration file
GPU_OPTIMIZATION_CONFIG_FILE = Path(__file__).with_suffix('').parent.parent / "config" / "gpu_optimization_config.yaml"

if sys.platform == "win32":
    os.environ["PYTHONIOENCODING"] = "utf-8"

# Setup logging with INFO level for general runs, DEBUG can be enabled via run_enhanced_backtesting_kimi.py --log-level DEBUG
logging.basicConfig(level=logging.INFO, format='%(asctime)s %(levelname)s:%(message)s')
logger = logging.getLogger(__name__)

# GPU detection
try:
    import cupy as cp
    np = cp # Alias cupy to np if GPU is available
    GPU_AVAILABLE = True
    logger.info("CuPy (GPU) is available and will be used.")
except ImportError:
    np = numpy # Fallback to numpy if CuPy is not available
    GPU_AVAILABLE = False
    logger.info("CuPy (GPU) is NOT available. Falling back to NumPy (CPU).")

# Load GPU optimization configuration
GPU_CONFIG = {}
if GPU_OPTIMIZATION_CONFIG_FILE.exists():
    try:
        with open(GPU_OPTIMIZATION_CONFIG_FILE, 'r', encoding='utf-8') as f:
            GPU_CONFIG = yaml.safe_load(f)
        logger.info(f"Loaded GPU optimization config from {GPU_OPTIMIZATION_CONFIG_FILE}")
    except Exception as e:
        logger.error(f"Failed to load GPU optimization config: {e}")
else:
    logger.warning(f"GPU optimization config file not found at {GPU_OPTIMIZATION_CONFIG_FILE}")

# Apply Polars GPU settings if available and enabled in config
# Note: Direct `pl.Config.set_streaming_engine("cuda")` might not be available or
# the correct way to enable Polars GPU in all versions/builds.
# Polars GPU acceleration typically requires a specific `polars-gpu` installation
# or a Polars build compiled with CUDA support, and often leverages GPU implicitly
# when data is on the GPU (e.g., via CuPy arrays) or through specific functions.
# Removing this line to prevent errors if the method is not found.
# Apply Polars GPU settings if available and enabled in config
# Note: Direct `pl.Config.set_streaming_engine("cuda")` or `pl.Config.set_global_string_cache`
# might not be available or the correct way to enable Polars GPU in all versions/builds.
# Polars GPU acceleration typically requires a specific `polars-gpu` installation
# or a Polars build compiled with CUDA support, and often leverages GPU implicitly
# when data is on the GPU (e.g., via CuPy arrays) or through specific functions.
# Removed explicit Polars configuration lines to prevent errors.
if GPU_AVAILABLE and GPU_CONFIG.get('polars', {}).get('gpu_engine', False):
    logger.info("Polars GPU engine is enabled in config. Ensure you have `polars-gpu` installed.")
    logger.info("Polars GPU operations will be attempted implicitly where supported (e.g., with CuPy arrays).")
    logger.info("Explicit Polars GPU configuration methods (like set_streaming_engine or set_global_string_cache) were not found or caused errors in this Polars version.")

# Main function for direct execution
async def main():
    """Main function for direct execution"""
    await main_async()

def debug_quick_test():
    """Debug function to test with minimal data and simple strategies"""
    logger.info("=== QUICK DEBUG TEST ===")
    
    # Load strategies for debugging
    strategies = load_strategies()
    if not strategies:
        logger.error("No strategies loaded - check your strategies.yaml file")
        return
    
    logger.info(f"Loaded {len(strategies)} strategies:")
    for i, strategy in enumerate(strategies):
        logger.info(f"  {i+1}. {strategy.get('name', 'Unnamed')}")
        logger.info(f"     Long: {strategy.get('long', 'NOT_DEFINED')}")
        logger.info(f"     Short: {strategy.get('short', 'NOT_DEFINED')}")
    
    # Check for feature files
    feature_files = get_available_feature_files()
    if not feature_files:
        logger.error("No feature files found - check your data/features directory")
        return
    
    logger.info(f"Found {len(feature_files)} feature files:")
    for i, (path, symbol, timeframe) in enumerate(feature_files[:5]):  # Show first 5
        logger.info(f"  {i+1}. {symbol} ({timeframe})")
    
    # Test with first file
    if feature_files:
        test_file, test_symbol, test_timeframe = feature_files[0]
        logger.info(f"Testing with: {test_symbol} ({test_timeframe})")

# Configuration paths
STRATEGIES_FILE = Path(__file__).with_suffix('').parent.parent / "config" / "strategies.yaml"

# Configuration
DATA_DIR = "data/features"
OUTPUT_DIR = "data/backtest"
TEMP_DIR = "data/backtest/temp" # New temporary directory
OUTPUT_FORMAT = "parquet"
COMPRESSION = "brotli"
RISK_REWARD_RATIOS = [[1, 2], [1.5, 2]]
RISK_PER_TRADE_PCT = 1.0
INITIAL_CAPITAL = 100000
TRANSACTION_COST_PCT = 0.05
SLIPPAGE_PCT = 0.02
PROFIT_THRESHOLD = 1.0
INTRADAY_MARGIN_MULTIPLIER = 3.5

# Performance optimization settings
MIN_SIGNAL_DISTANCE = 3  # Minimum bars between signals (reduced for more signals)
MIN_VOLUME_THRESHOLD = 100  # Minimum volume for valid signals
MAX_HOLDING_PERIOD = 100  # Maximum bars to hold position
MIN_PRICE_MOVE = 0.0005  # Minimum price movement
# MAX_TRADES_PER_STRATEGY removed to allow backtesting on all signals for clearer strategy picture

# Execution-mode switches
USE_PROCESS_POOL_EXECUTOR = True
CONCURRENT_FILES = 5 # Reduced to optimize memory usage
TIMEFRAMES = ["1min", "3min", "5min", "15min"]

# Global variable for the process pool executor
process_executor: Optional[concurrent.futures.ProcessPoolExecutor] = None

def init_executors():
    global process_executor
    if USE_PROCESS_POOL_EXECUTOR:
        logger.info(f"Initializing ProcessPoolExecutor with {CONCURRENT_FILES} workers")
        process_executor = concurrent.futures.ProcessPoolExecutor(max_workers=CONCURRENT_FILES)

def cleanup_executors():
    global process_executor
    if process_executor:
        logger.info("Shutting down ProcessPoolExecutor")
        process_executor.shutdown(wait=True)
        process_executor = None

def generate_strategy_signals(df: pl.DataFrame, side: str, strategy: dict) -> pl.Series:
    """Generate strategy signals with enhanced debugging and validation"""
    expr_str = strategy.get(side, "").strip()
    strategy_name = strategy.get('name', 'Unknown')
    
    if not expr_str:
        logger.debug(f"No {side} expression for strategy {strategy_name}")
        return pl.Series("mask", [False] * df.height)

    logger.debug(f"Processing {side} signal for {strategy_name}: {expr_str}")
    
    # Check available columns in dataframe
    available_cols = set(df.columns)
    logger.debug(f"Available columns: {sorted(available_cols)}")

    # Debugging for Donchian_Break strategy
    if strategy_name == "Donchian_Break":
        logger.debug(f"Debugging Donchian_Break strategy. Checking relevant columns:")
        for col in ["donchian_high", "donchian_low", "volume", "close"]:
            if col in df.columns:
                logger.debug(f"  Column '{col}':")
                logger.debug(f"    Head: {df[col].head(5).to_list()}")
                logger.debug(f"    Null count: {df[col].is_null().sum()}")
                logger.debug(f"    Min: {df[col].min()}, Max: {df[col].max()}, Mean: {df[col].mean()}")
            else:
                logger.debug(f"  Column '{col}' is NOT present in DataFrame.")

    # Manual replacement approach - more reliable
    replacements = {
        'close': 'pl.col("close")',
        'open': 'pl.col("open")',
        'high': 'pl.col("high")',
        'low': 'pl.col("low")',
        'volume': 'pl.col("volume")',
        'rsi_14': 'pl.col("rsi_14")',
        'rsi_5': 'pl.col("rsi_5")',
        'ema_5': 'pl.col("ema_5")',
        'ema_10': 'pl.col("ema_10")',
        'ema_13': 'pl.col("ema_13")',
        'ema_20': 'pl.col("ema_20")',
        'ema_21': 'pl.col("ema_21")',
        'ema_50': 'pl.col("ema_50")',
        'vwap': 'pl.col("vwap")',
        'supertrend': 'pl.col("supertrend")',
        'cpr_top': 'pl.col("cpr_top")',
        'cpr_bottom': 'pl.col("cpr_bottom")',
        'macd': 'pl.col("macd")',
        'macd_signal': 'pl.col("macd_signal")',
        'bb_upper': 'pl.col("bb_upper")',
        'bb_lower': 'pl.col("bb_lower")',
        'bb_middle': 'pl.col("bb_middle")',
        'atr': 'pl.col("atr")',
        'adx': 'pl.col("adx")',
        'cci': 'pl.col("cci")',
        'mfi': 'pl.col("mfi")',
        'stoch_k': 'pl.col("stoch_k")',
        'stoch_d': 'pl.col("stoch_d")',
        'pivot': 'pl.col("pivot")',
        'resistance': 'pl.col("resistance")',
        'support': 'pl.col("support")',
        'donchian_high': 'pl.col("donchian_high")',
        'donchian_low': 'pl.col("donchian_low")',
        'daily_orb_high': 'pl.col("daily_orb_high")',
        'daily_orb_low': 'pl.col("daily_orb_low")',
        'vcp_pattern': 'pl.col("vcp_pattern")',
        'upward_candle': 'pl.col("upward_candle")',
        'downward_candle': 'pl.col("downward_candle")',
    }
    
    # Replace boolean operators first
    expr_str = (expr_str
                .replace(" and ", " & ")
                .replace(" or ",  " | ")
                .replace(" not ", " ~"))
    
    # Check if required columns exist before replacement
    missing_cols = []
    for col_name in replacements.keys():
        if col_name in expr_str and col_name not in available_cols:
            missing_cols.append(col_name)
    
    if missing_cols:
        logger.warning(f"Missing columns for {strategy_name} {side}: {missing_cols}")
        return pl.Series("mask", [False] * df.height)
    
    # Replace column names (only if they exist in the dataframe)
    for col_name, pl_expr in replacements.items():
        if col_name in available_cols:
            # Use word boundaries to avoid partial matches
            pattern = r'\b' + re.escape(col_name) + r'\b'
            expr_str = re.sub(pattern, pl_expr, expr_str)

    # Add parentheses around each logical block to ensure correct precedence
    # This is crucial for expressions like A > B & C > D where Python's & has higher precedence than >
    expr_str = "( " + expr_str.replace(" & ", " ) & ( ").replace(" | ", " ) | ( ") + " )"

    logger.debug(f"Converted expression: {expr_str}")
    logger.debug(f"Final expression for eval: {expr_str}") # Added for debugging

    # Check for all-null columns that are part of the expression
    cols_in_expr = re.findall(r'pl\.col\("([^"]+)"\)', expr_str)
    for col in set(cols_in_expr): # Use set to avoid redundant checks
        if col in df.columns and df[col].is_null().all():
            logger.warning(f"Critical column '{col}' for {strategy_name} {side} is entirely null after data cleaning. Returning no signals.")
            return pl.Series("mask", [False] * df.height)

    try:
        # Evaluate the expression
        mask = eval(expr_str, {"__builtins__": {}, "pl": pl})
        if isinstance(mask, pl.Expr):
            result = df.select(mask.alias("signal")).to_series()
            signal_count = result.sum()
            logger.debug(f"Generated {signal_count} {side} signals for {strategy_name}")
            return result
        else:
            logger.debug(f"Expression returned constant value: {mask}")
            return pl.Series("mask", [bool(mask)] * df.height)

    except Exception as e:
        logger.warning(f"Expression failed ({side}) for {strategy_name}: {expr_str} – {e}")
        return pl.Series("mask", [False] * df.height)

def filter_signals_by_distance(signals: pl.Series, min_distance: int = MIN_SIGNAL_DISTANCE) -> pl.Series:
    """Filter signals to maintain minimum distance between them - RELAXED"""
    if signals.sum() == 0:
        return signals
    
    signal_indices = signals.arg_true().to_list() # Get indices of true signals using Polars
    if len(signal_indices) <= 1:
        return signals
    
    # More relaxed filtering - allow closer signals if they're strong
    filtered_indices = [signal_indices[0]]  # Always keep first signal
    
    for idx in signal_indices[1:]:
        if idx - filtered_indices[-1] >= min_distance:
            filtered_indices.append(idx)
        elif len(filtered_indices) < 10:  # Allow some close signals if we don't have many
            filtered_indices.append(idx)
    
    # Create new signal series
    filtered_signals_array = [False] * len(signals)
    for idx in filtered_indices:
        filtered_signals_array[idx] = True
    
    return pl.Series(signals.name, filtered_signals_array)

@jit(nopython=True, cache=True)
def _filter_signals_numba(signals_array: numpy.ndarray, min_distance: int) -> numpy.ndarray:
    """Numba-optimized helper for filtering signals."""
    if signals_array.sum() == 0:
        return signals_array
    
    signal_indices = numpy.where(signals_array)[0]
    if len(signal_indices) <= 1:
        return signals_array
    
    filtered_indices = [signal_indices[0]]
    
    for i in range(1, len(signal_indices)):
        idx = signal_indices[i]
        if idx - filtered_indices[-1] >= min_distance:
            filtered_indices.append(idx)
        elif len(filtered_indices) < 10: # Allow some close signals if we don't have many
            filtered_indices.append(idx)
            
    # Create new signal array
    filtered_signals_array = numpy.zeros_like(signals_array, dtype=numpy.bool_)
    for idx in filtered_indices:
        filtered_signals_array[idx] = True
    
    return filtered_signals_array

def filter_signals_by_distance(signals: pl.Series, min_distance: int = MIN_SIGNAL_DISTANCE) -> pl.Series:
    """Filter signals to maintain minimum distance between them - RELAXED"""
    # Convert Polars Series to a boolean NumPy array for Numba processing
    # Ensure the array is explicitly boolean to avoid 'pyobject' type issues with Numba
    signals_np = signals.to_numpy(allow_copy=True).astype(np.bool_)
    
    # Call the Numba-optimized function
    filtered_np = _filter_signals_numba(signals_np, min_distance)
    
    # Convert back to Polars Series
    return pl.Series(signals.name, filtered_np)

def calculate_daily_opening_range(df: pl.DataFrame, orb_period: int) -> pl.DataFrame:
    """
    Calculates the Opening Range High and Low for each day.
    The opening range is defined by the high and low of the first `orb_period` bars of each day.
    """
    if "datetime" not in df.columns:
        logger.error("Datetime column not found for ORB calculation.")
        return df

    # Ensure datetime is sorted and convert to date for grouping
    df = df.sort("datetime")
    
    # Calculate daily high and low for the first `orb_period` bars of each day
    # Use a window function to get the high/low for the first N bars of each day
    df = df.with_columns(
        pl.col("high").over(pl.col("datetime").dt.date()).head(orb_period).max().alias("daily_orb_high"),
        pl.col("low").over(pl.col("datetime").dt.date()).head(orb_period).min().alias("daily_orb_low")
    )
    
    logger.debug(f"Calculated daily opening range for period {orb_period}.")
    return df

def process_signals_vectorized(df: pl.DataFrame, long_signals: pl.Series, short_signals: pl.Series, strategy: Dict[str, Any], rr: List[float], timeframe: str) -> Optional[List[Dict[str, Any]]]:
    """Process signals using vectorbt for backtesting."""
    try:
        strategy_name = strategy.get('name', 'Unknown')
        logger.debug(f"Processing signals with vectorbt for {strategy_name}")

        # Convert Polars Series to NumPy/CuPy arrays
        # Use .to_numpy() which will return CuPy array if np is aliased to cp
        # Convert Polars Series to NumPy/CuPy arrays and reshape to 2D for vectorbt
        price_data = df["close"].to_numpy(allow_copy=True).astype(np.float64).reshape(-1, 1)
        open_data = df["open"].to_numpy(allow_copy=True).astype(np.float64).reshape(-1, 1)
        high_data = df["high"].to_numpy(allow_copy=True).astype(np.float64).reshape(-1, 1)
        low_data = df["low"].to_numpy(allow_copy=True).astype(np.float64).reshape(-1, 1)

        # The data is now pre-processed in simulate_trades_vectorized to handle NaNs, infs, and non-positive values.
        # Therefore, the explicit validation check here is no longer necessary.

        # --- LOGIC FOR INTRADAY CLOSURE ---
        # Identify the last bar of each day using a window function
        # Sort by datetime within each day group and mark the last one
        eod_exit_series = df.with_columns(
            (pl.col("datetime").rank(method="dense", descending=True).over(pl.col("datetime").dt.date()) == 1).alias("eod_exit")
        ).select("eod_exit").to_series()

        # Convert signals to numpy arrays
        long_entries = long_signals.to_numpy(allow_copy=True).astype(np.bool_).reshape(-1, 1)
        short_entries = short_signals.to_numpy(allow_copy=True).astype(np.bool_).reshape(-1, 1)
        eod_exits = eod_exit_series.to_numpy(allow_copy=True).astype(np.bool_).reshape(-1, 1)

        # Debug: Log signal counts
        logger.debug(f"Long entry signals count: {long_entries.sum()}")
        logger.debug(f"Short entry signals count: {short_entries.sum()}")
        logger.debug(f"EOD exit signals count: {eod_exits.sum()}")
        logger.debug(f"Price data shape: {price_data.shape}")

        # Create separate portfolios for long and short strategies
        trades_list = []
        
        # Process long trades if we have long signals
        if long_entries.sum() > 0:
            logger.debug(f"Processing long trades for {strategy_name}")
            pf_long = vbt.Portfolio.from_signals(
                price_data,
                long_entries,
                eod_exits,  # Use EOD exits for long positions
                init_cash=INITIAL_CAPITAL,
                fees=TRANSACTION_COST_PCT / 100.0,
                slippage=SLIPPAGE_PCT / 100.0,
            )
            
            if pf_long.trades.count().iloc[0] > 0:
                long_trades = extract_trades_from_portfolio(pf_long, df, 1)  # 1 for long
                trades_list.extend(long_trades)
                logger.debug(f"Generated {len(long_trades)} long trades")
        
        # Process short trades if we have short signals
        if short_entries.sum() > 0:
            logger.debug(f"Processing short trades for {strategy_name}")
            pf_short = vbt.Portfolio.from_signals(
                price_data,
                short_entries,
                eod_exits,  # Use EOD exits for short positions
                init_cash=INITIAL_CAPITAL,
                fees=TRANSACTION_COST_PCT / 100.0,
                slippage=SLIPPAGE_PCT / 100.0,
                short_entries=True,  # Enable short selling
            )
            
            if pf_short.trades.count().iloc[0] > 0:
                short_trades = extract_trades_from_portfolio(pf_short, df, -1)  # -1 for short
                trades_list.extend(short_trades)
                logger.debug(f"Generated {len(short_trades)} short trades")
        
        if len(trades_list) == 0:
            logger.warning(f"No trades generated by vectorbt for {strategy_name}")
            return None
        
        logger.debug(f"Generated {len(trades_list)} total trades using vectorbt for {strategy_name}")
        return trades_list

    except Exception as e:
        logger.error(f"vectorbt processing failed for {strategy.get('name', 'Unknown')}: {e}")
        import traceback
        logger.error(f"Full traceback: {traceback.format_exc()}")
        return None

def extract_trades_from_portfolio(pf, df, signal_type):
    """Extract trades from a vectorbt portfolio"""
    trades_list = []
    
    # Get the trades records for the first column
    trades_records = pf.trades.records_readable
    
    # Check if trades_records is empty
    if len(trades_records) == 0:
        return trades_list

    # Iterate over trade records
    for _, trade_row in trades_records.iterrows():
        try:
            # Use the actual column names from vectorbt
            quantity = abs(float(trade_row['Size']))
            entry_price = float(trade_row['Avg Entry Price'])
            exit_price = float(trade_row['Avg Exit Price'])
            trade_pnl = float(trade_row['PnL'])

            # Get timestamps - these are actually indices in vectorbt
            entry_idx = int(trade_row['Entry Timestamp'])
            exit_idx = int(trade_row['Exit Timestamp'])

            holding_period = exit_idx - entry_idx
            position_value = quantity * entry_price
            trade_pnl_pct = (trade_pnl / position_value) * 100 if position_value > 0 else 0

            trades_list.append({
                'entry_datetime': df["datetime"][entry_idx],
                'exit_datetime': df["datetime"][exit_idx],
                'entry_price': entry_price,
                'exit_price': exit_price,
                'signal_type': signal_type,
                'pnl': round(trade_pnl, 2),
                'pnl_pct': round(trade_pnl_pct, 2),
                'holding_period': int(holding_period),
                'quantity': quantity,
                'position_value': position_value,
                'stop_loss_price': None,
                'take_profit_price': None,
            })
        except Exception as e:
            logger.error(f"Error processing trade record: {e}")
            continue
    
    return trades_list

def simulate_trades_vectorized(df: pl.DataFrame, strategy: Dict[str, Any], rr: List[float], timeframe: str) -> Optional[List[Dict[str, Any]]]:
    """Optimized trade simulation with enhanced debugging"""
    logger.debug(f"Entering simulate_trades_vectorized for {strategy.get('name', 'Unknown')} with {len(df)} rows.")
    try:
        strategy_name = strategy.get('name', 'Unknown')
        logger.debug(f"Starting trade simulation for {strategy_name}")
        
        # Basic data validation and sorting
        df = df.sort("datetime")
        if len(df) < 20:  # Reduced from 50 for quick test
            logger.warning(f"Insufficient data for {strategy_name}: {len(df)} rows")
            return None

        # Identify all numerical columns that might contain NaNs
        numerical_cols = [
            'open', 'high', 'low', 'close', 'volume',
            'rsi_14', 'rsi_5', 'ema_5', 'ema_10', 'ema_13', 'ema_20', 'ema_21', 'ema_50',
            'vwap', 'supertrend', 'cpr_top', 'cpr_bottom', 'macd', 'macd_signal',
            'bb_upper', 'bb_lower', 'bb_middle', 'atr', 'adx', 'cci', 'mfi',
            'stoch_k', 'stoch_d', 'pivot', 'resistance', 'support',
            'donchian_high', 'donchian_low',
            'vcp_pattern', 'upward_candle', 'downward_candle'
        ]

        # Filter to only include columns actually present in the DataFrame
        cols_to_process = [col for col in numerical_cols if col in df.columns]

        # Apply a chain of operations: replace inf, replace non-positive (except for binary indicators), then fill nulls
        expressions = []
        # Binary indicator columns that can legitimately have 0 values
        binary_indicators = ['vcp_pattern', 'upward_candle', 'downward_candle']
        
        # Technical indicators that can have values between 0-100 (RSI, Stochastic, etc.)
        percentage_indicators = ['rsi_14', 'rsi_5', 'stoch_k', 'stoch_d', 'mfi']
        
        # Price-based columns that should be positive
        price_columns = ['open', 'high', 'low', 'close', 'volume', 'atr']
        
        for col in cols_to_process:
            if col in binary_indicators:
                # For binary indicators, replace infinite and NaN values, but not zeros
                expressions.append(
                    pl.when(pl.col(col).is_infinite() | pl.col(col).is_nan())
                    .then(pl.lit(None, dtype=pl.Float64))
                    .otherwise(pl.col(col))
                    .fill_null(strategy='forward')
                    .fill_null(strategy='backward')
                    .alias(col)
                )
            elif col in percentage_indicators:
                # For percentage indicators (RSI, Stochastic, etc.), replace infinite, NaN, and values outside valid range
                expressions.append(
                    pl.when(pl.col(col).is_infinite() | pl.col(col).is_nan() | (pl.col(col) < 0) | (pl.col(col) > 100))
                    .then(pl.lit(None, dtype=pl.Float64))
                    .otherwise(pl.col(col))
                    .fill_null(strategy='forward')
                    .fill_null(strategy='backward')
                    .alias(col)
                )
            elif col in price_columns:
                # For price/volume columns, replace inf and non-positive values
                expressions.append(
                    pl.when(pl.col(col).is_infinite() | (pl.col(col) <= 0))
                    .then(pl.lit(None, dtype=pl.Float64))
                    .otherwise(pl.col(col))
                    .fill_null(strategy='forward')
                    .fill_null(strategy='backward')
                    .alias(col)
                )
            else:
                # For other technical indicators (EMA, MACD, etc.), replace infinite and NaN values
                expressions.append(
                    pl.when(pl.col(col).is_infinite() | pl.col(col).is_nan())
                    .then(pl.lit(None, dtype=pl.Float64))
                    .otherwise(pl.col(col))
                    .fill_null(strategy='forward')
                    .fill_null(strategy='backward')
                    .alias(col)
                )
        df = df.with_columns(expressions)
        
        # Drop any remaining nulls in critical columns that couldn't be filled (e.g., leading/trailing NaNs if all values were bad)
        df = df.drop_nulls(subset=['close', 'high', 'low', 'open', 'volume', 'datetime'])
        logger.debug(f"DataFrame rows after critical nulls dropped: {len(df)}")
        if df.is_empty():
            logger.warning(f"DataFrame became empty after dropping critical nulls for {strategy_name}. Skipping.")
            return None

        logger.debug(f"Data validated and NaNs, infs, and non-positive values handled: {len(df)} rows")

        # Calculate Opening Range if it's the ORB strategy
        if strategy_name == "Opening_Range_Breakout":
            # The ORB period (e.g., first 15 minutes) should be configurable or derived.
            # For now, let's assume a default of 15 bars for 1min timeframe, or adjust based on timeframe.
            # A more robust solution would involve passing this as a strategy parameter.
            orb_period = 15 # Default for 1min timeframe, adjust as needed
            if timeframe == "3min": orb_period = 5
            elif timeframe == "5min": orb_period = 3
            elif timeframe == "15min": orb_period = 1
            
            df = calculate_daily_opening_range(df, orb_period)
            # Ensure the new columns are available for signal generation
            if "daily_orb_high" not in df.columns or "daily_orb_low" not in df.columns:
                logger.error(f"Failed to calculate daily opening range for {strategy_name}. Skipping.")
                return None
            logger.debug(f"Daily ORB calculated for {strategy_name}. Columns added.")

        # Generate signals with detailed logging
        logger.debug(f"Generating long signals for {strategy_name}")
        long_signals_raw = generate_strategy_signals(df, "long", strategy)
        logger.debug(f"Long signals raw count: {long_signals_raw.sum()}")
        
        logger.debug(f"Generating short signals for {strategy_name}")
        short_signals_raw = generate_strategy_signals(df, "short", strategy)
        logger.debug(f"Short signals raw count: {short_signals_raw.sum()}")
        
        # Apply distance filtering
        # Only filter if there are signals to avoid unnecessary computation
        long_signals = long_signals_raw
        if long_signals.sum() > 0:
            long_signals = filter_signals_by_distance(long_signals_raw, min_distance=MIN_SIGNAL_DISTANCE)
            logger.debug(f"Long signals after filtering: {long_signals.sum()}")
        
        short_signals = short_signals_raw
        if short_signals.sum() > 0:
            short_signals = filter_signals_by_distance(short_signals_raw, min_distance=MIN_SIGNAL_DISTANCE)
            logger.debug(f"Short signals after filtering: {short_signals.sum()}")
        
        # Filter to only signal rows once (this is for logging/early exit, not for vectorbt input)
        # The actual signals passed to vectorbt are long_signals and short_signals directly.
        if long_signals.sum() == 0 and short_signals.sum() == 0:
            logger.warning(f"No signals generated or found after filtering for {strategy_name}")
            return None

        # Process all signals without trade limiting for complete strategy analysis
        trades = process_signals_vectorized(df, long_signals, short_signals, strategy, rr, timeframe)
        
        return trades
        
    except Exception as e:
        logger.error(f"Vectorized trade simulation failed for {strategy.get('name', 'Unknown')}: {e}")
        import traceback
        logger.error(f"Full traceback: {traceback.format_exc()}")
        return None

def calculate_performance_metrics(trades: List[Dict[str, Any]], symbol: str, strategy_name: str, timeframe: str, rr_combo: List[float]) -> Optional[Dict[str, Any]]:
    """Calculate performance metrics with VERY RELAXED validation for debugging"""
    if not trades:
        logger.debug(f"No trades provided for {symbol} {strategy_name} {timeframe}")
        return None
    
    logger.debug(f"Calculating metrics for {len(trades)} trades")
    
    total_pnl = sum(t['pnl'] for t in trades)
    total_pnl_pct = sum(t['pnl_pct'] for t in trades)
    winning_trades = sum(1 for t in trades if t['pnl'] > 0)
    total_trades = len(trades)
    
    accuracy = winning_trades / total_trades if total_trades > 0 else 0
    expectancy = total_pnl / total_trades if total_trades > 0 else 0
    
    avg_win = sum(t['pnl'] for t in trades if t['pnl'] > 0) / winning_trades if winning_trades > 0 else 0
    losing_trades = total_trades - winning_trades
    avg_loss = sum(t['pnl'] for t in trades if t['pnl'] < 0) / losing_trades if losing_trades > 0 else 0
    
    profit_factor = avg_win / abs(avg_loss) if avg_loss != 0 else float('inf')
    
    # Calculate drawdown
    cumulative_pnl = []
    running_pnl = 0
    for trade in trades:
        running_pnl += trade['pnl_pct']
        cumulative_pnl.append(running_pnl)
    
    max_drawdown = 0
    if cumulative_pnl:
        peak = cumulative_pnl[0]
        for pnl in cumulative_pnl:
            if pnl > peak:
                peak = pnl
            drawdown = peak - pnl
            if drawdown > max_drawdown:
                max_drawdown = drawdown
    
    # Calculate Sharpe ratio
    pnl_series_pl = pl.Series([t['pnl_pct'] for t in trades])
    if len(pnl_series_pl) > 1:
        std_dev = pnl_series_pl.std()
        sharpe_ratio = (pnl_series_pl.mean() * math.sqrt(252)) / std_dev if std_dev > 0 else 0
    else:
        sharpe_ratio = 0
    
    result = {
        'strategy_name': strategy_name,
        'risk_reward_ratio': f"{rr_combo[0]}:{rr_combo[1]}",
        'total_trades': total_trades,
        'winning_trades': winning_trades,
        'accuracy': round(accuracy * 100, 2),
        'total_pnl': round(total_pnl, 2),
        'roi': round(total_pnl_pct, 2),
        'expectancy': round(expectancy, 2),
        'avg_win': round(avg_win, 2),
        'avg_loss': round(avg_loss, 2),
        'profit_factor': round(profit_factor, 2),
        'max_drawdown': round(max_drawdown, 2),
        'sharpe_ratio': round(sharpe_ratio, 2),
        'avg_holding_period': round(sum(t['holding_period'] for t in trades) / total_trades, 1),
        'is_profitable': total_pnl_pct > PROFIT_THRESHOLD,
        # Placeholder values for AI Training Agent compatibility
        'trade_direction': 'no_trade', # Default to 'no_trade'
        'signal_confidence': round(random.uniform(0.0, 1.0), 2), # Random confidence for now
        'market_regime': 'sideways', # Default to 'sideways'
        'expected_roi': round(expectancy, 2), # Can use expectancy as a proxy for expected_roi
        'best_strategy_id': random.randint(1, 25) # Random strategy ID for now (assuming 25 strategies)
    }
    
    logger.debug(f"Metrics calculated: {result}")
    return result

# File loading and processing functions
def load_strategies() -> List[Dict[str, Any]]:
    """Load strategies from YAML file with proper Windows encoding"""
    try:
        # Fix Windows encoding issue - explicitly use UTF-8
        with open(STRATEGIES_FILE, 'r', encoding='utf-8') as f:
            data = yaml.safe_load(f)
        strategies = data.get('strategies', [])
        logger.info(f"[LOAD] Loaded {len(strategies)} strategies")
        return strategies
    except Exception as e:
        logger.error(f"Failed to load strategies from {STRATEGIES_FILE}: {e}")
        return []

def get_available_feature_files() -> List[Tuple[str, str, str]]:
    """Get available feature files"""
    feature_files = []
    for file_path in Path(DATA_DIR).glob("*.parquet"):
        filename = file_path.name
        symbol, timeframe = extract_symbol_and_timeframe_from_filename(filename)
        if symbol and timeframe and timeframe in TIMEFRAMES:
            feature_files.append((str(file_path), symbol, timeframe))
    logger.info(f"[FOUND] {len(feature_files)} feature files")
    return feature_files

def extract_symbol_and_timeframe_from_filename(filename: str) -> Tuple[Optional[str], Optional[str]]:
    """Extract symbol and timeframe from filename"""
    try:
        stem = Path(filename).stem
        if stem.startswith("features_"):
            stem = stem[9:]
        parts = stem.split('_')
        timeframe = parts[-1] if parts[-1] in TIMEFRAMES else None
        symbol = '_'.join(parts[:-1]) if timeframe else None
        return symbol, timeframe
    except Exception as e:
        logger.debug(f"Failed to extract symbol/timeframe from {filename}: {e}")
    return None, None

def generate_output_filename(symbol: str, timeframe: str) -> str:
    """Generate output filename"""
    return f"backtest_{symbol}_{timeframe}.parquet"

def generate_temp_filename(symbol: str, timeframe: str, strategy_name: str, rr_combo: List[float]) -> str:
    """Generate a unique temporary filename for each strategy, timeframe, stock, and RR combination."""
    rr_str = f"{rr_combo[0]}_{rr_combo[1]}".replace(".", "_")
    return f"temp_backtest_{symbol}_{timeframe}_{strategy_name}_{rr_str}.parquet"

async def write_symbol_results_async(results: List[Dict[str, Any]], symbol: str, timeframe: str):
    """Write results to file immediately after processing each feature file"""
    if not results:
        logger.debug(f"No results to write for {symbol} ({timeframe})")
        return
    
    try:
        output_filename = generate_output_filename(symbol, timeframe)
        output_path = os.path.join(OUTPUT_DIR, output_filename)
        
        # Ensure output directory exists
        Path(OUTPUT_DIR).mkdir(parents=True, exist_ok=True)
        
        # Write results using efficient parquet format with compression
        start_time = time.time()
        
        # Use asyncio to run the blocking I/O operation in a thread pool
        loop = asyncio.get_running_loop()
        await loop.run_in_executor(
            None, 
            lambda: pl.DataFrame(results).write_parquet(output_path, compression=COMPRESSION)
        )
        
        write_time = time.time() - start_time
        
        logger.info(f"[SUCCESS] Written {len(results)} results for {symbol} ({timeframe}) to {output_filename} in {write_time:.2f}s")
        
        # Optional: Force flush to ensure data is written to disk immediately
        # This adds minimal overhead but ensures data persistence
        if hasattr(os, 'sync'):
            os.sync()  # Unix/Linux only
            
    except Exception as e:
        logger.error(f"[ERROR] Error writing results for {symbol} ({timeframe}): {e}")
        import traceback
        logger.error(f"Full traceback: {traceback.format_exc()}")

def process_single_file_sync(file_info: Tuple[str, str, str], strategies: List[Dict[str, Any]], risk_reward_ratios: List[List[float]]) -> List[Dict[str, Any]]:
    """Synchronous function to process a single file for backtesting."""
    file_path, symbol, timeframe = file_info
    file_results = []
    
    start_time = time.time()
    logger.info(f"Processing {symbol} ({timeframe})...")
    
    try:
        df = pl.read_parquet(file_path)
        logger.info(f"Loaded {len(df)} rows for {symbol}")
        
        required_cols = ['datetime', 'open', 'high', 'low', 'close', 'volume']
        missing_cols = [col for col in required_cols if col not in df.columns]
        if missing_cols:
            logger.error(f"Missing required columns for {symbol}: {missing_cols}")
            return []
        
        for strategy_idx, strategy in enumerate(strategies):
            strategy_name = strategy.get('name', f'Strategy_{strategy_idx}')
            logger.info(f"  Strategy {strategy_idx + 1}/{len(strategies)}: {strategy_name}")
            
            for rr_idx, rr in enumerate(risk_reward_ratios):
                rr_ratio_str = f"{rr[0]}:{rr[1]}"
                logger.debug(f"    Processing RR {rr_ratio_str}")
                trades = simulate_trades_vectorized(df, strategy, rr, timeframe) # Now synchronous
                
                logger.debug(f"    Generated {len(trades) if trades else 0} trades for RR {rr_ratio_str}")
                
                if trades and len(trades) >= 1:
                    metrics = calculate_performance_metrics(trades, symbol, strategy_name, timeframe, rr)
                    if metrics:
                        file_results.append(metrics)
                        logger.info(f"    Added metrics for RR {rr_ratio_str} - {len(trades)} trades")
                else:
                    logger.debug(f"    No valid trades for RR {rr_ratio_str}")
        
        file_time = time.time() - start_time
        logger.info(f"  File processed in {file_time:.2f}s")
        
    except Exception as e:
        logger.error(f"Error processing {symbol}: {e}")
        import traceback
        logger.error(f"Full traceback: {traceback.format_exc()}")
        
    return file_results

# Utility functions
def aggressive_memory_cleanup():
    """Aggressive memory cleanup"""
    gc.collect()

def reset_polars_state():
    """Reset Polars state"""
    pass

# Main async function
async def main_async():
    """Main asynchronous entry point"""
    logger.info("[INIT] Starting Enhanced Backtesting System - PERFORMANCE OPTIMIZED")
    logger.info("=" * 80)
    
    start_time = time.time()
    
    # Load strategies
    strategies = load_strategies()
    if not strategies:
        logger.error("[ERROR] No strategies loaded, exiting")
        return
    
    # Get files
    feature_files = get_available_feature_files()
    if not feature_files:
        logger.error("[ERROR] No feature files found, exiting")
        return
    
    logger.info(f"[INFO] Processing {len(feature_files)} files with {len(strategies)} strategies")
    
    # Ensure temporary directory exists (still needed for other potential uses, though not for temp results)
    Path(TEMP_DIR).mkdir(parents=True, exist_ok=True)

    init_executors() # Initialize the executor

    total_files = len(feature_files)
    all_backtest_results: List[Dict[str, Any]] = [] # Accumulate all results here

    if USE_PROCESS_POOL_EXECUTOR and process_executor:
        logger.info(f"[INFO] Using ProcessPoolExecutor for parallel strategy processing with {CONCURRENT_FILES} workers.")
        loop = asyncio.get_running_loop()
        
        # Process each feature file and write output immediately after completion
        for idx, file_info in enumerate(feature_files, 1):
            file_path, symbol, timeframe = file_info
            logger.info(f"Processing file {idx}/{total_files}: {symbol} ({timeframe})")
            
            # Create combinations for this specific file
            file_combinations = []
            for strategy in strategies:
                for rr in RISK_REWARD_RATIOS:
                    file_combinations.append((file_info, strategy, rr))

            # Submit all strategy/RR combinations for this file to the process pool
            futures = [
                loop.run_in_executor(
                    process_executor,
                    process_file_strategy_rr,
                    combo[0], combo[1], combo[2]
                ) for combo in file_combinations
            ]

            # Collect results for this file
            file_results = []
            for i, future in enumerate(asyncio.as_completed(futures)):
                try:
                    result_metrics = await future
                    if result_metrics:
                        file_results.extend(result_metrics)
                    logger.debug(f"Completed {i+1}/{len(futures)} combinations for {symbol}")
                except Exception as e:
                    logger.error(f"A parallel task failed for {symbol}: {e}")
            
            # Write output file immediately after processing this feature file
            if file_results:
                await write_symbol_results_async(file_results, symbol, timeframe)
                all_backtest_results.extend(file_results)
                logger.info(f"[SUCCESS] Completed and saved {symbol} ({timeframe}) - {len(file_results)} results")
            else:
                logger.warning(f"[WARNING] No results generated for {symbol} ({timeframe})")
            
            # Clear file_results to free memory immediately after writing
            file_results.clear()
            
            # Memory cleanup after each file (more frequent due to immediate file writing)
            if idx % 3 == 0:  # More frequent cleanup since we're writing files more often
                gc.collect()
                logger.debug(f"Memory cleanup performed after {idx} files")

    else:
        logger.info("[INFO] Running in sequential mode.")
        # Sequential processing with immediate file writing after each feature file
        for idx, file_info in enumerate(feature_files, 1):
            file_path, symbol, timeframe = file_info
            logger.info(f"Processing file {idx}/{total_files}: {symbol} ({timeframe})")
            
            results_from_file = process_single_file_sync(file_info, strategies, RISK_REWARD_RATIOS)
            
            # Write output file immediately after processing this feature file
            if results_from_file:
                await write_symbol_results_async(results_from_file, symbol, timeframe)
                all_backtest_results.extend(results_from_file)
                logger.info(f"[SUCCESS] Completed and saved {symbol} ({timeframe}) - {len(results_from_file)} results")
            else:
                logger.warning(f"[WARNING] No results generated for {symbol} ({timeframe})")
            
            # Clear results_from_file to free memory immediately after writing
            results_from_file.clear()
            
            # Memory cleanup after each file (more frequent due to immediate file writing)
            if idx % 5 == 0:  # More frequent cleanup since we're writing files more often
                gc.collect()
                logger.debug("Sequential mode memory cleanup performed.")


    cleanup_executors() # Shut down the executor

    # Summary
    end_time = time.time()
    total_time = end_time - start_time
    logger.info("[SUCCESS] BACKTESTING COMPLETED!")
    logger.info(f"[TIME] Total time: {total_time:.1f} seconds")
    logger.info(f"[FILES] Files processed: {total_files}")
    logger.info(f"[RESULTS] Total backtest results generated: {len(all_backtest_results)}")
    
    # Output summary
    output_files = list(Path(OUTPUT_DIR).glob("backtest_*.parquet"))
    if output_files:
        total_size = sum(f.stat().st_size for f in output_files) / (1024 * 1024)
        logger.info(f"[OUTPUT] Generated {len(output_files)} files ({total_size:.1f} MB)")
        logger.info(f"[PERFORMANCE] Files written immediately after processing - no batch delays")

def process_file_strategy_rr(file_info, strategy, rr) -> List[Dict[str, Any]]:
    """Synchronous function to process a single file for a specific strategy and RR, returning metrics directly."""
    file_path, symbol, timeframe = file_info
    strategy_name = strategy.get('name', 'UnknownStrategy')
    
    try:
        df = pl.read_parquet(file_path)
        if len(df) < 1000:
            logger.error(f"Skipping {symbol} ({timeframe}) for {strategy_name} due to insufficient data ({len(df)} rows). Minimum 1000 rows required.")
            return []

        trades = simulate_trades_vectorized(df, strategy, rr, timeframe)
        
        result_metrics = []
        if trades and len(trades) >= 1:
            metrics = calculate_performance_metrics(trades, symbol, strategy_name, timeframe, rr)
            if metrics:
                result_metrics.append(metrics)
        
        # Explicitly delete the DataFrame to free memory
        del df 
        # Aggressive memory cleanup after each combination
        aggressive_memory_cleanup()
        reset_polars_state() # This function is currently empty, but kept for consistency
        
        return result_metrics
    except Exception as e:
        logger.error(f"Failed for {symbol} | {strategy_name} | {rr}: {e}")
        import traceback
        logger.error(f"Full traceback: {traceback.format_exc()}")
        return []


# Main function for direct execution
async def main():
    """Main function for direct execution"""
    await main_async()


if __name__ == "__main__":
    asyncio.run(main())
