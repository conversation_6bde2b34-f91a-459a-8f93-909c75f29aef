#!/usr/bin/env python3
"""
CLEAN MARKET DATA AGENT
Modern implementation with proper async handling and rate limiting

Features:
- Proper SmartAPI date format handling (YYYY-MM-DD HH:MM)
- Modern timeframes: 1min, 2min, 3min, 5min, 10min, 15min, 30min, 1hr
- Clean async/await patterns
- WebSocket v2 integration
- Comprehensive error handling
- Global rate limiting to prevent API rate limit errors
"""

import asyncio
import logging
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, List, Any, Optional, Tuple
import polars as pl
import random
import os
import json
from pathlib import Path
import time
import threading
import numpy as np
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

from core.base_agent import BaseAgent
from core.event_system import EventBus, EventTypes
from core.smartapi_client import ModernSmartAPIClient, SmartAPICredentials, HistoricalDataRequest

logger = logging.getLogger(__name__)

class SmartAPIDownloadManager:
    """
    Global SmartAPI download manager that handles rate limiting across all threads.
    Based on the working pattern from run_clean_trading_system.py
    """
    _instance = None
    _lock = threading.Lock()
    _last_api_call = 0
    _min_interval = 1.0  # 1.0 second minimum interval (same as reference)

    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance

    @classmethod
    def download_with_rate_limit(cls, market_data_agent, symbol: str, start_date: datetime, end_date: datetime):
        """
        Download historical data with proper global rate limiting.
        This follows the exact pattern from the working reference file.
        """
        # Apply rate limiting exactly like in the reference file
        with cls._lock:
            current_time = time.time()
            time_since_last_call = current_time - cls._last_api_call

            if time_since_last_call < cls._min_interval:
                sleep_time = cls._min_interval - time_since_last_call
                logger.debug(f"[RATE-LIMIT] Sleeping for {sleep_time:.2f}s to respect API limits")
                time.sleep(sleep_time)

            cls._last_api_call = time.time()

        # Make the API call outside the lock to allow concurrent processing
        try:
            result = cls._make_sync_api_call(market_data_agent, symbol, start_date, end_date)
            return result
        except Exception as e:
            logger.error(f"[SMARTAPI] Error downloading data for {symbol}: {e}")
            return None

    @classmethod
    def _make_sync_api_call(cls, market_data_agent, symbol: str, start_date: datetime, end_date: datetime):
        """
        Make synchronous API call to avoid event loop conflicts.
        This creates a new event loop per thread to avoid conflicts.
        """
        try:
            # Create a new event loop for this thread to avoid conflicts
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            try:
                result = loop.run_until_complete(
                    market_data_agent.download_historical_data_for_symbol(
                        symbol=symbol,
                        start_date=start_date,
                        end_date=end_date
                    )
                )
                return result
            finally:
                loop.close()

        except Exception as e:
            logger.error(f"[SMARTAPI] Sync API call failed for {symbol}: {e}")
            return None

class SmartAPIRateLimiter:
    """
    Global SmartAPI rate limiter that handles rate limiting across all async workers.
    Based on the working pattern from run_clean_trading_system.py
    """
    _instance = None
    _lock = threading.Lock()
    _last_api_call = 0
    _min_interval = 1.0  # 1.0 second minimum interval (same as working reference)

    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance

    @classmethod
    async def download_with_rate_limit(cls, smartapi_client, symbol_token: str, exchange: str,
                                     start_date: datetime, end_date: datetime, interval: str):
        """
        Download historical data with proper global rate limiting.
        This follows the exact pattern from the working reference file.
        """
        # Apply rate limiting exactly like in the reference file
        with cls._lock:
            current_time = time.time()
            time_since_last_call = current_time - cls._last_api_call

            if time_since_last_call < cls._min_interval:
                sleep_time = cls._min_interval - time_since_last_call
                logger.debug(f"[RATE-LIMIT] Sleeping for {sleep_time:.2f}s to respect API limits")
                # Use asyncio.sleep for async context
                await asyncio.sleep(sleep_time)

            cls._last_api_call = time.time()

        # Make the API call outside the lock to allow concurrent processing
        try:
            data = await smartapi_client.get_historical_data_batch(
                symbol_token=symbol_token, exchange=exchange,
                start_date=start_date, end_date=end_date, interval=interval
            )
            return data
        except Exception as e:
            logger.error(f"[SMARTAPI] Error downloading data for token {symbol_token}: {e}")
            return None

class CleanMarketDataAgent(BaseAgent):
    """
    Clean Market Data Agent with modern architecture
    
    Features:
    - Proper date format handling
    - Modern timeframes
    - Clean async patterns
    - WebSocket v2 integration
    - Global rate limiting
    """
    
    def __init__(self, event_bus: EventBus, config: Any, session_id: str):
        super().__init__("CleanMarketDataAgent", event_bus, config, session_id)
        
        # SmartAPI client
        self.smartapi_client = None
        
        # Data storage with training timeframes
        training_timeframes = getattr(config, 'timeframes', ["1min", "3min", "5min", "15min"])
        self.timeframe_data = {tf: {} for tf in training_timeframes}
        
        # Real-time data
        self.realtime_data = {}
        
        # Instrument mapping
        self.instrument_map = {}
        self.symbol_to_token_map = self._load_token_mapping()

        # Configuration - will be updated dynamically by stock selector
        self.config = config
        self.selected_stocks = []  # Start empty, will be updated by stock selector
        
        # Retry configuration for historical data downloads - more conservative approach
        self.retry_config = {
            'max_retries': getattr(config, 'data_download_max_retries', 3),
            'base_delay': getattr(config, 'data_download_base_delay', 3.0),  # Increased from 2.0 to 3.0 seconds
            'max_delay': getattr(config, 'data_download_max_delay', 60.0),   # Increased from 30.0 to 60.0 seconds
            'exponential_base': getattr(config, 'data_download_exponential_base', 2.0),
            'jitter': getattr(config, 'data_download_jitter', True)
        }
        
        # Download workers for concurrent data fetching
        self.data_cache: Dict[str, pl.DataFrame] = {}
        self.download_queue = asyncio.Queue()
        # Reduced from 10 to 5 to match the working implementation's conservative approach
        self.num_download_workers = 5
        self.download_workers: List[asyncio.Task] = []
        
        self.log_info(f"Initialized with {self.num_download_workers} download workers with rate limiting.")

    def _load_token_mapping(self) -> Dict[str, str]:
        try:
            mapping_file_path = Path(__file__).parent.parent / 'token_mapping.json'
            with open(mapping_file_path, 'r') as f:
                data = json.load(f)
                self.log_info(f"Successfully loaded token mapping from {mapping_file_path}")
                return data.get("symbol_to_token", {})
        except Exception as e:
            self.log_error(f"Could not load or parse token_mapping.json: {e}")
            return {}

    def _load_credentials(self) -> Optional[SmartAPICredentials]:
        try:
            api_key, username, password, totp_token = (
                os.getenv('SMARTAPI_API_KEY'), os.getenv('SMARTAPI_USERNAME'),
                os.getenv('SMARTAPI_PASSWORD'), os.getenv('SMARTAPI_TOTP_TOKEN')
            )
            if all((api_key, username, password, totp_token)):
                return SmartAPICredentials(api_key, username, password, totp_token)
        except Exception as e:
            self.log_error(f"Failed to load credentials: {e}")
        return None

    async def initialize(self) -> bool:
        """Initialize the market data agent"""
        try:
            self.log_info("Initializing Clean Market Data Agent...")
            
            # Load credentials
            credentials = self._load_credentials()
            if not credentials:
                self.log_warning("No SmartAPI credentials found, running in demo mode")
                self.initialized = True
                return True
            
            # Initialize SmartAPI client
            self.smartapi_client = ModernSmartAPIClient(credentials)
            
            # Authenticate
            auth_success = await self.smartapi_client.authenticate()
            if not auth_success:
                self.log_error("SmartAPI authentication failed, falling back to demo mode")
                self.smartapi_client = None
            
            # Subscribe to events
            self.event_bus.subscribe("REQUEST_HISTORICAL_DATA", self._handle_data_request)
            self.event_bus.subscribe("STOCK_UNIVERSE_UPDATED", self._handle_universe_update)
            
            self.initialized = True
            self.log_info("Clean Market Data Agent initialized successfully")
            return True
            
        except Exception as e:
            self.log_error(f"Failed to initialize: {e}")
            return False

    async def start(self):
        self.log_info("Starting download workers...")
        self.running = True
        self.download_workers = [
            asyncio.create_task(self._download_worker(i)) for i in range(self.num_download_workers)
        ]

    async def _download_worker(self, worker_id: int):
        self.log_info(f"Download worker {worker_id} started.")
        while self.running:
            try:
                symbol, timeframe, event_to_set = await self.download_queue.get()
                self.log_info(f"Worker {worker_id} processing request for {symbol} ({timeframe})")
                
                data = None
                try:
                    data = await self._fetch_and_process_data(symbol, timeframe)
                except Exception as e:
                    self.log_error(f"Worker {worker_id} failed to process {symbol}: {e}")
                
                await self.event_bus.publish(
                    EventTypes.HISTORICAL_DATA_LOADED,
                    {'symbol': symbol, 'timeframe': timeframe, 'data': data},
                    source=self.name
                )
                if event_to_set:
                    event_to_set.set()
                
                # Add additional delay after each download to further reduce rate limit pressure
                await asyncio.sleep(0.5)
                
                self.download_queue.task_done()
            except asyncio.CancelledError:
                break
        self.log_info(f"Download worker {worker_id} stopped.")

    async def _fetch_and_process_data(self, symbol: str, timeframe: str) -> Optional[pl.DataFrame]:
        cache_key = f"{symbol}_{timeframe}"
        if cache_key in self.data_cache:
            self.log_info(f"Cache hit for {cache_key}.")
            return self.data_cache[cache_key]

        days = 90 if timeframe == '1day' else 30
        interval_map = {'1day': 'ONE_DAY', '1min': 'ONE_MINUTE'}
        api_interval = interval_map.get(timeframe)
        if not api_interval:
            self.log_warning(f"Unsupported timeframe '{timeframe}' for {symbol}")
            return None

        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        try:
            instrument = self._get_instrument_details(symbol)
            if not instrument or instrument.get('demo'):
                raise ValueError("No real token found.")

            df = await self._retry_with_backoff(
                self._download_real_data, symbol, instrument, start_date, end_date, api_interval
            )
            self.data_cache[cache_key] = df
            return df
        except Exception as e:
            self.log_warning(f"All attempts to fetch real data for {symbol} failed: {e}. Falling back to demo data.")
            df = self._create_demo_data(symbol, start_date, end_date, timeframe)
            self.data_cache[cache_key] = df
            return df

    async def _download_real_data(self, symbol: str, instrument: Dict, start_date: datetime, end_date: datetime, interval: str) -> pl.DataFrame:
        if not self.smartapi_client:
            raise ConnectionError("SmartAPI client not authenticated.")

        # Use the rate limiter to prevent API rate limit errors
        data = await SmartAPIRateLimiter.download_with_rate_limit(
            self.smartapi_client, 
            symbol_token=instrument['token'], 
            exchange=instrument['exchange'],
            start_date=start_date, 
            end_date=end_date, 
            interval=interval
        )
        
        if not data:
            raise ValueError(f"API returned no data for {symbol}")

        df = pl.DataFrame(data, schema=[
            ("timestamp", pl.Utf8), ("open", pl.Float64), ("high", pl.Float64),
            ("low", pl.Float64), ("close", pl.Float64), ("volume", pl.Int64)
        ]).with_columns(
            pl.col("timestamp").str.to_datetime("%Y-%m-%dT%H:%M:%S%z")
        ).sort("timestamp")
        
        if df.is_empty() or df.height < 20:
            raise ValueError(f"Insufficient data for {symbol} ({df.height} rows)")
        
        self.log_info(f"Successfully downloaded {df.height} rows for {symbol}")
        return df

    def _create_demo_data(self, symbol: str, start_date: datetime, end_date: datetime, timeframe: str) -> pl.DataFrame:
        self.log_info(f"Creating demo data for {symbol}")
        interval = "1d" if timeframe == '1day' else "1m"
        dates = pl.date_range(start_date, end_date, interval, eager=True).alias("timestamp")
        
        base_price = random.uniform(100, 3000)
        price_noise = np.random.normal(loc=0, scale=base_price * 0.02, size=len(dates))
        close_prices = base_price + np.cumsum(price_noise)
        
        df = pl.DataFrame({
            "timestamp": dates,
            "open": close_prices - np.random.uniform(0, 5, len(dates)),
            "high": close_prices + np.random.uniform(0, 5, len(dates)),
            "low": close_prices - np.random.uniform(0, 5, len(dates)),
            "close": close_prices,
            "volume": np.random.randint(100_000, 5_000_000, size=len(dates))
        })
        return df

    def _get_instrument_details(self, symbol: str) -> Optional[Dict]:
        keys_to_check = [f"{symbol}-EQ_NSE", f"{symbol}_NSE", symbol]
        token = next((self.symbol_to_token_map[key] for key in keys_to_check if key in self.symbol_to_token_map), None)
        
        if token:
            return {'token': token, 'symbol': symbol, 'exchange': 'NSE'}
        return {'token': f"DEMO_{symbol}", 'symbol': symbol, 'exchange': 'NSE', 'demo': True}

    async def _handle_data_request(self, event):
        try:
            symbol = event.data.get('symbol')
            timeframe = event.data.get('timeframe', '1day')
            event_to_set = event.data.get('event_to_set')
            if symbol:
                await self.download_queue.put((symbol, timeframe, event_to_set))
        except Exception as e:
            self.log_error(f"Failed to queue data request: {e}")

    async def _handle_universe_update(self, event):
        self.log_info("Noted updated universe. Data will be fetched on-demand.")

    async def _retry_with_backoff(self, func, *args, **kwargs):
        last_exception = None
        for attempt in range(self.retry_config['max_retries']):
            try:
                return await func(*args, **kwargs)
            except Exception as e:
                last_exception = e
                delay = self.retry_config['base_delay'] * (self.retry_config['exponential_base'] ** attempt)
                if self.retry_config['jitter']:
                    delay *= random.uniform(0.5, 1.5)
                delay = min(delay, self.retry_config['max_delay'])
                self.log_warning(f"Attempt {attempt + 1} for {args[0]} failed. Retrying in {delay:.2f}s...")
                await asyncio.sleep(delay)
        raise last_exception

    async def download_historical_data_for_symbol(self, symbol: str, start_date: datetime, end_date: datetime) -> Optional[pl.DataFrame]:
        """
        Download historical data for a single symbol.
        This method is called by SmartAPIDownloadManager from run_clean_trading_system.py
        """
        try:
            self.log_info(f"Downloading 1-min data for {symbol} from {start_date} to {end_date}")

            # Get instrument details
            instrument = self._get_instrument_details(symbol)
            if not instrument:
                self.log_warning(f"No instrument found for {symbol}")
                return None

            if instrument.get('demo'):
                self.log_warning(f"Only demo token available for {symbol}, skipping real data download")
                return None

            # Download 1-minute data using the rate limiter
            df = await self._retry_with_backoff(
                self._download_real_data,
                symbol,
                instrument,
                start_date,
                end_date,
                "ONE_MINUTE"
            )

            if df is not None and not df.is_empty():
                self.log_info(f"Successfully downloaded {len(df)} rows for {symbol}")
                return df
            else:
                self.log_warning(f"No data downloaded for {symbol}")
                return None

        except Exception as e:
            self.log_error(f"Error downloading data for {symbol}: {e}")
            return None

    async def _download_historical_data(self, stocks: List[str]):
        """
        Downloads 25 days of 1-minute historical data for the given stocks from SmartAPI
        using ThreadPoolExecutor with optimized settings and retry mechanism.
        This method follows the exact pattern from run_clean_trading_system.py
        """
        from concurrent.futures import ThreadPoolExecutor, as_completed
        import time
        from typing import Tuple
        from pathlib import Path

        # Create data directory
        live_data_path = Path(__file__).parent.parent / "data" / "live"
        live_data_path.mkdir(parents=True, exist_ok=True)

        self.log_info(f"Starting optimized historical data download from SmartAPI for {len(stocks)} stocks...")

        end_date = datetime.now()
        start_date = end_date - timedelta(days=25) # 25 days historical data

        # Configuration based on working reference file (test/download_fno_data.py)
        # Use the same settings that work successfully in the reference
        MAX_CONCURRENT_DOWNLOADS = 17  # Same as working implementation
        RATE_LIMIT_DELAY = 0.5  # Minimal delay since rate limiting is handled by SmartAPIRateLimiter
        MAX_RETRIES = 3  # Same as reference file
        RETRY_DELAY_BASE = 1.0  # Same as reference file

        self.log_info("🚀 STARTING OPTIMIZED DOWNLOAD")
        self.log_info("="*60)
        self.log_info(f"📊 Symbols: {len(stocks)}")
        self.log_info(f"📅 Period: 25 days ({start_date} to {end_date})")
        self.log_info(f"🧵 Max concurrent: {MAX_CONCURRENT_DOWNLOADS}")
        self.log_info(f"⏳ Rate limit delay: {RATE_LIMIT_DELAY}s")
        self.log_info(f"🔄 Max retries: {MAX_RETRIES}")
        self.log_info("="*60)

        # Run the download process in a thread pool to avoid blocking the event loop
        def run_threaded_downloads():
            results = {}

            # Use ThreadPoolExecutor for concurrent downloads
            with ThreadPoolExecutor(max_workers=MAX_CONCURRENT_DOWNLOADS) as executor:
                future_to_symbol = {
                    executor.submit(
                        self._download_single_stock_sync,
                        symbol,
                        start_date,
                        end_date,
                        i + 1,
                        len(stocks),
                        live_data_path
                    ): symbol
                    for i, symbol in enumerate(stocks)
                }

                for future in as_completed(future_to_symbol):
                    symbol = future_to_symbol[future]
                    try:
                        symbol_result, success = future.result()
                        results[symbol] = success

                        # Add rate limiting delay
                        time.sleep(RATE_LIMIT_DELAY)

                    except Exception as e:
                        self.log_error(f"Error downloading {symbol}: {e}")
                        results[symbol] = False

            return results

        # Execute downloads in thread pool
        start_time = time.time()
        results = await asyncio.get_event_loop().run_in_executor(None, run_threaded_downloads)

        # Count initial results and update failed_stocks list
        successful_downloads = sum(1 for success in results.values() if success)
        failed_downloads = len(results) - successful_downloads
        failed_stocks = [symbol for symbol, success in results.items() if not success]

        self.log_info(f"Initial download completed - Success: {successful_downloads}, Failed: {failed_downloads}")

        # Retry failed downloads with exponential backoff
        if failed_stocks and MAX_RETRIES > 0:
            self.log_info(f"Retrying {len(failed_stocks)} failed downloads...")

            for retry_attempt in range(1, MAX_RETRIES + 1):
                if not failed_stocks:
                    break

                retry_delay = RETRY_DELAY_BASE * (2 ** (retry_attempt - 1))  # Exponential backoff
                self.log_info(f"Retry attempt {retry_attempt}/{MAX_RETRIES} for {len(failed_stocks)} stocks (delay: {retry_delay}s)")

                await asyncio.sleep(retry_delay)

                # Retry failed stocks with reduced concurrency
                def retry_downloads():
                    retry_results = {}
                    # Use fewer workers for retries to be more conservative
                    retry_workers = min(5, len(failed_stocks))
                    with ThreadPoolExecutor(max_workers=retry_workers) as executor:
                        future_to_symbol = {
                            executor.submit(
                                self._download_single_stock_sync,
                                symbol,
                                start_date,
                                end_date,
                                stocks.index(symbol) + 1,
                                len(stocks),
                                live_data_path
                            ): symbol
                            for symbol in failed_stocks
                        }

                        for future in as_completed(future_to_symbol):
                            symbol = future_to_symbol[future]
                            try:
                                symbol_result, success = future.result()
                                retry_results[symbol] = success
                                time.sleep(RATE_LIMIT_DELAY)
                            except Exception as e:
                                self.log_error(f"Retry error for {symbol}: {e}")
                                retry_results[symbol] = False

                    return retry_results

                retry_results = await asyncio.get_event_loop().run_in_executor(None, retry_downloads)

                # Update results and failed list
                retry_successful = 0
                new_failed_stocks = []
                for symbol, success in retry_results.items():
                    if success:
                        retry_successful += 1
                        successful_downloads += 1
                        failed_downloads -= 1
                    else:
                        new_failed_stocks.append(symbol)

                failed_stocks = new_failed_stocks
                self.log_info(f"Retry {retry_attempt} completed - Additional successes: {retry_successful}, Still failed: {len(failed_stocks)}")

        # Calculate performance metrics
        end_time = time.time()
        total_time = end_time - start_time
        symbols_per_minute = (len(stocks) / total_time) * 60 if total_time > 0 else 0
        success_rate = (successful_downloads / len(stocks)) * 100 if len(stocks) > 0 else 0

        # Final summary
        self.log_info("="*80)
        self.log_info("🎯 OPTIMIZED DOWNLOAD SUMMARY")
        self.log_info("="*80)
        self.log_info(f"📊 Total symbols: {len(stocks)}")
        self.log_info(f"✅ Successful downloads: {successful_downloads}")
        self.log_info(f"❌ Failed downloads: {failed_downloads}")
        self.log_info(f"📈 Success rate: {success_rate:.1f}%")
        self.log_info(f"⏱️  Total time: {total_time:.1f} seconds")
        self.log_info(f"🚀 Speed: {symbols_per_minute:.1f} symbols/minute")
        self.log_info(f"⚡ Avg time per symbol: {total_time/len(stocks):.2f} seconds")
        if failed_stocks:
            self.log_warning(f"🔴 Final failed stocks: {failed_stocks}")
        self.log_info("="*80)

    def _download_single_stock_sync(self, symbol: str, start_date: datetime, end_date: datetime,
                                   stock_index: int, total_stocks: int, live_data_path: Path) -> Tuple[str, bool]:
        """
        Download historical data for a single stock synchronously (for use with ThreadPoolExecutor).
        Returns tuple of (symbol, success_status).
        This method follows the exact pattern from run_clean_trading_system.py
        """
        import asyncio

        self.log_info(f"Downloading 1-min data for {symbol} ({stock_index}/{total_stocks}) from SmartAPI...")

        try:
            # Create a new event loop for this thread to avoid conflicts
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            try:
                # Use the download method
                df_1min_polars = loop.run_until_complete(
                    self.download_historical_data_for_symbol(
                        symbol=symbol,
                        start_date=start_date,
                        end_date=end_date
                    )
                )

                if df_1min_polars is not None and not df_1min_polars.is_empty():
                    # Work directly with Polars DataFrame - no pandas conversion needed
                    # Ensure 'timestamp' column exists
                    if 'timestamp' not in df_1min_polars.columns:
                        self.log_error(f"Downloaded data for {symbol} does not contain 'timestamp' column.")
                        return symbol, False

                    # Sort by timestamp and ensure proper data types
                    df_1min_polars = df_1min_polars.sort('timestamp')

                    # Save 1-min data to data/live using Polars
                    output_file_1min = live_data_path / f"{symbol}_1min.parquet"
                    df_1min_polars.write_parquet(output_file_1min, compression='brotli')
                    self.log_info(f"Saved 1-min data for {symbol} to {output_file_1min} ({len(df_1min_polars)} rows)")

                    # Generate higher timeframe data (3min, 5min, 15min)
                    self._generate_higher_timeframes_sync(symbol, df_1min_polars, live_data_path)

                    return symbol, True
                else:
                    self.log_warning(f"No 1-min data downloaded for {symbol} from SmartAPI.")
                    return symbol, False

            finally:
                loop.close()

        except ValueError as ve:
            # Handle specific token/symbol errors
            if "No instrument found" in str(ve) or "invalid token" in str(ve).lower():
                self.log_warning(f"Token not found for {symbol}: {ve}")
                return symbol, False
            else:
                self.log_error(f"ValueError downloading data for {symbol}: {ve}")
                return symbol, False
        except Exception as e:
            self.log_error(f"Error downloading/saving data for {symbol} from SmartAPI: {e}")
            return symbol, False

    def _generate_higher_timeframes_sync(self, symbol: str, df_1min: pl.DataFrame, live_data_path: Path):
        """Generate 3min, 5min, and 15min data from 1min data (synchronous version)"""
        try:
            timeframes = {
                '3min': 3,
                '5min': 5,
                '15min': 15
            }

            for tf_name, minutes in timeframes.items():
                try:
                    # Group by time intervals and aggregate
                    df_tf = df_1min.with_columns([
                        # Create time groups by truncating to the interval
                        (pl.col('timestamp').dt.truncate(f"{minutes}m")).alias('time_group')
                    ]).group_by('time_group').agg([
                        pl.col('open').first().alias('open'),
                        pl.col('high').max().alias('high'),
                        pl.col('low').min().alias('low'),
                        pl.col('close').last().alias('close'),
                        pl.col('volume').sum().alias('volume')
                    ]).rename({'time_group': 'timestamp'}).sort('timestamp')

                    if not df_tf.is_empty():
                        # Save to file
                        output_file = live_data_path / f"{symbol}_{tf_name}.parquet"
                        df_tf.write_parquet(output_file, compression='brotli')
                        self.log_debug(f"Generated {tf_name} data for {symbol} ({len(df_tf)} rows)")
                    else:
                        self.log_warning(f"No {tf_name} data generated for {symbol}")

                except Exception as e:
                    self.log_error(f"Error generating {tf_name} data for {symbol}: {e}")

        except Exception as e:
            self.log_error(f"Error in higher timeframe generation for {symbol}: {e}")

    async def stop(self):
        self.log_info("Stopping Clean Market Data Agent...")
        self.running = False
        for worker in self.download_workers:
            worker.cancel()
        await asyncio.gather(*self.download_workers, return_exceptions=True)
        self.log_info("Clean Market Data Agent stopped.")