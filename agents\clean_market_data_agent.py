#!/usr/bin/env python3
"""
CLEAN MARKET DATA AGENT
Modern implementation with proper async handling and rate limiting

Features:
- Proper SmartAPI date format handling (YYYY-MM-DD HH:MM)
- Modern timeframes: 1min, 2min, 3min, 5min, 10min, 15min, 30min, 1hr
- Clean async/await patterns
- WebSocket v2 integration
- Comprehensive error handling
- Global rate limiting to prevent API rate limit errors
"""

import asyncio
import logging
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, List, Any, Optional
import polars as pl
import random
import os
import json
from pathlib import Path
import time
import threading
import numpy as np
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

from core.base_agent import BaseAgent
from core.event_system import EventBus, EventTypes
from core.smartapi_client import ModernSmartAPIClient, SmartAPICredentials, HistoricalDataRequest

logger = logging.getLogger(__name__)

class SmartAPIRateLimiter:
    """
    Global SmartAPI rate limiter that handles rate limiting across all async workers.
    Based on the working pattern from run_clean_trading_system.py
    """
    _instance = None
    _lock = threading.Lock()
    _last_api_call = 0
    _min_interval = 1.0  # 1.0 second minimum interval (same as working reference)

    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance

    @classmethod
    async def download_with_rate_limit(cls, smartapi_client, symbol_token: str, exchange: str, 
                                     start_date: datetime, end_date: datetime, interval: str):
        """
        Download historical data with proper global rate limiting.
        This follows the exact pattern from the working reference file.
        """
        # Apply rate limiting exactly like in the reference file
        with cls._lock:
            current_time = time.time()
            time_since_last_call = current_time - cls._last_api_call

            if time_since_last_call < cls._min_interval:
                sleep_time = cls._min_interval - time_since_last_call
                logger.debug(f"[RATE-LIMIT] Sleeping for {sleep_time:.2f}s to respect API limits")
                # Use asyncio.sleep for async context
                await asyncio.sleep(sleep_time)

            cls._last_api_call = time.time()

        # Make the API call outside the lock to allow concurrent processing
        try:
            data = await smartapi_client.get_historical_data_batch(
                symbol_token=symbol_token, exchange=exchange,
                start_date=start_date, end_date=end_date, interval=interval
            )
            return data
        except Exception as e:
            logger.error(f"[SMARTAPI] Error downloading data for token {symbol_token}: {e}")
            return None

class CleanMarketDataAgent(BaseAgent):
    """
    Clean Market Data Agent with modern architecture
    
    Features:
    - Proper date format handling
    - Modern timeframes
    - Clean async patterns
    - WebSocket v2 integration
    - Global rate limiting
    """
    
    def __init__(self, event_bus: EventBus, config: Any, session_id: str):
        super().__init__("CleanMarketDataAgent", event_bus, config, session_id)
        
        # SmartAPI client
        self.smartapi_client = None
        
        # Data storage with training timeframes
        training_timeframes = getattr(config, 'timeframes', ["1min", "3min", "5min", "15min"])
        self.timeframe_data = {tf: {} for tf in training_timeframes}
        
        # Real-time data
        self.realtime_data = {}
        
        # Instrument mapping
        self.instrument_map = {}
        self.symbol_to_token_map = self._load_token_mapping()

        # Configuration - will be updated dynamically by stock selector
        self.config = config
        self.selected_stocks = []  # Start empty, will be updated by stock selector
        
        # Retry configuration for historical data downloads - more conservative approach
        self.retry_config = {
            'max_retries': getattr(config, 'data_download_max_retries', 3),
            'base_delay': getattr(config, 'data_download_base_delay', 3.0),  # Increased from 2.0 to 3.0 seconds
            'max_delay': getattr(config, 'data_download_max_delay', 60.0),   # Increased from 30.0 to 60.0 seconds
            'exponential_base': getattr(config, 'data_download_exponential_base', 2.0),
            'jitter': getattr(config, 'data_download_jitter', True)
        }
        
        # Download workers for concurrent data fetching
        self.data_cache: Dict[str, pl.DataFrame] = {}
        self.download_queue = asyncio.Queue()
        # Reduced from 10 to 5 to match the working implementation's conservative approach
        self.num_download_workers = 5
        self.download_workers: List[asyncio.Task] = []
        
        self.log_info(f"Initialized with {self.num_download_workers} download workers with rate limiting.")

    def _load_token_mapping(self) -> Dict[str, str]:
        try:
            mapping_file_path = Path(__file__).parent.parent / 'token_mapping.json'
            with open(mapping_file_path, 'r') as f:
                data = json.load(f)
                self.log_info(f"Successfully loaded token mapping from {mapping_file_path}")
                return data.get("symbol_to_token", {})
        except Exception as e:
            self.log_error(f"Could not load or parse token_mapping.json: {e}")
            return {}

    def _load_credentials(self) -> Optional[SmartAPICredentials]:
        try:
            api_key, username, password, totp_token = (
                os.getenv('SMARTAPI_API_KEY'), os.getenv('SMARTAPI_USERNAME'),
                os.getenv('SMARTAPI_PASSWORD'), os.getenv('SMARTAPI_TOTP_TOKEN')
            )
            if all((api_key, username, password, totp_token)):
                return SmartAPICredentials(api_key, username, password, totp_token)
        except Exception as e:
            self.log_error(f"Failed to load credentials: {e}")
        return None

    async def initialize(self) -> bool:
        """Initialize the market data agent"""
        try:
            self.log_info("Initializing Clean Market Data Agent...")
            
            # Load credentials
            credentials = self._load_credentials()
            if not credentials:
                self.log_warning("No SmartAPI credentials found, running in demo mode")
                self.initialized = True
                return True
            
            # Initialize SmartAPI client
            self.smartapi_client = ModernSmartAPIClient(credentials)
            
            # Authenticate
            auth_success = await self.smartapi_client.authenticate()
            if not auth_success:
                self.log_error("SmartAPI authentication failed, falling back to demo mode")
                self.smartapi_client = None
            
            # Subscribe to events
            self.event_bus.subscribe("REQUEST_HISTORICAL_DATA", self._handle_data_request)
            self.event_bus.subscribe("STOCK_UNIVERSE_UPDATED", self._handle_universe_update)
            
            self.initialized = True
            self.log_info("Clean Market Data Agent initialized successfully")
            return True
            
        except Exception as e:
            self.log_error(f"Failed to initialize: {e}")
            return False

    async def start(self):
        self.log_info("Starting download workers...")
        self.running = True
        self.download_workers = [
            asyncio.create_task(self._download_worker(i)) for i in range(self.num_download_workers)
        ]

    async def _download_worker(self, worker_id: int):
        self.log_info(f"Download worker {worker_id} started.")
        while self.running:
            try:
                symbol, timeframe, event_to_set = await self.download_queue.get()
                self.log_info(f"Worker {worker_id} processing request for {symbol} ({timeframe})")
                
                data = None
                try:
                    data = await self._fetch_and_process_data(symbol, timeframe)
                except Exception as e:
                    self.log_error(f"Worker {worker_id} failed to process {symbol}: {e}")
                
                await self.event_bus.publish(
                    EventTypes.HISTORICAL_DATA_LOADED,
                    {'symbol': symbol, 'timeframe': timeframe, 'data': data},
                    source=self.name
                )
                if event_to_set:
                    event_to_set.set()
                
                # Add additional delay after each download to further reduce rate limit pressure
                await asyncio.sleep(0.5)
                
                self.download_queue.task_done()
            except asyncio.CancelledError:
                break
        self.log_info(f"Download worker {worker_id} stopped.")

    async def _fetch_and_process_data(self, symbol: str, timeframe: str) -> Optional[pl.DataFrame]:
        cache_key = f"{symbol}_{timeframe}"
        if cache_key in self.data_cache:
            self.log_info(f"Cache hit for {cache_key}.")
            return self.data_cache[cache_key]

        days = 90 if timeframe == '1day' else 30
        interval_map = {'1day': 'ONE_DAY', '1min': 'ONE_MINUTE'}
        api_interval = interval_map.get(timeframe)
        if not api_interval:
            self.log_warning(f"Unsupported timeframe '{timeframe}' for {symbol}")
            return None

        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        try:
            instrument = self._get_instrument_details(symbol)
            if not instrument or instrument.get('demo'):
                raise ValueError("No real token found.")

            df = await self._retry_with_backoff(
                self._download_real_data, symbol, instrument, start_date, end_date, api_interval
            )
            self.data_cache[cache_key] = df
            return df
        except Exception as e:
            self.log_warning(f"All attempts to fetch real data for {symbol} failed: {e}. Falling back to demo data.")
            df = self._create_demo_data(symbol, start_date, end_date, timeframe)
            self.data_cache[cache_key] = df
            return df

    async def _download_real_data(self, symbol: str, instrument: Dict, start_date: datetime, end_date: datetime, interval: str) -> pl.DataFrame:
        if not self.smartapi_client:
            raise ConnectionError("SmartAPI client not authenticated.")

        # Use the rate limiter to prevent API rate limit errors
        data = await SmartAPIRateLimiter.download_with_rate_limit(
            self.smartapi_client, 
            symbol_token=instrument['token'], 
            exchange=instrument['exchange'],
            start_date=start_date, 
            end_date=end_date, 
            interval=interval
        )
        
        if not data:
            raise ValueError(f"API returned no data for {symbol}")

        df = pl.DataFrame(data, schema=[
            ("timestamp", pl.Utf8), ("open", pl.Float64), ("high", pl.Float64),
            ("low", pl.Float64), ("close", pl.Float64), ("volume", pl.Int64)
        ]).with_columns(
            pl.col("timestamp").str.to_datetime("%Y-%m-%dT%H:%M:%S%z")
        ).sort("timestamp")
        
        if df.is_empty() or df.height < 20:
            raise ValueError(f"Insufficient data for {symbol} ({df.height} rows)")
        
        self.log_info(f"Successfully downloaded {df.height} rows for {symbol}")
        return df

    def _create_demo_data(self, symbol: str, start_date: datetime, end_date: datetime, timeframe: str) -> pl.DataFrame:
        self.log_info(f"Creating demo data for {symbol}")
        interval = "1d" if timeframe == '1day' else "1m"
        dates = pl.date_range(start_date, end_date, interval, eager=True).alias("timestamp")
        
        base_price = random.uniform(100, 3000)
        price_noise = np.random.normal(loc=0, scale=base_price * 0.02, size=len(dates))
        close_prices = base_price + np.cumsum(price_noise)
        
        df = pl.DataFrame({
            "timestamp": dates,
            "open": close_prices - np.random.uniform(0, 5, len(dates)),
            "high": close_prices + np.random.uniform(0, 5, len(dates)),
            "low": close_prices - np.random.uniform(0, 5, len(dates)),
            "close": close_prices,
            "volume": np.random.randint(100_000, 5_000_000, size=len(dates))
        })
        return df

    def _get_instrument_details(self, symbol: str) -> Optional[Dict]:
        keys_to_check = [f"{symbol}-EQ_NSE", f"{symbol}_NSE", symbol]
        token = next((self.symbol_to_token_map[key] for key in keys_to_check if key in self.symbol_to_token_map), None)
        
        if token:
            return {'token': token, 'symbol': symbol, 'exchange': 'NSE'}
        return {'token': f"DEMO_{symbol}", 'symbol': symbol, 'exchange': 'NSE', 'demo': True}

    async def _handle_data_request(self, event):
        try:
            symbol = event.data.get('symbol')
            timeframe = event.data.get('timeframe', '1day')
            event_to_set = event.data.get('event_to_set')
            if symbol:
                await self.download_queue.put((symbol, timeframe, event_to_set))
        except Exception as e:
            self.log_error(f"Failed to queue data request: {e}")

    async def _handle_universe_update(self, event):
        self.log_info("Noted updated universe. Data will be fetched on-demand.")

    async def _retry_with_backoff(self, func, *args, **kwargs):
        last_exception = None
        for attempt in range(self.retry_config['max_retries']):
            try:
                return await func(*args, **kwargs)
            except Exception as e:
                last_exception = e
                delay = self.retry_config['base_delay'] * (self.retry_config['exponential_base'] ** attempt)
                if self.retry_config['jitter']:
                    delay *= random.uniform(0.5, 1.5)
                delay = min(delay, self.retry_config['max_delay'])
                self.log_warning(f"Attempt {attempt + 1} for {args[0]} failed. Retrying in {delay:.2f}s...")
                await asyncio.sleep(delay)
        raise last_exception

    async def stop(self):
        self.log_info("Stopping Clean Market Data Agent...")
        self.running = False
        for worker in self.download_workers:
            worker.cancel()
        await asyncio.gather(*self.download_workers, return_exceptions=True)
        self.log_info("Clean Market Data Agent stopped.")