#!/usr/bin/env python3
"""
MODERN SIGNAL GENERATION AGENT
Clean implementation with proper async handling and modern strategies

Features:
- Real-time signal generation from market data
- Multiple strategy support with modern indicators
- Clean async/await implementation
- Event-driven signal publishing
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import numpy as np
import polars as pl
from dataclasses import dataclass

from .base_agent import BaseAgent
from ..utils.event_bus import EventBus, Event

logger = logging.getLogger(__name__)

@dataclass
class TradingSignal:
    """Trading signal data structure"""
    symbol: str
    signal_type: str  # BUY, SELL, HOLD
    strength: float   # 0.0 to 1.0
    entry_price: float
    stop_loss: float
    target_price: float
    strategy_name: str
    timeframe: str
    timestamp: datetime
    confidence: float
    metadata: Dict[str, Any]

class ModernSignalGenerationAgent(BaseAgent):
    """
    Modern Signal Generation Agent with clean architecture
    
    Features:
    - Clean async/await implementation
    - Modern trading strategies
    - Proper error handling
    - Event-driven architecture
    """
    
    def __init__(self, event_bus: EventBus, config: Any, session_id: str):
        super().__init__("ModernSignalGenerationAgent", event_bus, config, session_id)
        
        # Signal generation state
        self.active_signals = {}
        self.signal_history = []
        self.strategy_performance = {}
        
        # Market data cache
        self.market_data_cache = {}
        self.historical_data_cache = {}
        
        # Modern strategy configurations
        self.strategies = {
            'rsi_reversal': {'enabled': True, 'weight': 0.3},
            'moving_average_crossover': {'enabled': True, 'weight': 0.25},
            'bollinger_bands': {'enabled': True, 'weight': 0.2},
            'macd_divergence': {'enabled': True, 'weight': 0.15},
            'volume_breakout': {'enabled': True, 'weight': 0.1}
        }
        
        # Signal generation parameters
        self.min_signal_strength = 0.6
        self.max_signals_per_symbol = 1
        self.signal_cooldown_minutes = 15
    
    async def initialize(self) -> bool:
        """Initialize the signal generation agent"""
        try:
            self.log_info("Initializing Modern Signal Generation Agent...")
            
            # Subscribe to market data events
            self.event_bus.subscribe("MARKET_DATA_UPDATE", self._handle_market_data_update)
            self.event_bus.subscribe("HISTORICAL_DATA_RESPONSE", self._handle_historical_data_response)
            
            # Initialize strategy models
            await self._initialize_strategies()
            
            self.initialized = True
            self.log_info("Modern Signal Generation Agent initialized successfully")
            return True
            
        except Exception as e:
            self.log_error(f"Failed to initialize: {e}")
            return False
    
    async def _initialize_strategies(self):
        """Initialize trading strategies"""
        try:
            self.log_info("Initializing trading strategies...")
            
            # Initialize each strategy
            for strategy_name, config in self.strategies.items():
                if config['enabled']:
                    self.strategy_performance[strategy_name] = {
                        'signals_generated': 0,
                        'successful_signals': 0,
                        'accuracy': 0.0,
                        'last_signal': None
                    }
            
            self.log_info(f"Initialized {len([s for s in self.strategies.values() if s['enabled']])} strategies")
            
        except Exception as e:
            self.log_error(f"Failed to initialize strategies: {e}")
    
    async def start(self):
        """Start the signal generation agent"""
        try:
            self.log_info("Starting Modern Signal Generation Agent...")
            
            self.running = True
            
            # Start signal generation loop
            await self._start_signal_generation_loop()
            
        except Exception as e:
            self.log_error(f"Error starting agent: {e}")
    
    async def _start_signal_generation_loop(self):
        """Start the main signal generation loop"""
        try:
            self.log_info("Starting signal generation loop...")
            
            while self.running:
                try:
                    # Generate signals for all configured stocks
                    for symbol in self.config.selected_stocks:
                        await self._generate_signals_for_symbol(symbol)
                    
                    # Clean up old signals
                    await self._cleanup_old_signals()
                    
                    # Sleep for a short interval
                    await asyncio.sleep(30)  # Generate signals every 30 seconds
                    
                except Exception as e:
                    self.log_error(f"Error in signal generation loop: {e}")
                    await asyncio.sleep(5)
            
            self.log_info("Signal generation loop ended")
            
        except Exception as e:
            self.log_error(f"Failed to start signal generation loop: {e}")
    
    async def _generate_signals_for_symbol(self, symbol: str):
        """Generate signals for a specific symbol"""
        try:
            # Check if we have recent market data
            if symbol not in self.market_data_cache:
                # Request real-time data
                await self._request_market_data(symbol)
                return
            
            # Check signal cooldown
            if self._is_in_cooldown(symbol):
                return
            
            # Get historical data for analysis
            historical_data = await self._get_historical_data(symbol)
            if historical_data is None or len(historical_data) < 50:
                return
            
            # Generate signals using different strategies
            signals = []
            
            for strategy_name, config in self.strategies.items():
                if not config['enabled']:
                    continue
                
                try:
                    signal = await self._apply_strategy(strategy_name, symbol, historical_data)
                    if signal:
                        signals.append(signal)
                except Exception as e:
                    self.log_error(f"Error applying strategy {strategy_name} for {symbol}: {e}")
            
            # Combine signals if multiple strategies agree
            if signals:
                combined_signal = await self._combine_signals(symbol, signals)
                if combined_signal and combined_signal.strength >= self.min_signal_strength:
                    await self._publish_signal(combined_signal)
            
        except Exception as e:
            self.log_error(f"Failed to generate signals for {symbol}: {e}")
    
    async def _apply_strategy(self, strategy_name: str, symbol: str, data: pl.DataFrame) -> Optional[TradingSignal]:
        """Apply a specific trading strategy"""
        try:
            if strategy_name == 'rsi_reversal':
                return await self._rsi_reversal_strategy(symbol, data)
            elif strategy_name == 'moving_average_crossover':
                return await self._ma_crossover_strategy(symbol, data)
            elif strategy_name == 'bollinger_bands':
                return await self._bollinger_bands_strategy(symbol, data)
            elif strategy_name == 'macd_divergence':
                return await self._macd_strategy(symbol, data)
            elif strategy_name == 'volume_breakout':
                return await self._volume_breakout_strategy(symbol, data)
            
            return None
            
        except Exception as e:
            self.log_error(f"Error applying strategy {strategy_name}: {e}")
            return None
    
    async def _rsi_reversal_strategy(self, symbol: str, data: pl.DataFrame) -> Optional[TradingSignal]:
        """RSI reversal strategy"""
        try:
            # Calculate RSI
            data = data.with_columns([
                self._calculate_rsi(pl.col("close"), 14).alias("rsi")
            ])
            
            latest = data.tail(1).to_dicts()[0]
            current_price = latest['close']
            rsi = latest['rsi']
            
            if rsi is None:
                return None
            
            # Generate signals based on RSI levels
            if rsi < 30:  # Oversold - potential buy
                return TradingSignal(
                    symbol=symbol,
                    signal_type="BUY",
                    strength=min((30 - rsi) / 10, 1.0),
                    entry_price=current_price,
                    stop_loss=current_price * 0.98,
                    target_price=current_price * 1.04,
                    strategy_name="rsi_reversal",
                    timeframe="1min",
                    timestamp=datetime.now(),
                    confidence=0.7,
                    metadata={"rsi": rsi}
                )
            elif rsi > 70:  # Overbought - potential sell
                return TradingSignal(
                    symbol=symbol,
                    signal_type="SELL",
                    strength=min((rsi - 70) / 10, 1.0),
                    entry_price=current_price,
                    stop_loss=current_price * 1.02,
                    target_price=current_price * 0.96,
                    strategy_name="rsi_reversal",
                    timeframe="1min",
                    timestamp=datetime.now(),
                    confidence=0.7,
                    metadata={"rsi": rsi}
                )
            
            return None
            
        except Exception as e:
            self.log_error(f"Error in RSI reversal strategy: {e}")
            return None
    
    async def _ma_crossover_strategy(self, symbol: str, data: pl.DataFrame) -> Optional[TradingSignal]:
        """Moving average crossover strategy"""
        try:
            # Calculate moving averages
            data = data.with_columns([
                pl.col("close").rolling_mean(window_size=9).alias("ma_fast"),
                pl.col("close").rolling_mean(window_size=21).alias("ma_slow")
            ])
            
            # Get last few rows to detect crossover
            recent = data.tail(3).to_dicts()
            if len(recent) < 3:
                return None
            
            current = recent[-1]
            previous = recent[-2]
            
            current_price = current['close']
            ma_fast_current = current['ma_fast']
            ma_slow_current = current['ma_slow']
            ma_fast_prev = previous['ma_fast']
            ma_slow_prev = previous['ma_slow']
            
            if None in [ma_fast_current, ma_slow_current, ma_fast_prev, ma_slow_prev]:
                return None
            
            # Detect crossover
            if ma_fast_prev <= ma_slow_prev and ma_fast_current > ma_slow_current:
                # Bullish crossover
                strength = min(abs(ma_fast_current - ma_slow_current) / ma_slow_current * 10, 1.0)
                return TradingSignal(
                    symbol=symbol,
                    signal_type="BUY",
                    strength=strength,
                    entry_price=current_price,
                    stop_loss=current_price * 0.97,
                    target_price=current_price * 1.05,
                    strategy_name="moving_average_crossover",
                    timeframe="1min",
                    timestamp=datetime.now(),
                    confidence=0.6,
                    metadata={"ma_fast": ma_fast_current, "ma_slow": ma_slow_current}
                )
            elif ma_fast_prev >= ma_slow_prev and ma_fast_current < ma_slow_current:
                # Bearish crossover
                strength = min(abs(ma_fast_current - ma_slow_current) / ma_slow_current * 10, 1.0)
                return TradingSignal(
                    symbol=symbol,
                    signal_type="SELL",
                    strength=strength,
                    entry_price=current_price,
                    stop_loss=current_price * 1.03,
                    target_price=current_price * 0.95,
                    strategy_name="moving_average_crossover",
                    timeframe="1min",
                    timestamp=datetime.now(),
                    confidence=0.6,
                    metadata={"ma_fast": ma_fast_current, "ma_slow": ma_slow_current}
                )
            
            return None
            
        except Exception as e:
            self.log_error(f"Error in MA crossover strategy: {e}")
            return None
    
    async def _bollinger_bands_strategy(self, symbol: str, data: pl.DataFrame) -> Optional[TradingSignal]:
        """Bollinger Bands strategy"""
        try:
            # Calculate Bollinger Bands
            data = data.with_columns([
                pl.col("close").rolling_mean(window_size=20).alias("bb_middle"),
                pl.col("close").rolling_std(window_size=20).alias("bb_std")
            ]).with_columns([
                (pl.col("bb_middle") + 2 * pl.col("bb_std")).alias("bb_upper"),
                (pl.col("bb_middle") - 2 * pl.col("bb_std")).alias("bb_lower")
            ])
            
            latest = data.tail(1).to_dicts()[0]
            current_price = latest['close']
            bb_upper = latest['bb_upper']
            bb_lower = latest['bb_lower']
            bb_middle = latest['bb_middle']
            
            if None in [bb_upper, bb_lower, bb_middle]:
                return None
            
            # Generate signals based on band touches
            if current_price <= bb_lower:
                # Price at lower band - potential buy
                strength = min((bb_lower - current_price) / bb_lower * 20, 1.0)
                return TradingSignal(
                    symbol=symbol,
                    signal_type="BUY",
                    strength=strength,
                    entry_price=current_price,
                    stop_loss=current_price * 0.98,
                    target_price=bb_middle,
                    strategy_name="bollinger_bands",
                    timeframe="1min",
                    timestamp=datetime.now(),
                    confidence=0.65,
                    metadata={"bb_upper": bb_upper, "bb_lower": bb_lower, "bb_middle": bb_middle}
                )
            elif current_price >= bb_upper:
                # Price at upper band - potential sell
                strength = min((current_price - bb_upper) / bb_upper * 20, 1.0)
                return TradingSignal(
                    symbol=symbol,
                    signal_type="SELL",
                    strength=strength,
                    entry_price=current_price,
                    stop_loss=current_price * 1.02,
                    target_price=bb_middle,
                    strategy_name="bollinger_bands",
                    timeframe="1min",
                    timestamp=datetime.now(),
                    confidence=0.65,
                    metadata={"bb_upper": bb_upper, "bb_lower": bb_lower, "bb_middle": bb_middle}
                )
            
            return None
            
        except Exception as e:
            self.log_error(f"Error in Bollinger Bands strategy: {e}")
            return None
    
    async def _macd_strategy(self, symbol: str, data: pl.DataFrame) -> Optional[TradingSignal]:
        """MACD strategy"""
        try:
            # Calculate MACD
            data = data.with_columns([
                pl.col("close").ewm_mean(span=12).alias("ema_12"),
                pl.col("close").ewm_mean(span=26).alias("ema_26")
            ]).with_columns([
                (pl.col("ema_12") - pl.col("ema_26")).alias("macd_line")
            ]).with_columns([
                pl.col("macd_line").ewm_mean(span=9).alias("signal_line")
            ]).with_columns([
                (pl.col("macd_line") - pl.col("signal_line")).alias("macd_histogram")
            ])
            
            # Get recent data for crossover detection
            recent = data.tail(3).to_dicts()
            if len(recent) < 3:
                return None
            
            current = recent[-1]
            previous = recent[-2]
            
            current_price = current['close']
            macd_current = current['macd_line']
            signal_current = current['signal_line']
            macd_prev = previous['macd_line']
            signal_prev = previous['signal_line']
            
            if None in [macd_current, signal_current, macd_prev, signal_prev]:
                return None
            
            # Detect MACD crossover
            if macd_prev <= signal_prev and macd_current > signal_current:
                # Bullish crossover
                strength = min(abs(macd_current - signal_current) * 100, 1.0)
                return TradingSignal(
                    symbol=symbol,
                    signal_type="BUY",
                    strength=strength,
                    entry_price=current_price,
                    stop_loss=current_price * 0.97,
                    target_price=current_price * 1.06,
                    strategy_name="macd_divergence",
                    timeframe="1min",
                    timestamp=datetime.now(),
                    confidence=0.7,
                    metadata={"macd": macd_current, "signal": signal_current}
                )
            elif macd_prev >= signal_prev and macd_current < signal_current:
                # Bearish crossover
                strength = min(abs(macd_current - signal_current) * 100, 1.0)
                return TradingSignal(
                    symbol=symbol,
                    signal_type="SELL",
                    strength=strength,
                    entry_price=current_price,
                    stop_loss=current_price * 1.03,
                    target_price=current_price * 0.94,
                    strategy_name="macd_divergence",
                    timeframe="1min",
                    timestamp=datetime.now(),
                    confidence=0.7,
                    metadata={"macd": macd_current, "signal": signal_current}
                )
            
            return None
            
        except Exception as e:
            self.log_error(f"Error in MACD strategy: {e}")
            return None
    
    async def _volume_breakout_strategy(self, symbol: str, data: pl.DataFrame) -> Optional[TradingSignal]:
        """Volume breakout strategy"""
        try:
            # Calculate volume moving average
            data = data.with_columns([
                pl.col("volume").rolling_mean(window_size=20).alias("volume_ma")
            ])
            
            latest = data.tail(1).to_dicts()[0]
            current_price = latest['close']
            current_volume = latest['volume']
            volume_ma = latest['volume_ma']
            
            if volume_ma is None or volume_ma == 0:
                return None
            
            # Check for volume breakout (volume > 2x average)
            volume_ratio = current_volume / volume_ma
            
            if volume_ratio > 2.0:
                # High volume - determine direction from price action
                recent_prices = data.tail(5).select(pl.col("close")).to_series().to_list()
                if len(recent_prices) >= 2:
                    price_change = (recent_prices[-1] - recent_prices[0]) / recent_prices[0]
                    
                    if price_change > 0.01:  # 1% price increase with high volume
                        strength = min(volume_ratio / 5.0, 1.0)
                        return TradingSignal(
                            symbol=symbol,
                            signal_type="BUY",
                            strength=strength,
                            entry_price=current_price,
                            stop_loss=current_price * 0.96,
                            target_price=current_price * 1.08,
                            strategy_name="volume_breakout",
                            timeframe="1min",
                            timestamp=datetime.now(),
                            confidence=0.6,
                            metadata={"volume_ratio": volume_ratio, "price_change": price_change}
                        )
                    elif price_change < -0.01:  # 1% price decrease with high volume
                        strength = min(volume_ratio / 5.0, 1.0)
                        return TradingSignal(
                            symbol=symbol,
                            signal_type="SELL",
                            strength=strength,
                            entry_price=current_price,
                            stop_loss=current_price * 1.04,
                            target_price=current_price * 0.92,
                            strategy_name="volume_breakout",
                            timeframe="1min",
                            timestamp=datetime.now(),
                            confidence=0.6,
                            metadata={"volume_ratio": volume_ratio, "price_change": price_change}
                        )
            
            return None
            
        except Exception as e:
            self.log_error(f"Error in volume breakout strategy: {e}")
            return None
    
    def _calculate_rsi(self, close_col, period: int = 14):
        """Calculate RSI using Polars expressions"""
        try:
            delta = close_col.diff()
            gain = delta.clip_min(0)
            loss = (-delta).clip_min(0)
            
            avg_gain = gain.rolling_mean(window_size=period)
            avg_loss = loss.rolling_mean(window_size=period)
            
            rs = avg_gain / avg_loss
            rsi = 100 - (100 / (1 + rs))
            
            return rsi
            
        except Exception as e:
            self.log_error(f"Error calculating RSI: {e}")
            return pl.lit(None)
    
    async def _combine_signals(self, symbol: str, signals: List[TradingSignal]) -> Optional[TradingSignal]:
        """Combine multiple signals into a single signal"""
        try:
            if not signals:
                return None
            
            # Group signals by type
            buy_signals = [s for s in signals if s.signal_type == "BUY"]
            sell_signals = [s for s in signals if s.signal_type == "SELL"]
            
            # Calculate weighted scores
            buy_score = sum(s.strength * self.strategies[s.strategy_name]['weight'] for s in buy_signals)
            sell_score = sum(s.strength * self.strategies[s.strategy_name]['weight'] for s in sell_signals)
            
            # Determine final signal
            if buy_score > sell_score and buy_score > 0.3:
                # Combine buy signals
                strongest_buy = max(buy_signals, key=lambda s: s.strength)
                return TradingSignal(
                    symbol=symbol,
                    signal_type="BUY",
                    strength=min(buy_score, 1.0),
                    entry_price=strongest_buy.entry_price,
                    stop_loss=strongest_buy.stop_loss,
                    target_price=strongest_buy.target_price,
                    strategy_name="combined",
                    timeframe="1min",
                    timestamp=datetime.now(),
                    confidence=min(sum(s.confidence for s in buy_signals) / len(buy_signals), 1.0),
                    metadata={"contributing_strategies": [s.strategy_name for s in buy_signals]}
                )
            elif sell_score > buy_score and sell_score > 0.3:
                # Combine sell signals
                strongest_sell = max(sell_signals, key=lambda s: s.strength)
                return TradingSignal(
                    symbol=symbol,
                    signal_type="SELL",
                    strength=min(sell_score, 1.0),
                    entry_price=strongest_sell.entry_price,
                    stop_loss=strongest_sell.stop_loss,
                    target_price=strongest_sell.target_price,
                    strategy_name="combined",
                    timeframe="1min",
                    timestamp=datetime.now(),
                    confidence=min(sum(s.confidence for s in sell_signals) / len(sell_signals), 1.0),
                    metadata={"contributing_strategies": [s.strategy_name for s in sell_signals]}
                )
            
            return None
            
        except Exception as e:
            self.log_error(f"Error combining signals: {e}")
            return None
    
    async def _publish_signal(self, signal: TradingSignal):
        """Publish a trading signal"""
        try:
            # Store signal
            self.active_signals[signal.symbol] = signal
            self.signal_history.append(signal)
            
            # Update strategy performance
            if signal.strategy_name in self.strategy_performance:
                self.strategy_performance[signal.strategy_name]['signals_generated'] += 1
                self.strategy_performance[signal.strategy_name]['last_signal'] = datetime.now()
            
            # Publish signal event
            event = Event(
                type="TRADING_SIGNAL_GENERATED",
                data={
                    "signal": signal,
                    "symbol": signal.symbol,
                    "signal_type": signal.signal_type,
                    "strength": signal.strength,
                    "strategy": signal.strategy_name,
                    "timestamp": signal.timestamp
                },
                source=self.name
            )
            self.event_bus.publish(event)
            
            self.log_info(f"Published {signal.signal_type} signal for {signal.symbol} (strength: {signal.strength:.2f})")
            
        except Exception as e:
            self.log_error(f"Failed to publish signal: {e}")
    
    def _is_in_cooldown(self, symbol: str) -> bool:
        """Check if symbol is in signal cooldown period"""
        try:
            if symbol not in self.active_signals:
                return False
            
            last_signal_time = self.active_signals[symbol].timestamp
            cooldown_end = last_signal_time + timedelta(minutes=self.signal_cooldown_minutes)
            
            return datetime.now() < cooldown_end
            
        except Exception as e:
            self.log_error(f"Error checking cooldown for {symbol}: {e}")
            return False
    
    async def _request_market_data(self, symbol: str):
        """Request market data for a symbol"""
        try:
            event = Event(
                type="REQUEST_REALTIME_DATA",
                data={
                    "symbol": symbol,
                    "request_id": f"signal_gen_{symbol}_{datetime.now().timestamp()}"
                },
                source=self.name
            )
            self.event_bus.publish(event)
            
        except Exception as e:
            self.log_error(f"Failed to request market data for {symbol}: {e}")
    
    async def _get_historical_data(self, symbol: str, timeframe: str = "1min") -> Optional[pl.DataFrame]:
        """Get historical data for a symbol"""
        try:
            if symbol in self.historical_data_cache:
                return self.historical_data_cache[symbol]
            
            # Request historical data
            event = Event(
                type="REQUEST_HISTORICAL_DATA",
                data={
                    "symbol": symbol,
                    "timeframe": timeframe,
                    "request_id": f"signal_gen_hist_{symbol}_{datetime.now().timestamp()}"
                },
                source=self.name
            )
            self.event_bus.publish(event)
            
            # Wait a bit for response (in real implementation, this would be handled differently)
            await asyncio.sleep(1)
            
            return self.historical_data_cache.get(symbol)
            
        except Exception as e:
            self.log_error(f"Failed to get historical data for {symbol}: {e}")
            return None
    
    async def _cleanup_old_signals(self):
        """Clean up old signals"""
        try:
            cutoff_time = datetime.now() - timedelta(hours=1)
            
            # Remove old active signals
            expired_symbols = [
                symbol for symbol, signal in self.active_signals.items()
                if signal.timestamp < cutoff_time
            ]
            
            for symbol in expired_symbols:
                del self.active_signals[symbol]
            
            # Trim signal history
            if len(self.signal_history) > 1000:
                self.signal_history = self.signal_history[-1000:]
            
        except Exception as e:
            self.log_error(f"Failed to cleanup old signals: {e}")
    
    async def _handle_market_data_update(self, event: Event):
        """Handle market data update event"""
        try:
            symbol = event.data.get('symbol')
            data_point = event.data.get('data_point')
            
            if symbol and data_point:
                self.market_data_cache[symbol] = data_point
                self.increment_message_count()
            
        except Exception as e:
            self.log_error(f"Failed to handle market data update: {e}")
    
    async def _handle_historical_data_response(self, event: Event):
        """Handle historical data response event"""
        try:
            symbol = event.data.get('symbol')
            data = event.data.get('data')
            
            if symbol and data is not None:
                self.historical_data_cache[symbol] = data
                self.increment_message_count()
            
        except Exception as e:
            self.log_error(f"Failed to handle historical data response: {e}")
    
    async def stop(self):
        """Stop the signal generation agent"""
        try:
            self.log_info("Stopping Modern Signal Generation Agent...")
            
            self.running = False
            
            self.log_info("Modern Signal Generation Agent stopped")
            
        except Exception as e:
            self.log_error(f"Error stopping agent: {e}")