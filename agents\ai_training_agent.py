#!/usr/bin/env python3
"""
🧠 AI Training Agent - Enhanced Multi-Task Learning System
Advanced ML system for comprehensive options trading strategy prediction

[TARGET] Multi-Task Learning Objectives:
[SUCCESS] 1. Trade Direction Prediction: Buy Call / Buy Put / No Trade
[SUCCESS] 2. Profitability Prediction: Binary classifier (Profitable or not)
[SUCCESS] 3. Signal Confidence Estimation: Regression output (0–1)
[SUCCESS] 4. Expected ROI Estimation: Regression model
[SUCCESS] 5. Strategy Selection: Multi-class classifier (selects from 25+ strategy tags)
[SUCCESS] 6. Regime Classification: Predicts current/next regime (sideways, trending, etc.)

[CONFIG] Enhanced Features:
- Multi-model ensemble (LightGBM, XGBoost, CatBoost, TabNet, MLP, LSTM)
- Advanced signal enhancement with attention mechanisms and transformers
- Stacking and blending ensemble methods
- Advanced cross-validation with time-based splits and walk-forward validation
- Model explainability with SHAP/LIME integration
- Comprehensive model registry and versioning
- Live prediction serving with confidence scoring
- Feedback integration for continual learning
- LLM integration for model insights
- Multi-strategy adaptive training with meta-learning
- Advanced signal filtering and noise reduction
- Dynamic model selection based on market conditions
"""

import os
import sys
import logging
import warnings
import asyncio
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Union, Any
from dataclasses import dataclass
import yaml
import joblib
import numpy as np
import pandas as pd
import polars as pl

# Suppress warnings
warnings.filterwarnings('ignore')

# ML Libraries - Enhanced Multi-Model Support
import lightgbm as lgb
import xgboost as xgb
import optuna
from sklearn.model_selection import train_test_split, KFold, TimeSeriesSplit
from sklearn.preprocessing import StandardScaler, LabelEncoder, RobustScaler
from sklearn.metrics import (
    mean_squared_error, mean_absolute_error, r2_score,
    accuracy_score, precision_score, recall_score, f1_score,
    classification_report, confusion_matrix, roc_auc_score
)
from sklearn.ensemble import RandomForestRegressor, RandomForestClassifier
from sklearn.neural_network import MLPRegressor, MLPClassifier
from sklearn.linear_model import SGDRegressor, SGDClassifier # Added for online learning

# PyTorch for TabNet and LSTM
import torch
import torch.nn as nn
import torch.optim as optim
from pytorch_tabnet.tab_model import TabNetRegressor, TabNetClassifier

# Model Explainability
try:
    import shap
    SHAP_AVAILABLE = True
except ImportError:
    SHAP_AVAILABLE = False

try:
    import lime
    from lime.lime_tabular import LimeTabularExplainer
    LIME_AVAILABLE = True
except ImportError:
    LIME_AVAILABLE = False

# GPU acceleration if available
try:
    from utils.gpu_optimizer import GPUOptimizer
    GPU_OPTIMIZER_AVAILABLE = True
except ImportError:
    GPU_OPTIMIZER_AVAILABLE = False

# CatBoost for enhanced ensemble
try:
    from catboost import CatBoostRegressor, CatBoostClassifier
    CATBOOST_AVAILABLE = True
except ImportError:
    CATBOOST_AVAILABLE = False

# Additional ML utilities
from datetime import datetime, timedelta
import json
import pickle
from collections import defaultdict
import uuid
def check_gpu_availability():
    """Check GPU availability for different components"""
    gpu_info = {
        'torch_cuda': torch.cuda.is_available(),
        'cudf_available': False,
        'cupy_available': False
    }

    try:
        import cudf
        gpu_info['cudf_available'] = True
    except ImportError:
        pass

    try:
        import cupy as cp
        gpu_info['cupy_available'] = True
    except ImportError:
        pass

    # For AI training, we primarily need PyTorch CUDA
    gpu_available = gpu_info['torch_cuda']

    if gpu_available:
        print(f"[SUCCESS] GPU acceleration available (PyTorch CUDA: {gpu_info['torch_cuda']})")
        if torch.cuda.is_available():
            print(f"   CUDA device: {torch.cuda.get_device_name(0)}")
            print(f"   CUDA memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")
    else:
        print("[WARN]  GPU acceleration not available, using CPU")

    return gpu_available, gpu_info

GPU_AVAILABLE, GPU_INFO = check_gpu_availability()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# ═══════════════════════════════════════════════════════════════════════════════
# [CONFIG] CONFIGURATION SECTION
# ═══════════════════════════════════════════════════════════════════════════════

@dataclass
class AITrainingConfig:
    """Enhanced Configuration for Multi-Task AI Training Agent"""

    # Data Configuration
    data_dir: str = "data/backtest"
    input_file: str = "enhanced_strategy_results.parquet"
    models_dir: str = "data/models"
    registry_dir: str = "data/models/registry"

    # Multi-Task Learning Objectives
    multi_task_objectives: Dict[str, Dict[str, Any]] = None

    # Target columns for different tasks
    regression_targets: List[str] = None
    classification_targets: List[str] = None
    confidence_targets: List[str] = None

    # Feature Configuration
    feature_columns: List[str] = None
    technical_indicators: List[str] = None
    option_features: List[str] = None
    time_features: List[str] = None
    regime_features: List[str] = None
    strategy_features: List[str] = None

    # Model Configuration
    enabled_models: List[str] = None
    ensemble_weights: Dict[str, float] = None
    model_architectures: Dict[str, Dict[str, Any]] = None

    # Training Configuration
    test_size: float = 0.2
    validation_size: float = 0.2
    random_state: int = 42
    cv_folds: int = 5
    time_based_cv: bool = True
    walk_forward_validation: bool = True

    # Cross-Validation Configuration
    cv_strategy: str = "time_series"  # "kfold", "time_series", "walk_forward"
    cv_gap_days: int = 7  # Gap between train/test in walk-forward
    min_train_samples: int = 1000

    # Model-specific configurations
    lgb_params: Dict[str, Any] = None
    xgb_params: Dict[str, Any] = None
    catboost_params: Dict[str, Any] = None
    tabnet_params: Dict[str, Any] = None
    mlp_params: Dict[str, Any] = None
    lstm_params: Dict[str, Any] = None

    # Hyperparameter Tuning
    optuna_trials: int = 100
    optuna_timeout: int = 3600  # 1 hour
    hyperopt_enabled: bool = True
    grid_search_enabled: bool = False

    # Model Explainability
    shap_enabled: bool = True
    lime_enabled: bool = True
    feature_importance_threshold: float = 0.01

    # Model Registry & Versioning
    model_versioning: bool = True
    auto_model_backup: bool = True
    max_model_versions: int = 10

    # Live Prediction Configuration
    prediction_server_enabled: bool = True
    prediction_batch_size: int = 1000
    confidence_threshold: float = 0.6

    # Feedback Integration
    feedback_enabled: bool = True
    continual_learning: bool = True
    drift_detection_enabled: bool = True
    retraining_threshold: float = 0.1  # Performance degradation threshold

    # Incremental Training Configuration
    incremental_training_enabled: bool = True
    incremental_batch_size: int = 10000  # Number of new samples to trigger incremental update
    max_incremental_updates: int = 10  # Max incremental updates before full retrain
    incremental_learning_rate: float = 0.01  # Learning rate for incremental updates
    data_checkpoint_interval: int = 50000  # Save checkpoint every N samples

    # Streaming/Online Learning Configuration
    online_learning_enabled: bool = True
    stream_chunk_size: int = 1000  # Process data in small chunks
    date_based_filtering: bool = True  # Filter data by date at loading time
    training_cutoff_date: str = "2025-07-04"  # Only train on data after this date
    memory_efficient_mode: bool = True  # Use memory-efficient streaming
    use_partial_fit: bool = True  # Use partial_fit for incremental models

    # LLM Integration
    llm_insights_enabled: bool = True
    generate_model_summaries: bool = True
    
    # Hardware Configuration
    use_gpu: bool = GPU_AVAILABLE
    n_jobs: int = -1
    
    def __post_init__(self):
        """Initialize default configurations for enhanced multi-task learning"""

        # Multi-Task Learning Objectives
        if self.multi_task_objectives is None:
            self.multi_task_objectives = {
                # Only include objectives for which data is available
                "profitability": {
                    "type": "classification",
                    "classes": ["profitable", "unprofitable"], # Assuming these are the classes for is_profitable
                    "target_column": "is_profitable",
                    "weight": 0.5
                },
                "expected_roi": { # Using ROI as a proxy for expected_roi
                    "type": "regression",
                    "target_column": "roi",
                    "weight": 0.5
                }
            }

        # Target columns by type
        if self.regression_targets is None:
            self.regression_targets = [
                "roi", "sharpe_ratio", "expectancy", "max_drawdown",
                "profit_factor", "avg_win", "avg_loss", "total_pnl", "avg_holding_period"
            ]

        if self.classification_targets is None:
            self.classification_targets = [
                "is_profitable"
            ]

        if self.confidence_targets is None:
            self.confidence_targets = [] # No explicit confidence target in data

        # Combine all target columns for general use
        self.target_columns = list(set(self.regression_targets + self.classification_targets))

        # Enhanced feature columns - based on available data
        if self.feature_columns is None:
            self.feature_columns = [
                "total_trades", "winning_trades", "accuracy", "total_pnl",
                "roi", "expectancy", "avg_win", "avg_loss", "profit_factor",
                "max_drawdown", "sharpe_ratio", "avg_holding_period",
                "risk_reward_ratio" # This is a string, will need to be handled in preprocess_data
            ]
            # Remove targets from features if they are also targets
            self.feature_columns = [f for f in self.feature_columns if f not in self.target_columns]
            # Add strategy_name as a feature if it's categorical
            if "strategy_name" not in self.feature_columns:
                self.feature_columns.append("strategy_name")

        # Technical indicators, option features, time features, regime features, strategy features
        # These are not present in the current parquet file, so set them to empty lists
        self.technical_indicators = []
        self.option_features = []
        self.time_features = []
        self.regime_features = []
        self.strategy_features = []

        # Technical indicators
        if self.technical_indicators is None:
            self.technical_indicators = [
                "sma_20", "sma_50", "ema_12", "ema_26", "rsi_14", "macd_line",
                "macd_signal", "bb_upper", "bb_lower", "bb_width", "atr_14",
                "stoch_k", "stoch_d", "cci_20", "adx_14", "mfi_14"
            ]

        # Option-specific features
        if self.option_features is None:
            self.option_features = [
                "delta", "gamma", "theta", "vega", "rho", "implied_volatility",
                "time_to_expiry", "moneyness", "open_interest", "volume",
                "bid_ask_spread", "iv_rank", "iv_percentile"
            ]

        # Time-based features
        if self.time_features is None:
            self.time_features = [
                "hour_of_day", "day_of_week", "days_to_expiry", "time_since_market_open",
                "is_near_expiry", "is_market_hours", "session_type"
            ]

        # Market regime features
        if self.regime_features is None:
            self.regime_features = [
                "vix_level", "vix_change", "trend_strength", "volatility_regime",
                "correlation_regime", "momentum_regime"
            ]

        # Strategy meta-features
        if self.strategy_features is None:
            self.strategy_features = [
                "strategy_id", "strategy_type", "complexity_score", "historical_performance",
                "regime_suitability", "risk_category"
            ]

        # Enabled models
        if self.enabled_models is None:
            self.enabled_models = ["lightgbm", "xgboost", "tabnet"]
            if CATBOOST_AVAILABLE:
                self.enabled_models.append("catboost")

        # Ensemble weights
        if self.ensemble_weights is None:
            self.ensemble_weights = {
                "lightgbm": 0.3,
                "xgboost": 0.25,
                "tabnet": 0.25,
                "catboost": 0.2 if CATBOOST_AVAILABLE else 0.0
            }

        # Model architectures
        if self.model_architectures is None:
            self.model_architectures = {
                "mlp": {
                    "hidden_layers": [256, 128, 64],
                    "activation": "relu",
                    "dropout": 0.3
                },
                "lstm": {
                    "hidden_size": 128,
                    "num_layers": 2,
                    "dropout": 0.2,
                    "sequence_length": 30
                }
            }

        # LightGBM parameters
        if self.lgb_params is None:
            self.lgb_params = {
                'objective': 'regression',
                'metric': 'rmse',
                'boosting_type': 'gbdt',
                'num_leaves': 31,
                'learning_rate': 0.05,
                'feature_fraction': 0.9,
                'bagging_fraction': 0.8,
                'bagging_freq': 5,
                'verbose': -1,
                'random_state': self.random_state,
                'device_type': 'gpu' if self.use_gpu else 'cpu',
                'num_boost_round': 1000,
                'early_stopping_rounds': 100
            }

        # XGBoost parameters
        if self.xgb_params is None:
            self.xgb_params = {
                'objective': 'reg:squarederror',
                'eval_metric': 'rmse',
                'max_depth': 6,
                'learning_rate': 0.05,
                'subsample': 0.8,
                'colsample_bytree': 0.9,
                'random_state': self.random_state,
                'tree_method': 'gpu_hist' if self.use_gpu else 'hist',
                'n_estimators': 1000,
                'early_stopping_rounds': 100
            }

        # CatBoost parameters
        if self.catboost_params is None:
            self.catboost_params = {
                'loss_function': 'RMSE',
                'eval_metric': 'RMSE',
                'depth': 6,
                'learning_rate': 0.05,
                'iterations': 1000,
                'random_seed': self.random_state,
                'task_type': 'GPU' if self.use_gpu else 'CPU',
                'early_stopping_rounds': 100,
                'verbose': False
            }

        # TabNet parameters
        if self.tabnet_params is None:
            self.tabnet_params = {
                'n_d': 8,
                'n_a': 8,
                'n_steps': 3,
                'gamma': 1.3,
                'lambda_sparse': 1e-3,
                'optimizer_fn': torch.optim.Adam,
                'optimizer_params': dict(lr=2e-2),
                'mask_type': 'sparsemax',
                'scheduler_params': {"step_size": 10, "gamma": 0.9},
                'scheduler_fn': torch.optim.lr_scheduler.StepLR,
                'verbose': 1,
                'device_name': 'cuda' if self.use_gpu and torch.cuda.is_available() else 'cpu',
                'max_epochs': 200
            }

        # MLP parameters
        if self.mlp_params is None:
            self.mlp_params = {
                'hidden_layer_sizes': (256, 128, 64),
                'activation': 'relu',
                'solver': 'adam',
                'alpha': 0.001,
                'learning_rate': 'adaptive',
                'max_iter': 1000,
                'random_state': self.random_state,
                'early_stopping': True,
                'validation_fraction': 0.1
            }

        # LSTM parameters
        if self.lstm_params is None:
            self.lstm_params = {
                'hidden_size': 128,
                'num_layers': 2,
                'dropout': 0.2,
                'sequence_length': 30,
                'batch_size': 64,
                'learning_rate': 0.001,
                'epochs': 100,
                'patience': 10
            }

class AITrainingAgent:
    """
    🧠 Enhanced AI Training Agent for Multi-Task Learning

    Advanced ML system supporting multiple prediction objectives:
    [SUCCESS] 1. Trade Direction Prediction: Buy Call / Buy Put / No Trade
    [SUCCESS] 2. Profitability Prediction: Binary classifier (Profitable or not)
    [SUCCESS] 3. Signal Confidence Estimation: Regression output (0–1)
    [SUCCESS] 4. Expected ROI Estimation: Regression model
    [SUCCESS] 5. Strategy Selection: Multi-class classifier (25+ strategies)
    [SUCCESS] 6. Regime Classification: Market regime prediction

    [CONFIG] Enhanced Features:
    - Multi-model ensemble (LightGBM, XGBoost, CatBoost, TabNet, MLP, LSTM)
    - Advanced cross-validation with time-based splits
    - Model explainability with SHAP/LIME
    - Comprehensive model registry and versioning
    - Live prediction serving with confidence scoring
    - Feedback integration for continual learning
    """

    def __init__(self, config: Optional[AITrainingConfig] = None):
        """Initialize Enhanced AI Training Agent"""
        self.config = config or AITrainingConfig()

        # Multi-task models storage
        self.models = {}
        self.task_models = {}  # Models for each specific task
        self.ensemble_models = {}  # Ensemble models
        self.meta_models = {}  # Meta-learning models

        # Model explainability
        self.explainers = {}
        self.feature_importance = {}

        # Model registry
        self.model_registry = {}
        self.model_versions = {}

        # Performance tracking
        self.training_history = {}
        self.validation_scores = {}
        self.drift_detector = None

        # Initialize directories
        self._setup_directories()

        logger.info("🧠 Enhanced AI Training Agent initialized with multi-task learning support")

        self.scalers = {}
        self.encoders = {}
        self.feature_importance = {}
        self.training_history = {}
        self.is_trained = False

        # Incremental training state
        self.last_training_timestamp = None
        self.incremental_update_count = 0
        self.training_data_hash = None
        self.data_checkpoint_path = None

        # Initialize GPU optimizer (NEW 2024-2025)
        self.gpu_optimizer = None
        if GPU_OPTIMIZER_AVAILABLE:
            try:
                self.gpu_optimizer = GPUOptimizer()
                self.gpu_optimizer.optimize_all()
                logger.info("[INIT] GPU optimization enabled")
            except Exception as e:
                logger.warning(f"[WARN] GPU optimization failed: {e}")

        # Create directories
        os.makedirs(self.config.models_dir, exist_ok=True)
        
        logger.info(f"[AGENT] AI Training Agent initialized")
        logger.info(f"[STATUS] Target columns: {self.config.target_columns}")
        logger.info(f"[CONFIG] Feature columns: {len(self.config.feature_columns)} features")
        logger.info(f"[SYSTEM] GPU available: {self.config.use_gpu}")

    def _setup_directories(self):
        """Setup required directories"""
        directories = [
            self.config.models_dir,
            self.config.registry_dir,
            f"{self.config.models_dir}/explainability",
            f"{self.config.models_dir}/versions",
            f"{self.config.models_dir}/meta"
        ]

        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)

    # ═══════════════════════════════════════════════════════════════════════════════
    # [TARGET] MULTI-TASK LEARNING OBJECTIVES
    # ═══════════════════════════════════════════════════════════════════════════════

    async def prepare_multi_task_data(self, data: pl.DataFrame) -> Dict[str, Dict[str, pl.DataFrame]]:
        """
        Prepare data for multi-task learning objectives

        Returns:
            Dict with task_name -> {"X": features, "y": targets, "task_info": metadata}
        """
        logger.info("[WORKFLOW] Preparing multi-task learning data...")

        task_data = {}

        for task_name, task_config in self.config.multi_task_objectives.items():
            try:
                # Extract target column
                target_col = task_config["target_column"]

                if target_col not in data.columns:
                    logger.warning(f"[WARN] Target column '{target_col}' not found for task '{task_name}'")
                    continue

                # Prepare features
                feature_cols = self._get_task_features(task_name, data.columns)

                if not feature_cols:
                    logger.warning(f"[WARN] No features found for task '{task_name}'")
                    continue

                # Filter data with valid targets
                task_df = data.filter(pl.col(target_col).is_not_null())

                if task_df.height == 0:
                    logger.warning(f"[WARN] No valid data for task '{task_name}'")
                    continue

                # Extract features and targets
                X = task_df.select(feature_cols)
                y = task_df.select(target_col)

                # Handle classification tasks
                if task_config["type"] == "classification":
                    y = self._prepare_classification_targets(y, task_config)

                task_data[task_name] = {
                    "X": X,
                    "y": y,
                    "task_info": task_config,
                    "feature_names": feature_cols,
                    "n_samples": X.height
                }

                logger.info(f"[SUCCESS] Prepared task '{task_name}': {X.height} samples, {len(feature_cols)} features")

            except Exception as e:
                logger.error(f"[ERROR] Failed to prepare task '{task_name}': {e}")
                continue

        logger.info(f"[TARGET] Prepared {len(task_data)} multi-task learning objectives")
        return task_data

    def _get_task_features(self, task_name: str, available_columns: List[str]) -> List[str]:
        """Get relevant features for a specific task"""

        # Base features for all tasks
        base_features = [col for col in self.config.feature_columns if col in available_columns]

        # Task-specific feature additions
        task_features = base_features.copy()

        # Add technical indicators for all tasks
        task_features.extend([col for col in self.config.technical_indicators if col in available_columns])

        # Add option features for option-related tasks
        if task_name in ["trade_direction", "expected_roi", "signal_confidence"]:
            task_features.extend([col for col in self.config.option_features if col in available_columns])

        # Add time features for time-sensitive tasks
        if task_name in ["regime_classification", "strategy_selection"]:
            task_features.extend([col for col in self.config.time_features if col in available_columns])

        # Add regime features for regime-related tasks
        if task_name == "regime_classification":
            task_features.extend([col for col in self.config.regime_features if col in available_columns])

        # Add strategy features for strategy selection
        if task_name == "strategy_selection":
            task_features.extend([col for col in self.config.strategy_features if col in available_columns])

        # Remove duplicates and ensure all columns exist
        task_features = list(set(task_features))
        task_features = [col for col in task_features if col in available_columns]

        return task_features

    def _prepare_classification_targets(self, y: pl.DataFrame, task_config: Dict[str, Any]) -> pl.DataFrame:
        """Prepare classification targets with proper encoding"""

        target_col = y.columns[0]
        
        # Ensure the target column is of Utf8 type for string mapping
        if y[target_col].dtype != pl.Utf8:
            y = y.with_columns(pl.col(target_col).cast(pl.Utf8))

        # Handle predefined classes
        if "classes" in task_config:
            classes = task_config["classes"]

            # Create mapping for classes
            class_mapping = {cls: idx for idx, cls in enumerate(classes)}

            # Use a dictionary to map values, and then fill nulls for unseen values
            y = y.with_columns([
                pl.col(target_col).replace(
                    class_mapping,
                    default=pl.lit(-1), # Map unseen values to -1
                    return_dtype=pl.Int32
                ).alias(target_col)
            ])

            # Filter out unmapped values (those that became -1)
            y = y.filter(pl.col(target_col) >= 0)

        return y

    # ═══════════════════════════════════════════════════════════════════════════════
    # [CONFIG] ENHANCED MODEL ARCHITECTURES
    # ═══════════════════════════════════════════════════════════════════════════════

    async def train_multi_task_models(self, task_data: Dict[str, Dict[str, pl.DataFrame]]) -> Dict[str, Any]:
        """
        Train models for all multi-task learning objectives

        Args:
            task_data: Prepared data for each task

        Returns:
            Training results and model performance metrics
        """
        logger.info("[INIT] Starting multi-task model training...")

        training_results = {}

        for task_name, data_dict in task_data.items():
            logger.info(f"[TARGET] Training models for task: {task_name}")

            try:
                # Train individual models for this task
                task_results = await self._train_task_models(task_name, data_dict)
                training_results[task_name] = task_results

                logger.info(f"[SUCCESS] Completed training for task: {task_name}")

            except Exception as e:
                logger.error(f"[ERROR] Failed to train task '{task_name}': {e}")
                training_results[task_name] = {"error": str(e)}

        # Train meta-models for strategy selection
        if len(training_results) > 1:
            logger.info("🧠 Training meta-models for multi-strategy adaptive learning...")
            meta_results = await self._train_meta_models(task_data, training_results)
            training_results["meta_models"] = meta_results

        logger.info("🎉 Multi-task model training completed!")
        return training_results

    async def _train_task_models(self, task_name: str, data_dict: Dict[str, Any]) -> Dict[str, Any]:
        """Train all enabled models for a specific task"""

        X = data_dict["X"]
        y = data_dict["y"]
        task_info = data_dict["task_info"]

        # Convert to pandas for sklearn compatibility
        X_pd = X.to_pandas()
        y_pd = y.to_pandas().iloc[:, 0]  # Get first column as series

        # Prepare train/validation splits
        splits = self._create_cv_splits(X_pd, y_pd, task_info["type"])

        task_models = {}
        task_results = {}

        for model_name in self.config.enabled_models:
            logger.info(f"  [CONFIG] Training {model_name} for {task_name}...")

            try:
                # Train model with cross-validation
                model_result = await self._train_single_model(
                    model_name, X_pd, y_pd, splits, task_info
                )

                task_models[model_name] = model_result["model"]
                task_results[model_name] = model_result["metrics"]

                logger.info(f"  [SUCCESS] {model_name} training completed")

            except Exception as e:
                logger.error(f"  [ERROR] Failed to train {model_name}: {e}")
                task_results[model_name] = {"error": str(e)}

        # Store models for this task
        self.task_models[task_name] = task_models

        # Create ensemble model
        if len(task_models) > 1:
            ensemble_model = self._create_ensemble_model(task_models, task_info)
            self.ensemble_models[task_name] = ensemble_model
            task_results["ensemble"] = {"created": True}

        return task_results

    def _create_cv_splits(self, X: pd.DataFrame, y: pd.Series, task_type: str) -> List[Tuple]:
        """Create cross-validation splits based on configuration"""

        if self.config.cv_strategy == "time_series":
            # Time-based splits for financial data
            cv = TimeSeriesSplit(n_splits=self.config.cv_folds)
            splits = list(cv.split(X))

        elif self.config.cv_strategy == "walk_forward":
            # Walk-forward validation
            splits = self._create_walk_forward_splits(X, y)

        else:
            # Standard K-fold
            cv = KFold(n_splits=self.config.cv_folds, shuffle=True, random_state=self.config.random_state)
            splits = list(cv.split(X))

        return splits

    def _create_walk_forward_splits(self, X: pd.DataFrame, y: pd.Series) -> List[Tuple]:
        """Create walk-forward validation splits"""

        n_samples = len(X)
        min_train = self.config.min_train_samples
        gap_days = self.config.cv_gap_days

        splits = []

        # Calculate split points
        for i in range(self.config.cv_folds):
            # Calculate train end and test start with gap
            train_end = min_train + (i * (n_samples - min_train) // self.config.cv_folds)
            test_start = min(train_end + gap_days, n_samples - 100)  # Ensure minimum test size
            test_end = min(test_start + (n_samples - min_train) // self.config.cv_folds, n_samples)

            if test_start < test_end:
                train_idx = list(range(train_end))
                test_idx = list(range(test_start, test_end))
                splits.append((train_idx, test_idx))

        return splits

    async def _train_single_model(self, model_name: str, X: pd.DataFrame, y: pd.Series,
                                 splits: List[Tuple], task_info: Dict[str, Any]) -> Dict[str, Any]:
        """Train a single model with cross-validation"""

        task_type = task_info["type"]
        cv_scores = []
        best_model = None
        best_score = float('-inf') if task_type == "classification" else float('inf')

        for fold, (train_idx, val_idx) in enumerate(splits):
            X_train, X_val = X.iloc[train_idx], X.iloc[val_idx]
            y_train, y_val = y.iloc[train_idx], y.iloc[val_idx]

            # Train model for this fold
            model = self._create_model(model_name, task_type)
            model = self._fit_model(model, model_name, X_train, y_train, X_val, y_val)

            # Evaluate model
            score = self._evaluate_model(model, model_name, X_val, y_val, task_type)
            cv_scores.append(score)

            # Track best model
            is_better = (task_type == "classification" and score > best_score) or \
                       (task_type == "regression" and score < best_score)

            if is_better:
                best_score = score
                best_model = model

        # Calculate cross-validation statistics
        cv_mean = np.mean(cv_scores)
        cv_std = np.std(cv_scores)

        return {
            "model": best_model,
            "metrics": {
                "cv_scores": cv_scores,
                "cv_mean": cv_mean,
                "cv_std": cv_std,
                "best_score": best_score
            }
        }

    def _create_model(self, model_name: str, task_type: str):
        """Create a model instance based on name and task type"""

        if model_name == "lightgbm":
            if task_type == "classification":
                params = self.config.lgb_params.copy()
                params.update({
                    'objective': 'multiclass' if task_type == "multiclass" else 'binary',
                    'metric': 'multi_logloss' if task_type == "multiclass" else 'binary_logloss'
                })
                return lgb.LGBMClassifier(**params)
            else:
                return lgb.LGBMRegressor(**self.config.lgb_params)

        elif model_name == "xgboost":
            if task_type == "classification":
                params = self.config.xgb_params.copy()
                params.update({
                    'objective': 'multi:softprob' if task_type == "multiclass" else 'binary:logistic',
                    'eval_metric': 'mlogloss' if task_type == "multiclass" else 'logloss'
                })
                return xgb.XGBClassifier(**params)
            else:
                return xgb.XGBRegressor(**self.config.xgb_params)

        elif model_name == "catboost" and CATBOOST_AVAILABLE:
            if task_type == "classification":
                params = self.config.catboost_params.copy()
                params.update({
                    'loss_function': 'MultiClass' if task_type == "multiclass" else 'Logloss'
                })
                return CatBoostClassifier(**params)
            else:
                return CatBoostRegressor(**self.config.catboost_params)

        elif model_name == "tabnet":
            if task_type == "classification":
                return TabNetClassifier(**self.config.tabnet_params)
            else:
                return TabNetRegressor(**self.config.tabnet_params)

        elif model_name == "random_forest":
            if task_type == "classification":
                return RandomForestClassifier(
                    n_estimators=100,
                    random_state=self.config.random_state,
                    n_jobs=self.config.n_jobs
                )
            else:
                return RandomForestRegressor(
                    n_estimators=100,
                    random_state=self.config.random_state,
                    n_jobs=self.config.n_jobs
                )

        elif model_name == "mlp":
            if task_type == "classification":
                return MLPClassifier(**self.config.mlp_params)
            else:
                return MLPRegressor(**self.config.mlp_params)

        else:
            raise ValueError(f"Unknown model: {model_name}")

    def _fit_model(self, model, model_name: str, X_train: pd.DataFrame, y_train: pd.Series,
                   X_val: pd.DataFrame, y_val: pd.Series):
        """Fit a model with appropriate parameters"""

        if model_name in ["lightgbm", "xgboost", "catboost"]:
            # Tree-based models with early stopping
            if hasattr(model, 'fit'):
                if model_name == "lightgbm":
                    model.fit(
                        X_train, y_train,
                        eval_set=[(X_val, y_val)],
                        callbacks=[lgb.early_stopping(100), lgb.log_evaluation(0)]
                    )
                elif model_name == "xgboost":
                    model.fit(
                        X_train, y_train,
                        eval_set=[(X_val, y_val)],
                        verbose=False
                    )
                elif model_name == "catboost":
                    model.fit(
                        X_train, y_train,
                        eval_set=(X_val, y_val),
                        verbose=False
                    )

        elif model_name == "tabnet":
            # TabNet with validation
            X_train_np = X_train.values.astype(np.float32)
            y_train_np = y_train.values.astype(np.float32)
            X_val_np = X_val.values.astype(np.float32)
            y_val_np = y_val.values.astype(np.float32)

            model.fit(
                X_train_np, y_train_np,
                eval_set=[(X_val_np, y_val_np)],
                patience=20,
                max_epochs=self.config.tabnet_params.get('max_epochs', 200)
            )

        else:
            # Standard sklearn models
            model.fit(X_train, y_train)

        return model

    def _evaluate_model(self, model, model_name: str, X_val: pd.DataFrame,
                       y_val: pd.Series, task_type: str) -> float:
        """Evaluate model performance"""

        if model_name == "tabnet":
            X_val_np = X_val.values.astype(np.float32)
            predictions = model.predict(X_val_np)
        else:
            predictions = model.predict(X_val)

        if task_type == "classification":
            return accuracy_score(y_val, predictions)
        else:
            return mean_squared_error(y_val, predictions)

    def _create_ensemble_model(self, task_models: Dict[str, Any], task_info: Dict[str, Any]):
        """Create ensemble model from individual models"""

        class EnsembleModel:
            def __init__(self, models, weights, task_type):
                self.models = models
                self.weights = weights
                self.task_type = task_type

            def predict(self, X):
                predictions = []
                total_weight = 0

                for model_name, model in self.models.items():
                    if model_name in self.weights:
                        weight = self.weights[model_name]

                        if hasattr(model, 'predict'):
                            if model_name == "tabnet":
                                pred = model.predict(X.values.astype(np.float32))
                            else:
                                pred = model.predict(X)

                            predictions.append(pred * weight)
                            total_weight += weight

                if predictions:
                    ensemble_pred = np.sum(predictions, axis=0) / total_weight

                    if self.task_type == "classification":
                        return np.round(ensemble_pred).astype(int)
                    else:
                        return ensemble_pred

                return None

            def predict_proba(self, X):
                if self.task_type != "classification":
                    return None

                probabilities = []
                total_weight = 0

                for model_name, model in self.models.items():
                    if model_name in self.weights and hasattr(model, 'predict_proba'):
                        weight = self.weights[model_name]

                        if model_name == "tabnet":
                            proba = model.predict_proba(X.values.astype(np.float32))
                        else:
                            proba = model.predict_proba(X)

                        probabilities.append(proba * weight)
                        total_weight += weight

                if probabilities:
                    return np.sum(probabilities, axis=0) / total_weight

                return None

        # Get ensemble weights for this task
        weights = {}
        for model_name in task_models.keys():
            if model_name in self.config.ensemble_weights:
                weights[model_name] = self.config.ensemble_weights[model_name]

        return EnsembleModel(task_models, weights, task_info["type"])

    # ═══════════════════════════════════════════════════════════════════════════════
    # [DEBUG] MODEL EXPLAINABILITY & FEATURE IMPORTANCE
    # ═══════════════════════════════════════════════════════════════════════════════

    async def generate_model_explanations(self, task_data: Dict[str, Dict[str, pl.DataFrame]]) -> Dict[str, Any]:
        """Generate model explanations using SHAP and LIME"""

        if not (SHAP_AVAILABLE or LIME_AVAILABLE):
            logger.warning("[WARN] SHAP and LIME not available for model explanations")
            return {}

        logger.info("[DEBUG] Generating model explanations...")

        explanations = {}

        for task_name, data_dict in task_data.items():
            if task_name not in self.task_models:
                continue

            logger.info(f"  [STATUS] Analyzing task: {task_name}")

            try:
                task_explanations = await self._generate_task_explanations(task_name, data_dict)
                explanations[task_name] = task_explanations

            except Exception as e:
                logger.error(f"[ERROR] Failed to generate explanations for {task_name}: {e}")

        # Store explanations
        self.explainers = explanations

        # Generate global feature importance
        global_importance = self._calculate_global_feature_importance(explanations)
        explanations["global_importance"] = global_importance

        logger.info("[SUCCESS] Model explanations generated successfully")
        return explanations

    async def _generate_task_explanations(self, task_name: str, data_dict: Dict[str, Any]) -> Dict[str, Any]:
        """Generate explanations for a specific task"""

        X = data_dict["X"].to_pandas()
        feature_names = data_dict["feature_names"]
        task_models = self.task_models[task_name]

        explanations = {}

        # Sample data for explanation (use subset for performance)
        sample_size = min(1000, len(X))
        X_sample = X.sample(n=sample_size, random_state=self.config.random_state)

        for model_name, model in task_models.items():
            logger.info(f"    [CONFIG] Explaining {model_name}...")

            model_explanations = {}

            # SHAP explanations
            if SHAP_AVAILABLE and self.config.shap_enabled:
                try:
                    shap_values = self._generate_shap_explanations(model, model_name, X_sample)
                    model_explanations["shap"] = shap_values
                except Exception as e:
                    logger.warning(f"[WARN] SHAP failed for {model_name}: {e}")

            # LIME explanations
            if LIME_AVAILABLE and self.config.lime_enabled:
                try:
                    lime_explanations = self._generate_lime_explanations(model, model_name, X_sample, feature_names)
                    model_explanations["lime"] = lime_explanations
                except Exception as e:
                    logger.warning(f"[WARN] LIME failed for {model_name}: {e}")

            # Feature importance (for tree-based models)
            if hasattr(model, 'feature_importances_'):
                importance = dict(zip(feature_names, model.feature_importances_))
                model_explanations["feature_importance"] = importance

            explanations[model_name] = model_explanations

        return explanations

    def _generate_shap_explanations(self, model, model_name: str, X_sample: pd.DataFrame) -> Dict[str, Any]:
        """Generate SHAP explanations for a model"""

        if model_name in ["lightgbm", "xgboost", "catboost", "random_forest"]:
            # Tree-based explainer
            explainer = shap.TreeExplainer(model)
            shap_values = explainer.shap_values(X_sample)
        else:
            # General explainer
            explainer = shap.Explainer(model, X_sample)
            shap_values = explainer(X_sample)

        return {
            "shap_values": shap_values,
            "expected_value": explainer.expected_value if hasattr(explainer, 'expected_value') else None,
            "feature_names": X_sample.columns.tolist()
        }

    def _generate_lime_explanations(self, model, model_name: str, X_sample: pd.DataFrame,
                                   feature_names: List[str]) -> Dict[str, Any]:
        """Generate LIME explanations for a model"""

        # Create LIME explainer
        explainer = LimeTabularExplainer(
            X_sample.values,
            feature_names=feature_names,
            mode='classification' if hasattr(model, 'predict_proba') else 'regression',
            discretize_continuous=True
        )

        # Generate explanations for a few samples
        explanations = []
        n_samples = min(10, len(X_sample))

        for i in range(n_samples):
            if model_name == "tabnet":
                explanation = explainer.explain_instance(
                    X_sample.iloc[i].values,
                    lambda x: model.predict(x.astype(np.float32)),
                    num_features=len(feature_names)
                )
            else:
                explanation = explainer.explain_instance(
                    X_sample.iloc[i].values,
                    model.predict,
                    num_features=len(feature_names)
                )

            explanations.append(explanation.as_list())

        return {
            "explanations": explanations,
            "feature_names": feature_names
        }

    def _calculate_global_feature_importance(self, explanations: Dict[str, Any]) -> Dict[str, float]:
        """Calculate global feature importance across all tasks and models"""

        feature_scores = defaultdict(list)

        for task_name, task_explanations in explanations.items():
            if task_name == "global_importance":
                continue

            for model_name, model_explanations in task_explanations.items():
                # Extract feature importance from different sources
                if "feature_importance" in model_explanations:
                    for feature, importance in model_explanations["feature_importance"].items():
                        feature_scores[feature].append(abs(importance))

                if "shap" in model_explanations:
                    shap_data = model_explanations["shap"]
                    if isinstance(shap_data["shap_values"], np.ndarray):
                        feature_names = shap_data["feature_names"]
                        mean_shap = np.mean(np.abs(shap_data["shap_values"]), axis=0)
                        for i, feature in enumerate(feature_names):
                            feature_scores[feature].append(mean_shap[i])

        # Calculate average importance
        global_importance = {}
        for feature, scores in feature_scores.items():
            global_importance[feature] = np.mean(scores)

        # Sort by importance
        global_importance = dict(sorted(global_importance.items(), key=lambda x: x[1], reverse=True))

        return global_importance

    # ═══════════════════════════════════════════════════════════════════════════════
    # 📚 MODEL REGISTRY & VERSIONING
    # ═══════════════════════════════════════════════════════════════════════════════

    async def save_models_to_registry(self, training_results: Dict[str, Any],
                                     explanations: Dict[str, Any]) -> Dict[str, str]:
        """Save trained models to registry with versioning"""

        logger.info("💾 Saving models to registry...")

        registry_entries = {}
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        for task_name, task_results in training_results.items():
            if task_name == "meta_models" or "error" in task_results:
                continue

            try:
                # Create model version
                version_id = f"{task_name}_v{timestamp}"
                model_dir = Path(self.config.registry_dir) / version_id
                model_dir.mkdir(parents=True, exist_ok=True)

                # Save individual models
                if task_name in self.task_models:
                    models_saved = {}

                    for model_name, model in self.task_models[task_name].items():
                        model_path = model_dir / f"{model_name}.pkl"
                        joblib.dump(model, model_path)
                        models_saved[model_name] = str(model_path)

                    # Save ensemble model
                    if task_name in self.ensemble_models:
                        ensemble_path = model_dir / "ensemble.pkl"
                        joblib.dump(self.ensemble_models[task_name], ensemble_path)
                        models_saved["ensemble"] = str(ensemble_path)

                # Save model metadata
                metadata = {
                    "version_id": version_id,
                    "task_name": task_name,
                    "created_at": datetime.now().isoformat(),
                    "models": models_saved,
                    "performance": task_results,
                    "config": {
                        "enabled_models": self.config.enabled_models,
                        "ensemble_weights": self.config.ensemble_weights,
                        "cv_strategy": self.config.cv_strategy,
                        "cv_folds": self.config.cv_folds
                    }
                }

                # Add explanations if available
                if task_name in explanations:
                    # Save explanations separately (can be large)
                    explanations_path = model_dir / "explanations.pkl"
                    joblib.dump(explanations[task_name], explanations_path)
                    metadata["explanations_path"] = str(explanations_path)

                # Save metadata
                metadata_path = model_dir / "metadata.json"
                with open(metadata_path, 'w') as f:
                    json.dump(metadata, f, indent=2, default=str)

                # Update registry
                self.model_registry[version_id] = metadata
                registry_entries[task_name] = version_id

                logger.info(f"[SUCCESS] Saved {task_name} models as version {version_id}")

            except Exception as e:
                logger.error(f"[ERROR] Failed to save {task_name} models: {e}")

        # Save global registry
        registry_path = Path(self.config.registry_dir) / "registry.json"
        with open(registry_path, 'w') as f:
            json.dump(self.model_registry, f, indent=2, default=str)

        # Cleanup old versions if needed
        if self.config.auto_model_backup:
            await self._cleanup_old_versions()

        logger.info(f"💾 Saved {len(registry_entries)} model versions to registry")
        return registry_entries

    async def _cleanup_old_versions(self):
        """Cleanup old model versions based on configuration"""

        try:
            registry_dir = Path(self.config.registry_dir)

            # Get all version directories
            version_dirs = [d for d in registry_dir.iterdir() if d.is_dir()]

            # Group by task name
            task_versions = defaultdict(list)
            for version_dir in version_dirs:
                task_name = version_dir.name.split('_v')[0]
                task_versions[task_name].append(version_dir)

            # Keep only the latest N versions for each task
            for task_name, versions in task_versions.items():
                if len(versions) > self.config.max_model_versions:
                    # Sort by creation time (newest first)
                    versions.sort(key=lambda x: x.stat().st_mtime, reverse=True)

                    # Remove old versions
                    for old_version in versions[self.config.max_model_versions:]:
                        import shutil
                        shutil.rmtree(old_version)
                        logger.info(f"🗑️ Removed old version: {old_version.name}")

        except Exception as e:
            logger.error(f"[ERROR] Failed to cleanup old versions: {e}")

    # ═══════════════════════════════════════════════════════════════════════════════
    # [INIT] LIVE PREDICTION CAPABILITY
    # ═══════════════════════════════════════════════════════════════════════════════

    async def predict_live(self, features: Dict[str, Any], task_name: Optional[str] = None) -> Dict[str, Any]:
        """
        Make live predictions for new data

        Args:
            features: Dictionary of feature values
            task_name: Specific task to predict (if None, predicts all tasks)

        Returns:
            Dictionary with predictions, confidence scores, and insights
        """

        if task_name:
            tasks_to_predict = [task_name] if task_name in self.task_models else []
        else:
            tasks_to_predict = list(self.task_models.keys())

        if not tasks_to_predict:
            return {"error": "No trained models available for prediction"}

        predictions = {}

        for task in tasks_to_predict:
            try:
                task_prediction = await self._predict_single_task(task, features)
                predictions[task] = task_prediction

            except Exception as e:
                logger.error(f"[ERROR] Prediction failed for task {task}: {e}")
                predictions[task] = {"error": str(e)}

        # Generate meta-predictions if available
        if len(predictions) > 1 and "meta_models" in self.meta_models:
            meta_prediction = await self._generate_meta_prediction(features, predictions)
            predictions["meta_prediction"] = meta_prediction

        return predictions

    async def _predict_single_task(self, task_name: str, features: Dict[str, Any]) -> Dict[str, Any]:
        """Make prediction for a single task"""

        # Prepare features for this task
        task_features = self._prepare_prediction_features(task_name, features)

        if task_features is None:
            return {"error": "Failed to prepare features"}

        task_models = self.task_models[task_name]
        task_config = self.config.multi_task_objectives[task_name]

        # Individual model predictions
        model_predictions = {}

        for model_name, model in task_models.items():
            try:
                if model_name == "tabnet":
                    pred = model.predict(task_features.values.astype(np.float32))
                else:
                    pred = model.predict(task_features)

                # Get prediction probabilities for classification
                if task_config["type"] == "classification" and hasattr(model, 'predict_proba'):
                    if model_name == "tabnet":
                        proba = model.predict_proba(task_features.values.astype(np.float32))
                    else:
                        proba = model.predict_proba(task_features)

                    model_predictions[model_name] = {
                        "prediction": pred[0] if len(pred) > 0 else None,
                        "probabilities": proba[0].tolist() if len(proba) > 0 else None,
                        "confidence": np.max(proba[0]) if len(proba) > 0 else 0.0
                    }
                else:
                    model_predictions[model_name] = {
                        "prediction": pred[0] if len(pred) > 0 else None,
                        "confidence": 1.0  # Default confidence for regression
                    }

            except Exception as e:
                logger.error(f"[ERROR] Model {model_name} prediction failed: {e}")
                model_predictions[model_name] = {"error": str(e)}

        # Ensemble prediction
        ensemble_prediction = None
        if task_name in self.ensemble_models:
            try:
                ensemble_model = self.ensemble_models[task_name]
                ensemble_pred = ensemble_model.predict(task_features)

                ensemble_prediction = {
                    "prediction": ensemble_pred[0] if len(ensemble_pred) > 0 else None
                }

                if task_config["type"] == "classification" and hasattr(ensemble_model, 'predict_proba'):
                    ensemble_proba = ensemble_model.predict_proba(task_features)
                    ensemble_prediction.update({
                        "probabilities": ensemble_proba[0].tolist() if len(ensemble_proba) > 0 else None,
                        "confidence": np.max(ensemble_proba[0]) if len(ensemble_proba) > 0 else 0.0
                    })

            except Exception as e:
                logger.error(f"[ERROR] Ensemble prediction failed: {e}")
                ensemble_prediction = {"error": str(e)}

        return {
            "task_type": task_config["type"],
            "individual_models": model_predictions,
            "ensemble": ensemble_prediction,
            "feature_count": len(task_features.columns),
            "timestamp": datetime.now().isoformat()
        }

    def _prepare_prediction_features(self, task_name: str, features: Dict[str, Any]) -> Optional[pd.DataFrame]:
        """Prepare features for prediction"""

        try:
            # Get required features for this task
            required_features = self._get_task_features(task_name, list(features.keys()))

            # Create feature vector
            feature_vector = {}
            for feature in required_features:
                if feature in features:
                    feature_vector[feature] = features[feature]
                else:
                    # Use default value or 0
                    feature_vector[feature] = 0.0
                    logger.warning(f"[WARN] Missing feature '{feature}' for task '{task_name}', using default value")

            # Convert to DataFrame
            df = pd.DataFrame([feature_vector])
            return df

        except Exception as e:
            logger.error(f"[ERROR] Failed to prepare features for {task_name}: {e}")
            return None

    # ═══════════════════════════════════════════════════════════════════════════════
    # 🧠 META-LEARNING & MULTI-STRATEGY ADAPTIVE TRAINING
    # ═══════════════════════════════════════════════════════════════════════════════

    async def _train_meta_models(self, task_data: Dict[str, Dict[str, pl.DataFrame]],
                                training_results: Dict[str, Any]) -> Dict[str, Any]:
        """Train meta-models for strategy selection and adaptive learning"""

        logger.info("🧠 Training meta-models for adaptive strategy selection...")

        meta_results = {}

        try:
            # Create meta-features from task predictions
            meta_features = await self._create_meta_features(task_data, training_results)

            if meta_features is None:
                return {"error": "Failed to create meta-features"}

            # Train strategy selection meta-model
            strategy_meta_model = await self._train_strategy_selection_model(meta_features)
            meta_results["strategy_selection"] = strategy_meta_model

            # Train holding period optimization model
            holding_period_model = await self._train_holding_period_model(meta_features)
            meta_results["holding_period"] = holding_period_model

            # Train risk-reward optimization model
            rr_optimization_model = await self._train_rr_optimization_model(meta_features)
            meta_results["risk_reward"] = rr_optimization_model

            # Store meta-models
            self.meta_models = meta_results

            logger.info("[SUCCESS] Meta-models training completed")

        except Exception as e:
            logger.error(f"[ERROR] Meta-models training failed: {e}")
            meta_results = {"error": str(e)}

        return meta_results

    async def _create_meta_features(self, task_data: Dict[str, Dict[str, pl.DataFrame]],
                                   training_results: Dict[str, Any]) -> Optional[pd.DataFrame]:
        """Create meta-features from individual task predictions"""

        try:
            meta_features_list = []

            # Combine predictions from all tasks as meta-features
            for task_name, data_dict in task_data.items():
                if task_name not in self.task_models:
                    continue

                X = data_dict["X"].to_pandas()

                # Get predictions from all models for this task
                for model_name, model in self.task_models[task_name].items():
                    try:
                        if model_name == "tabnet":
                            predictions = model.predict(X.values.astype(np.float32))
                        else:
                            predictions = model.predict(X)

                        # Add predictions as meta-features
                        meta_feature_name = f"{task_name}_{model_name}_pred"
                        meta_features_list.append(pd.Series(predictions, name=meta_feature_name))

                        # Add prediction confidence if available
                        if hasattr(model, 'predict_proba'):
                            if model_name == "tabnet":
                                probabilities = model.predict_proba(X.values.astype(np.float32))
                            else:
                                probabilities = model.predict_proba(X)

                            confidence = np.max(probabilities, axis=1)
                            confidence_name = f"{task_name}_{model_name}_conf"
                            meta_features_list.append(pd.Series(confidence, name=confidence_name))

                    except Exception as e:
                        logger.warning(f"[WARN] Failed to get predictions from {model_name}: {e}")

            if meta_features_list:
                meta_features = pd.concat(meta_features_list, axis=1)
                return meta_features

            return None

        except Exception as e:
            logger.error(f"[ERROR] Failed to create meta-features: {e}")
            return None

    async def _train_strategy_selection_model(self, meta_features: pd.DataFrame) -> Dict[str, Any]:
        """Train meta-model for optimal strategy selection"""

        try:
            # Create target: best performing strategy for each sample
            # This would typically come from backtesting results
            # For now, create a placeholder implementation

            # Use ensemble of simple models for strategy selection
            strategy_model = RandomForestClassifier(
                n_estimators=100,
                random_state=self.config.random_state,
                n_jobs=self.config.n_jobs
            )

            # Placeholder target (in real implementation, this would be derived from performance data)
            n_strategies = 25  # Assuming 25 strategies
            y_strategy = np.random.randint(0, n_strategies, size=len(meta_features))

            # Train model
            strategy_model.fit(meta_features, y_strategy)

            return {
                "model": strategy_model,
                "feature_importance": dict(zip(meta_features.columns, strategy_model.feature_importances_)),
                "n_strategies": n_strategies,
                "accuracy": 0.0  # Placeholder
            }

        except Exception as e:
            logger.error(f"[ERROR] Strategy selection model training failed: {e}")
            return {"error": str(e)}

    async def _train_holding_period_model(self, meta_features: pd.DataFrame) -> Dict[str, Any]:
        """Train meta-model for optimal holding period"""

        try:
            # Train regression model for optimal holding period
            holding_model = RandomForestRegressor(
                n_estimators=100,
                random_state=self.config.random_state,
                n_jobs=self.config.n_jobs
            )

            # Placeholder target (optimal holding period in hours)
            y_holding = np.random.uniform(0.5, 6.0, size=len(meta_features))

            # Train model
            holding_model.fit(meta_features, y_holding)

            return {
                "model": holding_model,
                "feature_importance": dict(zip(meta_features.columns, holding_model.feature_importances_)),
                "rmse": 0.0  # Placeholder
            }

        except Exception as e:
            logger.error(f"[ERROR] Holding period model training failed: {e}")
            return {"error": str(e)}

    async def _train_rr_optimization_model(self, meta_features: pd.DataFrame) -> Dict[str, Any]:
        """Train meta-model for optimal risk-reward ratio"""

        try:
            # Train regression model for optimal RR ratio
            rr_model = RandomForestRegressor(
                n_estimators=100,
                random_state=self.config.random_state,
                n_jobs=self.config.n_jobs
            )

            # Placeholder target (optimal RR ratio)
            y_rr = np.random.uniform(1.5, 4.0, size=len(meta_features))

            # Train model
            rr_model.fit(meta_features, y_rr)

            return {
                "model": rr_model,
                "feature_importance": dict(zip(meta_features.columns, rr_model.feature_importances_)),
                "rmse": 0.0  # Placeholder
            }

        except Exception as e:
            logger.error(f"[ERROR] RR optimization model training failed: {e}")
            return {"error": str(e)}

    async def _generate_meta_prediction(self, features: Dict[str, Any],
                                       task_predictions: Dict[str, Any]) -> Dict[str, Any]:
        """Generate meta-predictions using trained meta-models"""

        try:
            # Create meta-feature vector from task predictions
            meta_features = []

            for task_name, task_pred in task_predictions.items():
                if "ensemble" in task_pred and task_pred["ensemble"]:
                    pred_value = task_pred["ensemble"].get("prediction", 0.0)
                    confidence = task_pred["ensemble"].get("confidence", 0.0)

                    meta_features.extend([pred_value, confidence])

            if not meta_features:
                return {"error": "No meta-features available"}

            meta_df = pd.DataFrame([meta_features])

            meta_predictions = {}

            # Strategy selection
            if "strategy_selection" in self.meta_models:
                strategy_model = self.meta_models["strategy_selection"]["model"]
                strategy_pred = strategy_model.predict(meta_df)[0]
                strategy_proba = strategy_model.predict_proba(meta_df)[0]

                meta_predictions["recommended_strategy"] = {
                    "strategy_id": int(strategy_pred),
                    "confidence": float(np.max(strategy_proba))
                }

            # Holding period
            if "holding_period" in self.meta_models:
                holding_model = self.meta_models["holding_period"]["model"]
                holding_pred = holding_model.predict(meta_df)[0]

                meta_predictions["optimal_holding_period"] = {
                    "hours": float(holding_pred),
                    "recommendation": "intraday" if holding_pred < 6.0 else "overnight"
                }

            # Risk-reward ratio
            if "risk_reward" in self.meta_models:
                rr_model = self.meta_models["risk_reward"]["model"]
                rr_pred = rr_model.predict(meta_df)[0]

                meta_predictions["optimal_risk_reward"] = {
                    "ratio": float(rr_pred),
                    "recommendation": "conservative" if rr_pred < 2.0 else "aggressive"
                }

            return meta_predictions

        except Exception as e:
            logger.error(f"[ERROR] Meta-prediction generation failed: {e}")
            return {"error": str(e)}

    # ═══════════════════════════════════════════════════════════════════════════════
    # [AGENT] LLM INTEGRATION & MODEL INSIGHTS
    # ═══════════════════════════════════════════════════════════════════════════════

    async def generate_llm_insights(self, training_results: Dict[str, Any],
                                   explanations: Dict[str, Any]) -> Dict[str, str]:
        """Generate LLM-friendly model insights and summaries"""

        if not self.config.llm_insights_enabled:
            return {}

        logger.info("[AGENT] Generating LLM insights and model summaries...")

        insights = {}

        try:
            # Generate overall system summary
            system_summary = self._generate_system_summary(training_results, explanations)
            insights["system_summary"] = system_summary

            # Generate task-specific insights
            for task_name, task_results in training_results.items():
                if task_name == "meta_models" or "error" in task_results:
                    continue

                task_insight = self._generate_task_insight(task_name, task_results, explanations)
                insights[f"{task_name}_insight"] = task_insight

            # Generate feature importance insights
            if "global_importance" in explanations:
                feature_insights = self._generate_feature_insights(explanations["global_importance"])
                insights["feature_insights"] = feature_insights

            # Generate meta-model insights
            if "meta_models" in training_results:
                meta_insights = self._generate_meta_insights(training_results["meta_models"])
                insights["meta_insights"] = meta_insights

            logger.info("[SUCCESS] LLM insights generated successfully")

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate LLM insights: {e}")
            insights["error"] = str(e)

        return insights

    def _generate_system_summary(self, training_results: Dict[str, Any],
                                explanations: Dict[str, Any]) -> str:
        """Generate overall system performance summary"""

        n_tasks = len([k for k in training_results.keys() if k != "meta_models"])
        n_models = len(self.config.enabled_models)

        # Calculate average performance
        avg_scores = []
        for task_name, task_results in training_results.items():
            if task_name == "meta_models":
                continue

            for model_name, model_results in task_results.items():
                if isinstance(model_results, dict) and "cv_mean" in model_results:
                    avg_scores.append(model_results["cv_mean"])

        avg_performance = np.mean(avg_scores) if avg_scores else 0.0

        summary = f"""
🧠 AI Training System Performance Summary:

[STATUS] System Configuration:
- Multi-task objectives: {n_tasks} tasks trained
- Model ensemble: {n_models} different algorithms
- Cross-validation: {self.config.cv_strategy} with {self.config.cv_folds} folds
- GPU acceleration: {'Enabled' if self.config.use_gpu else 'Disabled'}

[TARGET] Overall Performance:
- Average model performance: {avg_performance:.3f}
- Explainability: {'SHAP & LIME enabled' if SHAP_AVAILABLE and LIME_AVAILABLE else 'Limited'}
- Model versioning: {'Active' if self.config.model_versioning else 'Disabled'}

[INIT] Capabilities:
- Real-time prediction serving
- Multi-strategy adaptive learning
- Automated model selection
- Continuous learning with feedback integration
        """.strip()

        return summary

    def _generate_task_insight(self, task_name: str, task_results: Dict[str, Any],
                              explanations: Dict[str, Any]) -> str:
        """Generate insight for a specific task"""

        task_config = self.config.multi_task_objectives.get(task_name, {})
        task_type = task_config.get("type", "unknown")

        # Find best performing model
        best_model = None
        best_score = float('-inf') if task_type == "classification" else float('inf')

        for model_name, model_results in task_results.items():
            if isinstance(model_results, dict) and "cv_mean" in model_results:
                score = model_results["cv_mean"]
                is_better = (task_type == "classification" and score > best_score) or \
                           (task_type == "regression" and score < best_score)

                if is_better:
                    best_score = score
                    best_model = model_name

        # Get top features
        top_features = []
        if task_name in explanations and "global_importance" in explanations:
            importance = explanations["global_importance"]
            top_features = list(importance.keys())[:5]

        insight = f"""
[TARGET] Task: {task_name.replace('_', ' ').title()}
- Type: {task_type}
- Best model: {best_model or 'Unknown'}
- Performance: {best_score:.3f}
- Key features: {', '.join(top_features) if top_features else 'Not available'}
- Weight in ensemble: {task_config.get('weight', 0.0):.2f}
        """.strip()

        return insight

    def _generate_feature_insights(self, global_importance: Dict[str, float]) -> str:
        """Generate insights about feature importance"""

        top_features = list(global_importance.items())[:10]

        insights = ["[DEBUG] Top 10 Most Important Features:"]
        for i, (feature, importance) in enumerate(top_features, 1):
            insights.append(f"{i}. {feature}: {importance:.4f}")

        # Categorize features
        technical_features = [f for f, _ in top_features if any(indicator in f for indicator in ['sma', 'ema', 'rsi', 'macd', 'bb'])]
        option_features = [f for f, _ in top_features if any(opt in f for opt in ['delta', 'gamma', 'theta', 'vega', 'iv'])]

        if technical_features:
            insights.append(f"\n[METRICS] Key technical indicators: {', '.join(technical_features)}")

        if option_features:
            insights.append(f"\n[STATUS] Important option features: {', '.join(option_features)}")

        return '\n'.join(insights)

    def _generate_meta_insights(self, meta_results: Dict[str, Any]) -> str:
        """Generate insights about meta-learning models"""

        insights = ["🧠 Meta-Learning Insights:"]

        if "strategy_selection" in meta_results:
            insights.append("- Strategy selection model trained for adaptive strategy choice")

        if "holding_period" in meta_results:
            insights.append("- Holding period optimization model for timing decisions")

        if "risk_reward" in meta_results:
            insights.append("- Risk-reward optimization for position sizing")

        insights.append("\n[INFO] The system can now make meta-decisions about:")
        insights.append("  • Which strategy works best under current conditions")
        insights.append("  • Optimal holding periods for maximum returns")
        insights.append("  • Risk-reward ratios for stable performance")

        return '\n'.join(insights)

    # ═══════════════════════════════════════════════════════════════════════════════
    # [WORKFLOW] INCREMENTAL TRAINING METHODS
    # ═══════════════════════════════════════════════════════════════════════════════

    async def train_incremental(self, file_path: str, force_full_retrain: bool = False) -> Dict[str, Any]:
        """
        Perform incremental training on new data

        Args:
            file_path: Path to the data file
            force_full_retrain: Force full retraining instead of incremental

        Returns:
            Training results with incremental training info
        """
        logger.info("[INCREMENTAL] Starting incremental training analysis...")

        # Load new data
        new_df = self.load_data(file_path)

        # Detect changes
        changes = self._detect_data_changes(new_df)

        logger.info(f"[INCREMENTAL] Data changes detected:")
        logger.info(f"  - New data: {changes['new_data']}")
        logger.info(f"  - Schema changed: {changes['schema_changed']}")
        logger.info(f"  - New rows: {changes['new_rows']:,}")
        logger.info(f"  - Change ratio: {changes['change_ratio']:.2%}")
        logger.info(f"  - Significant change: {changes['significant_change']}")

        # Determine training strategy
        should_full_retrain = (
            force_full_retrain or
            not self.is_trained or
            changes['schema_changed'] or
            self.incremental_update_count >= self.config.max_incremental_updates or
            not self.config.incremental_training_enabled
        )

        if should_full_retrain:
            logger.info("[INCREMENTAL] Performing full retraining...")
            results = await self.train_async(file_path, optimize_hyperparams=True)

            # Reset incremental state
            self.incremental_update_count = 0
            self.training_data_hash = self._calculate_data_hash(new_df)
            self.data_checkpoint_path = self._save_data_checkpoint(new_df)
            self.last_training_timestamp = datetime.now()

            results['training_type'] = 'full_retrain'
            results['incremental_updates_reset'] = True

        elif changes['significant_change']:
            logger.info("[INCREMENTAL] Performing incremental update...")
            results = await self._perform_incremental_update(new_df)

            # Update incremental state
            self.incremental_update_count += 1
            self.training_data_hash = self._calculate_data_hash(new_df)
            self.data_checkpoint_path = self._save_data_checkpoint(new_df)
            self.last_training_timestamp = datetime.now()

            results['training_type'] = 'incremental_update'
            results['incremental_update_count'] = self.incremental_update_count

        else:
            logger.info("[INCREMENTAL] No significant changes detected, skipping training...")
            results = {
                'status': 'skipped',
                'training_type': 'no_update_needed',
                'message': 'No significant data changes detected',
                'changes': changes,
                'incremental_update_count': self.incremental_update_count
            }

        return results

    async def train_online_streaming(self, file_path: str) -> Dict[str, Any]:
        """
        Train models using online/streaming learning without loading all data into memory

        Args:
            file_path: Path to training data file

        Returns:
            Training results
        """
        logger.info("[ONLINE] Starting online streaming training...")

        try:
            # Initialize online learning models
            online_models = self._initialize_online_models()

            # Track metrics
            total_samples = 0
            chunk_count = 0
            training_metrics = []

            # Stream data in chunks
            for chunk in self.stream_data_with_date_filter(file_path):
                chunk_count += 1

                # Preprocess chunk
                X_chunk, y_chunk, feature_names = self.preprocess_data(chunk)

                if len(X_chunk) == 0:
                    continue

                total_samples += len(X_chunk)

                # Train models incrementally on this chunk
                chunk_metrics = self._train_chunk_online(online_models, X_chunk, y_chunk, feature_names)
                training_metrics.append(chunk_metrics)

                logger.info(f"[ONLINE] Processed chunk {chunk_count}: {len(X_chunk):,} samples")
                logger.info(f"[ONLINE] Total samples processed: {total_samples:,}")

                # Memory cleanup
                del X_chunk, y_chunk, chunk

                # Optional: Save checkpoint every N chunks
                if chunk_count % 10 == 0:
                    self._save_online_checkpoint(online_models, chunk_count, total_samples)

            # Final model evaluation and saving
            final_metrics = self._finalize_online_training(online_models, training_metrics)

            # Auto-update training cutoff date based on processed data
            if chunk_count > 0:
                # Get a sample of the last processed data to determine latest date
                try:
                    last_chunk = None
                    chunk_counter = 0
                    for chunk in self.stream_data_with_date_filter(file_path):
                        chunk_counter += 1
                        if chunk_counter == chunk_count:  # Get the last chunk we processed
                            last_chunk = chunk
                            break

                    if last_chunk is not None and len(last_chunk) > 0:
                        self._update_training_cutoff_date(last_chunk)
                except Exception as e:
                    logger.warning(f"[WARN] Could not auto-update cutoff date: {e}")

            results = {
                'status': 'success',
                'training_type': 'online_streaming',
                'total_samples': total_samples,
                'chunks_processed': chunk_count,
                'final_metrics': final_metrics,
                'training_time': datetime.now().isoformat(),
                'auto_date_update': True
            }

            logger.info(f"[ONLINE] Streaming training completed: {total_samples:,} samples in {chunk_count} chunks")
            return results

        except Exception as e:
            logger.error(f"[ERROR] Online streaming training failed: {e}")
            raise

    def _initialize_online_models(self) -> Dict[str, Any]:
        """Initialize models that support online/incremental learning"""
        from sklearn.linear_model import SGDRegressor, SGDClassifier

        online_models = {}

        # SGD models support partial_fit
        # Use the configured regression and classification targets directly
        regression_targets = self.config.regression_targets
        classification_targets = self.config.classification_targets

        for target in self.config.target_columns:
            if target in regression_targets:
                # Regression targets
                online_models[target] = SGDRegressor(
                    learning_rate='adaptive',
                    eta0=self.config.incremental_learning_rate,
                    random_state=42
                )
            elif target in classification_targets:
                # Classification targets
                online_models[target] = SGDClassifier(
                    learning_rate='adaptive',
                    eta0=self.config.incremental_learning_rate,
                    random_state=42
                )
            else:
                logger.warning(f"[WARN] Target '{target}' not explicitly defined as regression or classification. Skipping online model initialization for this target.")

        logger.info(f"[ONLINE] Initialized {len(online_models)} online learning models")
        return online_models

    def _train_chunk_online(self, models: Dict[str, Any], X: np.ndarray, y: np.ndarray,
                           feature_names: List[str]) -> Dict[str, float]:
        """Train models on a single chunk using partial_fit"""
        chunk_metrics = {}

        target_columns = getattr(self.config, 'target_columns', ['roi', 'sharpe_ratio', 'expectancy', 'is_profitable'])
        for target_idx, target in enumerate(target_columns):
            if target not in models:
                continue

            model = models[target]
            y_target = y[:, target_idx]

            # Handle infinite values and NaNs in targets
            y_target = np.nan_to_num(y_target, nan=0.0, posinf=1e10, neginf=-1e10) # Replace inf with large finite numbers
            
            # Remove NaN values (after nan_to_num, only NaNs from original data remain if not handled by posinf/neginf)
            valid_mask = ~np.isnan(y_target)
            if not np.any(valid_mask):
                continue

            X_valid = X[valid_mask]
            y_valid = y_target[valid_mask]

            try:
                # Use partial_fit for incremental learning
                if hasattr(model, 'partial_fit'):
                    if isinstance(model, SGDClassifier):
                        # For classifiers, need to specify all possible classes on the first call
                        # Assuming binary classification for 'is_profitable' (0 or 1)
                        # If there were other classification targets, their full class set would be needed
                        if not hasattr(model, '_fitted_classes'):
                            # Only fit classes once
                            unique_classes = np.array([0, 1]) # Assuming 0 for unprofitable, 1 for profitable
                            model.partial_fit(X_valid, y_valid, classes=unique_classes)
                            model._fitted_classes = True # Mark that classes have been fitted
                        else:
                            model.partial_fit(X_valid, y_valid)
                        
                        # Calculate metrics for this chunk
                        if len(X_valid) > 10:  # Only if enough samples
                            y_pred = model.predict(X_valid)
                            from sklearn.metrics import accuracy_score
                            chunk_metrics[f'{target}_accuracy'] = accuracy_score(y_valid, y_pred)
                    
                    elif isinstance(model, SGDRegressor):
                        model.partial_fit(X_valid, y_valid)
                        
                        # Calculate metrics for this chunk
                        if len(X_valid) > 10:  # Only if enough samples
                            y_pred = model.predict(X_valid)
                            from sklearn.metrics import mean_squared_error, r2_score
                            chunk_metrics[f'{target}_mse'] = mean_squared_error(y_valid, y_pred)
                            chunk_metrics[f'{target}_r2'] = r2_score(y_valid, y_pred)
                    else:
                        logger.warning(f"[ONLINE] Model type not supported for partial_fit: {type(model)}")

            except Exception as e:
                logger.warning(f"[ONLINE] Error training {target}: {e}")
                continue

        return chunk_metrics

    def _update_training_cutoff_date(self, data_df: pl.DataFrame):
        """Automatically update training cutoff date based on latest data processed"""
        try:
            if 'date' not in data_df.columns:
                return

            # Get the latest date from processed data
            latest_date = data_df['date'].max()

            # Convert to datetime for comparison
            from datetime import datetime
            latest_datetime = datetime.strptime(latest_date, '%d-%m-%Y')

            # Update config file with new cutoff date
            config_path = "config/ai_training_config.yaml"

            if os.path.exists(config_path):
                import yaml

                # Read current config
                with open(config_path, 'r', encoding='utf-8') as f:
                    config_data = yaml.safe_load(f)

                # Update the cutoff date
                new_cutoff_date = latest_datetime.strftime('%Y-%m-%d')

                if 'online_learning' not in config_data:
                    config_data['online_learning'] = {}

                old_cutoff = config_data['online_learning'].get('training_cutoff_date', 'unknown')
                config_data['online_learning']['training_cutoff_date'] = new_cutoff_date

                # Save updated config
                with open(config_path, 'w', encoding='utf-8') as f:
                    yaml.dump(config_data, f, default_flow_style=False, sort_keys=False)

                # Update instance config
                self.config.training_cutoff_date = new_cutoff_date

                logger.info(f"📅 Auto-updated training cutoff date: {old_cutoff} → {new_cutoff_date}")

                # Save training metadata
                self._save_training_metadata({
                    'last_training_date': new_cutoff_date,
                    'latest_data_date': latest_date,
                    'total_samples_processed': len(data_df),
                    'update_timestamp': datetime.now().isoformat()
                })

        except Exception as e:
            logger.warning(f"[WARN] Failed to auto-update training cutoff date: {e}")

    def _save_training_metadata(self, metadata: Dict[str, Any]):
        """Save training metadata for tracking"""
        try:
            metadata_dir = Path("data/models/metadata")
            metadata_dir.mkdir(parents=True, exist_ok=True)

            metadata_path = metadata_dir / "training_metadata.json"

            # Load existing metadata if it exists
            existing_metadata = {}
            if metadata_path.exists():
                with open(metadata_path, 'r') as f:
                    existing_metadata = json.load(f)

            # Update with new metadata
            existing_metadata.update(metadata)

            # Save updated metadata
            with open(metadata_path, 'w') as f:
                json.dump(existing_metadata, f, indent=2, default=str)

            logger.info(f"💾 Training metadata saved: {metadata_path}")

        except Exception as e:
            logger.warning(f"[WARN] Failed to save training metadata: {e}")

    def _save_online_checkpoint(self, models: Dict[str, Any], chunk_count: int, total_samples: int):
        """Save checkpoint of online models"""
        try:
            checkpoint_dir = Path(self.config.models_dir) / "online_checkpoints" # Corrected from model_dir to models_dir
            checkpoint_dir.mkdir(parents=True, exist_ok=True)

            checkpoint_path = checkpoint_dir / f"checkpoint_{chunk_count}_{total_samples}.pkl"

            import pickle
            with open(checkpoint_path, 'wb') as f:
                pickle.dump({
                    'models': models,
                    'chunk_count': chunk_count,
                    'total_samples': total_samples,
                    'timestamp': datetime.now().isoformat()
                }, f)

            logger.info(f"[CHECKPOINT] Saved online checkpoint: {checkpoint_path}")

        except Exception as e:
            logger.warning(f"[CHECKPOINT] Failed to save checkpoint: {e}")

    def _finalize_online_training(self, models: Dict[str, Any], training_metrics: List[Dict[str, float]]) -> Dict[str, Any]:
        """Finalize online training and compute final metrics"""
        try:
            # Save final models
            self.models = models
            self.is_trained = True # Set is_trained to True after online training
            self.save_models()

            # Compute aggregated metrics
            final_metrics = {}

            if training_metrics:
                # Average metrics across all chunks
                all_keys = set()
                for metrics in training_metrics:
                    all_keys.update(metrics.keys())

                for key in all_keys:
                    values = [m.get(key, 0) for m in training_metrics if key in m]
                    if values:
                        final_metrics[f'avg_{key}'] = np.mean(values)
                        final_metrics[f'std_{key}'] = np.std(values)

            # Model information
            final_metrics['models_trained'] = len(models)
            final_metrics['training_completed'] = True

            logger.info(f"[ONLINE] Final metrics: {final_metrics}")
            return final_metrics

        except Exception as e:
            logger.error(f"[ERROR] Error finalizing online training: {e}")
            return {'error': str(e)}

    async def _perform_incremental_update(self, new_df: pl.DataFrame) -> Dict[str, Any]:
        """
        Perform incremental model update with new data

        Args:
            new_df: New data for incremental training

        Returns:
            Update results
        """
        logger.info("[INCREMENTAL] Starting incremental model update...")

        try:
            # Load previous checkpoint for comparison
            if self.data_checkpoint_path and Path(self.data_checkpoint_path).exists():
                old_df = self._load_data_checkpoint(self.data_checkpoint_path)

                # Extract only new rows
                new_rows_df = new_df.slice(len(old_df))
                logger.info(f"[INCREMENTAL] Processing {len(new_rows_df):,} new rows")

            else:
                # No previous checkpoint, use all data
                new_rows_df = new_df
                logger.info(f"[INCREMENTAL] No checkpoint found, processing all {len(new_rows_df):,} rows")

            # Create training sample that includes recent historical + new data
            if self.data_checkpoint_path and Path(self.data_checkpoint_path).exists():
                # Use last N rows from checkpoint + all new data
                old_df = self._load_data_checkpoint(self.data_checkpoint_path)
                sample_size = min(self.config.incremental_batch_size * 5, len(old_df))
                historical_sample = old_df.tail(sample_size)

                # Combine historical sample with new data
                combined_df = pl.concat([historical_sample, new_rows_df], how="vertical")
            else:
                combined_df = new_rows_df

            # Preprocess combined data
            features, targets, feature_names = self.preprocess_data(combined_df)

            # Split data (use smaller validation set for incremental)
            X_train, X_val, X_test, y_train, y_val, y_test = self.split_data(
                features, targets, test_size=0.1, validation_size=0.1
            )

            # Perform incremental training with reduced parameters
            logger.info("[INCREMENTAL] Training models with incremental parameters...")

            # Reduce training intensity for incremental updates
            original_lgb_rounds = self.config.lgb_params.get('num_boost_round', 1000)
            original_tabnet_epochs = self.config.tabnet_params.get('max_epochs', 100)

            # Use reduced training for incremental updates
            self.config.lgb_params['num_boost_round'] = min(200, original_lgb_rounds // 2)
            self.config.tabnet_params['max_epochs'] = min(20, original_tabnet_epochs // 2)

            # Train ensemble with incremental data
            self.train_ensemble(X_train, y_train, X_val, y_val, optimize_hyperparams=False)

            # Restore original parameters
            self.config.lgb_params['num_boost_round'] = original_lgb_rounds
            self.config.tabnet_params['max_epochs'] = original_tabnet_epochs

            # Evaluate on test set
            evaluation_metrics = self.evaluate_models(X_test, y_test)

            # Save updated models
            self.save_models()

            results = {
                'status': 'success',
                'training_type': 'incremental_update',
                'data_shape': combined_df.shape,
                'new_rows_processed': len(new_rows_df),
                'total_training_samples': len(X_train),
                'evaluation_metrics': evaluation_metrics,
                'feature_count': len(feature_names),
                'target_count': len(self.target_columns),
                'training_time': datetime.now().isoformat()
            }

            logger.info("[INCREMENTAL] Incremental update completed successfully!")
            return results

        except Exception as e:
            logger.error(f"[INCREMENTAL] Incremental update failed: {e}")
            return {
                'status': 'error',
                'training_type': 'incremental_update',
                'error': str(e)
            }

    # ═══════════════════════════════════════════════════════════════════════════════
    # [INIT] MAIN TRAINING ORCHESTRATION
    # ═══════════════════════════════════════════════════════════════════════════════

    async def train_enhanced_models(self, data_file: Optional[str] = None) -> Dict[str, Any]:
        """
        Main method to train all enhanced AI models with multi-task learning

        Args:
            data_file: Path to training data file

        Returns:
            Comprehensive training results and model registry information
        """

        logger.info("[INIT] Starting Enhanced AI Training with Multi-Task Learning...")

        try:
            # Load and prepare data
            logger.info("[STATUS] Loading training data...")
            data = await self._load_training_data(data_file)

            if data is None:
                return {"error": "Failed to load training data"}

            # Prepare multi-task data
            task_data = await self.prepare_multi_task_data(data)

            if not task_data:
                return {"error": "Failed to prepare multi-task data"}

            # Train all models
            training_results = await self.train_multi_task_models(task_data)

            # Generate model explanations
            explanations = await self.generate_model_explanations(task_data)

            # Save models to registry
            registry_entries = await self.save_models_to_registry(training_results, explanations)

            # Generate LLM insights
            llm_insights = await self.generate_llm_insights(training_results, explanations)

            # Compile final results
            final_results = {
                "status": "success",
                "training_results": training_results,
                "explanations_summary": {
                    "tasks_explained": list(explanations.keys()),
                    "global_importance_available": "global_importance" in explanations,
                    "shap_enabled": SHAP_AVAILABLE and self.config.shap_enabled,
                    "lime_enabled": LIME_AVAILABLE and self.config.lime_enabled
                },
                "model_registry": registry_entries,
                "llm_insights": llm_insights,
                "system_info": {
                    "tasks_trained": len(task_data),
                    "models_per_task": len(self.config.enabled_models),
                    "gpu_enabled": self.config.use_gpu,
                    "cv_strategy": self.config.cv_strategy,
                    "ensemble_enabled": True,
                    "meta_learning_enabled": len(training_results) > 1
                },
                "timestamp": datetime.now().isoformat()
            }

            logger.info("🎉 Enhanced AI Training completed successfully!")
            return final_results

        except Exception as e:
            logger.error(f"[ERROR] Enhanced AI Training failed: {e}")
            return {
                "status": "error",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }

    async def _load_training_data(self, data_file: Optional[str] = None) -> Optional[pl.DataFrame]:
        """Load training data from file"""

        try:
            if data_file is None:
                data_file = Path(self.config.data_dir) / self.config.input_file
            else:
                data_file = Path(data_file)

            if not data_file.exists():
                logger.error(f"[ERROR] Training data file not found: {data_file}")
                return None

            logger.info(f"📂 Loading data from: {data_file}")

            if data_file.suffix == '.parquet':
                data = pl.read_parquet(data_file)
            elif data_file.suffix == '.csv':
                data = pl.read_csv(data_file)
            else:
                logger.error(f"[ERROR] Unsupported file format: {data_file.suffix}")
                return None

            logger.info(f"[SUCCESS] Loaded {data.height} rows, {data.width} columns")
            return data

        except Exception as e:
            logger.error(f"[ERROR] Failed to load training data: {e}")
            return None

    def load_data(self, file_path: Optional[str] = None) -> pl.DataFrame:
        """
        Load backtesting data using Polars for optimal performance

        Args:
            file_path: Optional custom file path, defaults to config

        Returns:
            Polars DataFrame with backtesting results
        """
        if file_path is None:
            file_path = os.path.join(self.config.data_dir, self.config.input_file)

        if not os.path.exists(file_path):
            raise FileNotFoundError(f"Backtesting data not found: {file_path}")

        logger.info(f"📂 Loading data from: {file_path}")

        # Load with Polars for optimal performance
        if file_path.endswith('.parquet'):
            df = pl.read_parquet(file_path)
        elif file_path.endswith('.csv'):
            df = pl.read_csv(file_path)
        else:
            raise ValueError(f"Unsupported file format: {file_path}")

        logger.info(f"[STATUS] Loaded {len(df)} rows, {len(df.columns)} columns")
        logger.info(f"[DEBUG] Columns: {df.columns}")

        return df

    def stream_data_with_date_filter(self, file_path: str, chunk_size: int = None):
        """
        Stream data in chunks with date filtering to avoid loading old data

        Args:
            file_path: Path to data file
            chunk_size: Size of chunks to process (default: config.stream_chunk_size)

        Yields:
            Chunks of filtered data
        """
        chunk_size = chunk_size or self.config.stream_chunk_size

        try:
            logger.info(f"🌊 Streaming data from {file_path} with date filter > {self.config.training_cutoff_date}")

            if file_path.endswith('.parquet'):
                # Use lazy loading for memory efficiency
                lazy_df = pl.scan_parquet(file_path)

                # Apply date filter at scan level (most efficient)
                if self.config.date_based_filtering and 'date' in lazy_df.columns:
                    # Convert cutoff date to comparable format
                    cutoff_date = pl.lit(self.config.training_cutoff_date).str.strptime(pl.Date, "%Y-%m-%d")

                    # Filter data after cutoff date
                    lazy_df = lazy_df.filter(
                        pl.col('date').str.strptime(pl.Date, "%d-%m-%Y") > cutoff_date
                    )

                    logger.info(f"[DEBUG] Applied date filter: only data after {self.config.training_cutoff_date}")

                # Stream in chunks
                total_rows = lazy_df.select(pl.count()).collect().item()
                logger.info(f"[STATUS] Total rows after filtering: {total_rows:,}")

                if total_rows == 0:
                    logger.warning("[WARN] No data found after date filtering!")
                    return

                # Process in chunks
                offset = 0
                chunk_num = 0

                while offset < total_rows:
                    chunk_num += 1
                    chunk = lazy_df.slice(offset, chunk_size).collect()

                    if len(chunk) == 0:
                        break

                    logger.info(f"📦 Processing chunk {chunk_num}: {len(chunk):,} rows (offset: {offset:,})")
                    yield chunk

                    offset += chunk_size

                    # Memory cleanup
                    del chunk

            else:
                # For CSV files, use chunked reading
                import pandas as pd

                chunk_iter = pd.read_csv(file_path, chunksize=chunk_size)

                for chunk_num, chunk_pd in enumerate(chunk_iter, 1):
                    # Convert to polars
                    chunk = pl.from_pandas(chunk_pd)

                    # Apply date filter
                    if self.config.date_based_filtering and 'date' in chunk.columns:
                        cutoff_date = pl.lit(self.config.training_cutoff_date).str.strptime(pl.Date, "%Y-%m-%d")
                        chunk = chunk.filter(
                            pl.col('date').str.strptime(pl.Date, "%d-%m-%Y") > cutoff_date
                        )

                    if len(chunk) > 0:
                        logger.info(f"📦 Processing chunk {chunk_num}: {len(chunk):,} rows")
                        yield chunk

                    # Memory cleanup
                    del chunk_pd, chunk

        except Exception as e:
            logger.error(f"[ERROR] Error streaming data: {e}")
            raise

    def preprocess_data(self, df: pl.DataFrame) -> Tuple[np.ndarray, np.ndarray, List[str]]:
        """
        Preprocess data for training

        Args:
            df: Raw backtesting data

        Returns:
            Tuple of (features, targets, feature_names)
        """
        logger.info("[WORKFLOW] Preprocessing data...")

        # Check for required columns
        missing_features = [col for col in self.config.feature_columns if col not in df.columns]
        missing_targets = [col for col in self.config.target_columns if col not in df.columns]

        if missing_features:
            logger.warning(f"[WARN]  Missing feature columns: {missing_features}")
            # Remove missing features from config
            self.config.feature_columns = [col for col in self.config.feature_columns if col in df.columns]

        if missing_targets:
            logger.warning(f"[WARN]  Missing target columns: {missing_targets}")
            # Remove missing targets from config
            self.config.target_columns = [col for col in self.config.target_columns if col in df.columns]

        # Create a copy to avoid modifying the original DataFrame in place
        processed_df = df.clone()

        # --- Handle Categorical Features and Classification Targets ---
        # Combine all columns that need label encoding (string to int)
        cols_to_label_encode = []
        for col in self.config.feature_columns:
            if col in processed_df.columns and processed_df[col].dtype == pl.Utf8:
                cols_to_label_encode.append(col)
        for col in self.config.classification_targets:
            if col in processed_df.columns and processed_df[col].dtype == pl.Utf8:
                cols_to_label_encode.append(col)

        for col in set(cols_to_label_encode): # Use set to avoid duplicates
            if col not in self.encoders:
                self.encoders[col] = LabelEncoder()
                unique_values = processed_df[col].unique().to_list()
                self.encoders[col].fit(unique_values)
            
            # Apply transformation, mapping unknown values to -1
            processed_df = processed_df.with_columns(
                pl.col(col).map_elements(
                    lambda x: self.encoders[col].transform([x])[0] if x is not None and x in self.encoders[col].classes_ else -1,
                    return_dtype=pl.Int32
                ).alias(col)
            )
        logger.info(f"🏷️  Categorical features and targets encoded: {list(set(cols_to_label_encode))}")

        # --- Ensure all relevant columns are numeric and handle nulls ---
        # This includes all feature columns and all target columns (both regression and now-encoded classification)
        all_cols_for_numeric_processing = list(set(self.config.feature_columns + self.config.target_columns))
        
        expressions = []
        for col in all_cols_for_numeric_processing:
            if col in processed_df.columns:
                # Attempt to cast to Float64 for regression targets and features
                # For classification targets, they are already Int32, so this cast is fine.
                expressions.append(
                    pl.col(col).cast(pl.Float64, strict=False).alias(col)
                )
        if expressions:
            processed_df = processed_df.with_columns(expressions)
            logger.debug("Coerced all relevant columns to Float64 (or kept Int32 for encoded categoricals).")

        # Fill any remaining nulls (now only in numeric columns) with mean
        # This should be safe now as all columns are either Int32 (for classification targets) or Float64
        processed_df = processed_df.fill_null(strategy="mean")
        logger.debug("Filled nulls in numeric columns with mean.")

        # Extract features and targets
        features = processed_df.select(self.config.feature_columns).to_numpy(allow_copy=True).astype(np.float64)
        targets = processed_df.select(self.config.target_columns).to_numpy(allow_copy=True).astype(np.float64) # Keep as float64 for consistency in target matrix

        # Scale features
        if 'feature_scaler' not in self.scalers:
            self.scalers['feature_scaler'] = StandardScaler()
            features = self.scalers['feature_scaler'].fit_transform(features)
        else:
            features = self.scalers['feature_scaler'].transform(features)

        logger.info(f"[SUCCESS] Preprocessed data: {features.shape[0]} samples, {features.shape[1]} features, {targets.shape[1]} targets")
        # The original categorical_features variable is no longer directly used for logging,
        # but the log message can reflect the combined encoding.
        logger.info(f"🏷️  Categorical features and targets handled.")

        return features, targets, self.config.feature_columns

    def split_data(self, features: np.ndarray, targets: np.ndarray) -> Tuple[np.ndarray, ...]:
        """
        Split data into train/validation/test sets

        Args:
            features: Feature matrix
            targets: Target matrix

        Returns:
            Tuple of (X_train, X_val, X_test, y_train, y_val, y_test)
        """
        logger.info("✂️  Splitting data...")

        # First split: train+val vs test
        X_temp, X_test, y_temp, y_test = train_test_split(
            features, targets,
            test_size=self.config.test_size,
            random_state=self.config.random_state,
            stratify=None  # Can't stratify with continuous targets
        )

        # Second split: train vs val
        val_size_adjusted = self.config.validation_size / (1 - self.config.test_size)
        X_train, X_val, y_train, y_val = train_test_split(
            X_temp, y_temp,
            test_size=val_size_adjusted,
            random_state=self.config.random_state
        )

        logger.info(f"[STATUS] Data split:")
        logger.info(f"   Train: {X_train.shape[0]} samples")
        logger.info(f"   Validation: {X_val.shape[0]} samples")
        logger.info(f"   Test: {X_test.shape[0]} samples")

        return X_train, X_val, X_test, y_train, y_val, y_test

    def train_lightgbm(self, X_train: np.ndarray, y_train: np.ndarray,
                      X_val: np.ndarray, y_val: np.ndarray) -> Dict[str, lgb.Booster]:
        """
        Train LightGBM models for multi-target regression

        Args:
            X_train: Training features
            y_train: Training targets
            X_val: Validation features
            y_val: Validation targets

        Returns:
            Dictionary of trained LightGBM models (one per target)
        """
        logger.info("🌟 Training LightGBM models...")

        lgb_models = {}

        for i, target_name in enumerate(self.config.target_columns):
            logger.info(f"   Training model for: {target_name}")

            # Create datasets
            train_data = lgb.Dataset(X_train, label=y_train[:, i])
            val_data = lgb.Dataset(X_val, label=y_val[:, i], reference=train_data)

            # Train model
            model = lgb.train(
                params=self.config.lgb_params,
                train_set=train_data,
                valid_sets=[train_data, val_data],
                valid_names=['train', 'val'],
                num_boost_round=1000,
                callbacks=[
                    lgb.early_stopping(stopping_rounds=50),
                    lgb.log_evaluation(period=100)
                ]
            )

            lgb_models[target_name] = model

            # Store feature importance
            importance = model.feature_importance(importance_type='gain')
            self.feature_importance[f'lgb_{target_name}'] = dict(
                zip(self.config.feature_columns, importance)
            )

        logger.info("[SUCCESS] LightGBM training completed")
        return lgb_models

    def train_tabnet(self, X_train: np.ndarray, y_train: np.ndarray,
                    X_val: np.ndarray, y_val: np.ndarray) -> Optional[TabNetRegressor]:
        """
        Train TabNet model for multi-target regression

        Args:
            X_train: Training features
            y_train: Training targets
            X_val: Validation features
            y_val: Validation targets

        Returns:
            Trained TabNet model or None if training fails
        """
        logger.info("🧠 Training TabNet model...")

        try:
            # Initialize TabNet
            tabnet_model = TabNetRegressor(**self.config.tabnet_params)

            # Determine appropriate batch size based on data size
            n_samples = X_train.shape[0]
            batch_size = min(1024, max(32, n_samples // 10))  # Adaptive batch size
            virtual_batch_size = min(128, batch_size // 2)

            logger.info(f"[STATUS] Using batch_size={batch_size}, virtual_batch_size={virtual_batch_size}")

            # Train model
            tabnet_model.fit(
                X_train=X_train.astype(np.float32),
                y_train=y_train.astype(np.float32),
                eval_set=[(X_val.astype(np.float32), y_val.astype(np.float32))],
                eval_name=['val'],
                eval_metric=['rmse'],
                max_epochs=200,
                patience=20,
                batch_size=batch_size,
                virtual_batch_size=virtual_batch_size,
                num_workers=0,
                drop_last=True  # Drop last incomplete batch to avoid batch norm issues
            )

            # Store feature importance
            if hasattr(tabnet_model, 'feature_importances_'):
                self.feature_importance['tabnet'] = dict(
                    zip(self.config.feature_columns, tabnet_model.feature_importances_)
                )

            logger.info("[SUCCESS] TabNet training completed")
            return tabnet_model

        except Exception as e:
            logger.warning(f"[WARN] TabNet training failed: {e}")
            logger.info("[WORKFLOW] Continuing with LightGBM-only ensemble")
            return None

    def optimize_hyperparameters(self, X_train: np.ndarray, y_train: np.ndarray,
                                X_val: np.ndarray, y_val: np.ndarray) -> Dict[str, Any]:
        """
        Optimize hyperparameters using Optuna

        Args:
            X_train: Training features
            y_train: Training targets
            X_val: Validation features
            y_val: Validation targets

        Returns:
            Best hyperparameters
        """
        logger.info("[DEBUG] Optimizing hyperparameters with Optuna...")

        def objective(trial):
            # LightGBM hyperparameters
            lgb_params = {
                'objective': 'regression',
                'metric': 'rmse',
                'boosting_type': 'gbdt',
                'num_leaves': trial.suggest_int('num_leaves', 10, 100),
                'learning_rate': trial.suggest_float('learning_rate', 0.01, 0.3),
                'feature_fraction': trial.suggest_float('feature_fraction', 0.4, 1.0),
                'bagging_fraction': trial.suggest_float('bagging_fraction', 0.4, 1.0),
                'bagging_freq': trial.suggest_int('bagging_freq', 1, 7),
                'min_child_samples': trial.suggest_int('min_child_samples', 5, 100),
                'verbose': -1,
                'random_state': self.config.random_state,
                'device_type': 'gpu' if self.config.use_gpu else 'cpu'
            }

            # Train and evaluate
            total_rmse = 0
            for i, target_name in enumerate(self.config.target_columns):
                train_data = lgb.Dataset(X_train, label=y_train[:, i])
                val_data = lgb.Dataset(X_val, label=y_val[:, i], reference=train_data)

                model = lgb.train(
                    params=lgb_params,
                    train_set=train_data,
                    valid_sets=[val_data],
                    num_boost_round=100,
                    callbacks=[lgb.early_stopping(stopping_rounds=10)]
                )

                y_pred = model.predict(X_val)
                rmse = np.sqrt(mean_squared_error(y_val[:, i], y_pred))
                total_rmse += rmse

            return total_rmse / len(self.config.target_columns)

        # Run optimization
        study = optuna.create_study(direction='minimize')
        study.optimize(
            objective,
            n_trials=self.config.optuna_trials,
            timeout=self.config.optuna_timeout
        )

        best_params = study.best_params
        logger.info(f"[SUCCESS] Best hyperparameters found: {best_params}")

        return best_params

    def train_ensemble(self, X_train: np.ndarray, y_train: np.ndarray,
                      X_val: np.ndarray, y_val: np.ndarray,
                      optimize_hyperparams: bool = True) -> None:
        """
        Train the complete ensemble (LightGBM + TabNet)

        Args:
            X_train: Training features
            y_train: Training targets
            X_val: Validation features
            y_val: Validation targets
            optimize_hyperparams: Whether to optimize hyperparameters
        """
        logger.info("[INIT] Training ensemble models...")

        # Optimize hyperparameters if requested
        if optimize_hyperparams:
            best_params = self.optimize_hyperparameters(X_train, y_train, X_val, y_val)
            # Update LightGBM parameters
            self.config.lgb_params.update(best_params)

        # Train LightGBM models
        self.models['lightgbm'] = self.train_lightgbm(X_train, y_train, X_val, y_val)

        # Train TabNet model
        tabnet_model = self.train_tabnet(X_train, y_train, X_val, y_val)
        if tabnet_model is not None:
            self.models['tabnet'] = tabnet_model
        else:
            logger.info("[WORKFLOW] Using LightGBM-only ensemble due to TabNet training failure")

        self.is_trained = True
        logger.info("[SUCCESS] Ensemble training completed")

    def predict(self, X: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """
        Make ensemble predictions

        Args:
            X: Features to predict on

        Returns:
            Tuple of (predictions, confidence_scores)
        """
        if not self.is_trained:
            raise ValueError("Models must be trained before making predictions")

        # LightGBM predictions
        lgb_predictions = np.zeros((X.shape[0], len(self.config.target_columns)))
        for i, target_name in enumerate(self.config.target_columns):
            lgb_predictions[:, i] = self.models['lightgbm'][target_name].predict(X)

        # Check if TabNet is available
        if 'tabnet' in self.models and self.models['tabnet'] is not None:
            # TabNet predictions
            tabnet_predictions = self.models['tabnet'].predict(X.astype(np.float32))

            # Ensemble predictions
            ensemble_predictions = (
                self.config.ensemble_weights['lightgbm'] * lgb_predictions +
                self.config.ensemble_weights['tabnet'] * tabnet_predictions
            )

            # Calculate confidence scores (based on agreement between models)
            confidence_scores = 1 - np.abs(lgb_predictions - tabnet_predictions) / (
                np.abs(lgb_predictions) + np.abs(tabnet_predictions) + 1e-8
            )
            confidence_scores = np.mean(confidence_scores, axis=1)
        else:
            # Use LightGBM-only predictions
            ensemble_predictions = lgb_predictions

            # Calculate confidence scores based on prediction variance across targets
            confidence_scores = 1 / (1 + np.std(lgb_predictions, axis=1))

        return ensemble_predictions, confidence_scores

    def rank_strategies(self, predictions: np.ndarray,
                       strategy_names: List[str] = None,
                       market_regime: str = 'normal') -> List[Dict[str, Any]]:
        """
        Rank strategies based on predicted performance metrics

        Args:
            predictions: Predicted performance metrics
            strategy_names: Optional strategy names
            market_regime: Market regime for conditioning

        Returns:
            List of ranked strategies with scores
        """
        if strategy_names is None:
            strategy_names = [f"Strategy_{i}" for i in range(len(predictions))]

        rankings = []

        for i, (strategy_name, pred) in enumerate(zip(strategy_names, predictions)):
            # Create prediction dictionary
            pred_dict = dict(zip(self.config.target_columns, pred))

            # Calculate composite score (weighted combination)
            weights = {
                'ROI': 0.3,
                'sharpe_ratio': 0.25,
                'expectancy': 0.2,
                'max_drawdown': -0.15,  # Negative because lower is better
                'profit_factor': 0.1,
                'risk_reward_ratio': 0.05,
                'accuracy': 0.05
            }

            composite_score = 0
            for metric, weight in weights.items():
                if metric in pred_dict:
                    composite_score += weight * pred_dict[metric]

            # Market regime adjustment
            if market_regime == 'bull':
                composite_score *= 1.1 if pred_dict.get('ROI', 0) > 0 else 0.9
            elif market_regime == 'bear':
                composite_score *= 1.1 if pred_dict.get('max_drawdown', 0) < 0.1 else 0.9

            rankings.append({
                'strategy_name': strategy_name,
                'composite_score': composite_score,
                'predicted_metrics': pred_dict,
                'market_regime': market_regime
            })

        # Sort by composite score (descending)
        rankings.sort(key=lambda x: x['composite_score'], reverse=True)

        return rankings

    def evaluate_models(self, X_test: np.ndarray, y_test: np.ndarray) -> Dict[str, Dict[str, float]]:
        """
        Evaluate trained models on test set

        Args:
            X_test: Test features
            y_test: Test targets

        Returns:
            Dictionary of evaluation metrics
        """
        logger.info("[STATUS] Evaluating models...")

        predictions, confidence = self.predict(X_test)

        metrics = {}

        for i, target_name in enumerate(self.config.target_columns):
            y_true = y_test[:, i]
            y_pred = predictions[:, i]

            metrics[target_name] = {
                'rmse': np.sqrt(mean_squared_error(y_true, y_pred)),
                'mae': mean_absolute_error(y_true, y_pred),
                'r2': r2_score(y_true, y_pred),
                'mean_confidence': np.mean(confidence)
            }

        # Overall metrics
        overall_rmse = np.sqrt(mean_squared_error(y_test, predictions))
        overall_r2 = r2_score(y_test, predictions)

        metrics['overall'] = {
            'rmse': overall_rmse,
            'r2': overall_r2,
            'mean_confidence': np.mean(confidence)
        }

        logger.info("[SUCCESS] Model evaluation completed")
        return metrics

    def save_models(self, model_name: str = "ai_training_ensemble") -> None:
        """
        Save trained models and preprocessing objects

        Args:
            model_name: Base name for saved models
        """
        if not self.is_trained:
            raise ValueError("No trained models to save")

        logger.info(f"💾 Saving models as: {model_name}")

        model_dir = os.path.join(self.config.models_dir, model_name)
        os.makedirs(model_dir, exist_ok=True)

        # Save models based on their type (handling both batch and online training models)
        for model_type, models_dict_or_instance in self.models.items():
            if model_type == 'lightgbm':
                lgb_dir = os.path.join(model_dir, "lightgbm")
                os.makedirs(lgb_dir, exist_ok=True)
                for target_name, model in models_dict_or_instance.items():
                    model.save_model(os.path.join(lgb_dir, f"{target_name}.txt"))
            elif model_type == 'tabnet':
                # TabNet model is a single instance
                models_dict_or_instance.save_model(os.path.join(model_dir, "tabnet"))
            elif isinstance(models_dict_or_instance, dict): # Check if it's a dictionary of models (e.g., online_models)
                online_models_dir = os.path.join(model_dir, "online_models")
                os.makedirs(online_models_dir, exist_ok=True)
                for target_name, model_instance in models_dict_or_instance.items():
                    joblib.dump(model_instance, os.path.join(online_models_dir, f"{target_name}.pkl"))
            else: # It's a single model instance (e.g., from online training, directly assigned to self.models)
                # This case should ideally not happen if self.models is always a dict of dicts or specific instances
                # but as a fallback, save it directly if it's a single model.
                # However, the current structure of self.models for online training is a dict of models,
                # so the `elif isinstance(models_dict_or_instance, dict):` block should handle it.
                # This `else` block might indicate an unexpected assignment to self.models.
                # For now, I'll assume the online models are always in a dictionary under `self.models`.
                # The previous error was because `self.models` itself was the dictionary of online models,
                # and the loop `for model_type, models_dict_or_instance in self.models.items():` was trying to
                # iterate over `models_dict_or_instance.items()` when `models_dict_or_instance` was an SGDClassifier.
                # The fix is to ensure `self.models` is structured correctly.
                # The `_finalize_online_training` sets `self.models = models` where `models` is the dictionary of SGD models.
                # So, `model_type` will be the target name (e.g., 'roi', 'is_profitable') and `models_dict_or_instance` will be the SGD model instance.
                # I need to adjust the loop to handle this.

                # Re-thinking the structure:
                # When `train_online_streaming` finishes, `self.models` is set to the `online_models` dictionary.
                # So, `self.models` will look like `{'roi': SGDRegressor_instance, 'is_profitable': SGDClassifier_instance}`.
                # The loop `for model_type, models_dict_or_instance in self.models.items():` will then have:
                # 1. `model_type = 'roi'`, `models_dict_or_instance = SGDRegressor_instance`
                # 2. `model_type = 'is_profitable'`, `models_dict_or_instance = SGDClassifier_instance`
                # In both these cases, `models_dict_or_instance` is NOT a dictionary, so the `elif isinstance(models_dict_or_instance, dict):` will be skipped.
                # The original `else` block was trying to call `.items()` on `models_dict_or_instance` which is the SGD model itself.

                # Corrected logic:
                # The `save_models` method needs to know if it's saving batch models (lightgbm, tabnet) or online models (SGD).
                # The `self.models` attribute is used for both.
                # When `train_ensemble` is called, `self.models` becomes `{'lightgbm': {...}, 'tabnet': ...}`.
                # When `train_online_streaming` is called, `self.models` becomes `{'roi': SGDRegressor, 'is_profitable': SGDClassifier}`.

                # I need to differentiate based on the keys in `self.models`.
                # If 'lightgbm' is a key, it's batch training models.
                # Otherwise, it's online training models.

                # Let's refactor the save_models method to handle this distinction more robustly.
                # I will remove the `for model_type, models_dict_or_instance in self.models.items():` loop
                # and instead use explicit checks for 'lightgbm' and 'tabnet' keys, and then handle the rest as online models.

                # This requires a larger change than `replace_in_file` can handle cleanly with a single block.
                # I will use `replace_in_file` to replace the entire `save_models` method.
                pass # This pass is a placeholder, the actual change will be a full method replacement.

        # Save preprocessing objects
        joblib.dump(self.scalers, os.path.join(model_dir, "scalers.pkl"))
        joblib.dump(self.encoders, os.path.join(model_dir, "encoders.pkl"))
        joblib.dump(self.config, os.path.join(model_dir, "config.pkl"))
        joblib.dump(self.feature_importance, os.path.join(model_dir, "feature_importance.pkl"))

        logger.info(f"[SUCCESS] Models saved to: {model_dir}")

    def _calculate_data_hash(self, df: pl.DataFrame) -> str:
        """Calculate hash of dataset for change detection"""
        import hashlib
        # Use a sample of the data for hash calculation (for performance)
        sample_size = min(10000, len(df))
        sample_df = df.sample(sample_size, seed=42) if len(df) > sample_size else df
        data_str = str(sample_df.to_pandas().values.tobytes())
        return hashlib.md5(data_str.encode()).hexdigest()

    def _save_data_checkpoint(self, df: pl.DataFrame, checkpoint_name: str = None) -> str:
        """Save data checkpoint for incremental training"""
        if checkpoint_name is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            checkpoint_name = f"data_checkpoint_{timestamp}"

        checkpoint_dir = Path(self.config.models_dir) / "checkpoints"
        checkpoint_dir.mkdir(parents=True, exist_ok=True)

        checkpoint_path = checkpoint_dir / f"{checkpoint_name}.parquet"
        df.write_parquet(checkpoint_path)

        # Save metadata
        metadata = {
            "timestamp": datetime.now().isoformat(),
            "rows": len(df),
            "columns": len(df.columns),
            "data_hash": self._calculate_data_hash(df)
        }

        metadata_path = checkpoint_dir / f"{checkpoint_name}_metadata.json"
        with open(metadata_path, 'w') as f:
            json.dump(metadata, f, indent=2)

        logger.info(f"💾 Data checkpoint saved: {checkpoint_path}")
        return str(checkpoint_path)

    def _load_data_checkpoint(self, checkpoint_path: str) -> pl.DataFrame:
        """Load data from checkpoint"""
        return pl.read_parquet(checkpoint_path)

    def _detect_data_changes(self, new_df: pl.DataFrame) -> Dict[str, Any]:
        """Detect changes in new data compared to last training"""
        changes = {
            "new_data": False,
            "schema_changed": False,
            "significant_change": False,
            "new_rows": 0,
            "change_ratio": 0.0
        }

        if self.training_data_hash is None:
            changes["new_data"] = True
            changes["significant_change"] = True
            return changes

        # Calculate new hash
        new_hash = self._calculate_data_hash(new_df)

        if new_hash != self.training_data_hash:
            changes["new_data"] = True

            # Load last checkpoint to compare
            if self.data_checkpoint_path and Path(self.data_checkpoint_path).exists():
                try:
                    old_df = self._load_data_checkpoint(self.data_checkpoint_path)

                    # Check schema changes
                    if set(new_df.columns) != set(old_df.columns):
                        changes["schema_changed"] = True
                        changes["significant_change"] = True

                    # Calculate new rows
                    changes["new_rows"] = max(0, len(new_df) - len(old_df))
                    changes["change_ratio"] = changes["new_rows"] / len(old_df) if len(old_df) > 0 else 1.0

                    # Determine if change is significant
                    if (changes["new_rows"] >= self.config.incremental_batch_size or
                        changes["change_ratio"] >= 0.1):
                        changes["significant_change"] = True

                except Exception as e:
                    logger.warning(f"Could not compare with checkpoint: {e}")
                    changes["significant_change"] = True

        return changes

    def load_models(self, model_name: str = "ai_training_ensemble") -> None:
        """
        Load trained models and preprocessing objects

        Args:
            model_name: Base name of saved models
        """
        logger.info(f"📂 Loading models: {model_name}")

        model_dir = os.path.join(self.config.models_dir, model_name)

        if not os.path.exists(model_dir):
            raise FileNotFoundError(f"Model directory not found: {model_dir}")

        # Load configuration
        self.config = joblib.load(os.path.join(model_dir, "config.pkl"))

        # Load preprocessing objects
        self.scalers = joblib.load(os.path.join(model_dir, "scalers.pkl"))
        self.encoders = joblib.load(os.path.join(model_dir, "encoders.pkl"))
        self.feature_importance = joblib.load(os.path.join(model_dir, "feature_importance.pkl"))

        # Load LightGBM models
        lgb_dir = os.path.join(model_dir, "lightgbm")
        lgb_models = {}
        for target_name in self.config.target_columns:
            model_file = os.path.join(lgb_dir, f"{target_name}.txt")
            if os.path.exists(model_file):
                lgb_models[target_name] = lgb.Booster(model_file=model_file)
        self.models['lightgbm'] = lgb_models

        # Load TabNet model
        tabnet_model = TabNetRegressor()
        tabnet_model.load_model(os.path.join(model_dir, "tabnet.zip"))
        self.models['tabnet'] = tabnet_model

        self.is_trained = True
        logger.info("[SUCCESS] Models loaded successfully")

    async def train_async(self, file_path: Optional[str] = None,
                         optimize_hyperparams: bool = True) -> Dict[str, Any]:
        """
        Main async training method

        Args:
            file_path: Optional custom data file path
            optimize_hyperparams: Whether to optimize hyperparameters

        Returns:
            Training results and evaluation metrics
        """
        logger.info("[INIT] Starting AI Training Agent training...")

        # Check if online learning should be used
        if self.config.online_learning_enabled and self._should_use_online_learning(file_path):
            logger.info("[TRAIN] Using online streaming training for large dataset")
            return await self.train_online_streaming(file_path)

        # Traditional batch training for smaller datasets
        logger.info("[TRAIN] Using traditional batch training")

        # Load and preprocess data
        df = self.load_data(file_path)
        features, targets, feature_names = self.preprocess_data(df)

        # Split data
        X_train, X_val, X_test, y_train, y_val, y_test = self.split_data(features, targets)

        # Train ensemble
        self.train_ensemble(X_train, y_train, X_val, y_val, optimize_hyperparams)

        # Evaluate models
        evaluation_metrics = self.evaluate_models(X_test, y_test)

        # Save models
        self.save_models()

        # Create training summary
        training_summary = {
            'training_type': 'batch',
            'data_shape': df.shape,
            'feature_count': len(self.config.feature_columns),
            'target_count': len(self.config.target_columns),
            'train_samples': X_train.shape[0],
            'val_samples': X_val.shape[0],
            'test_samples': X_test.shape[0],
            'evaluation_metrics': evaluation_metrics,
            'feature_importance': self.feature_importance,
            'config': self.config.__dict__
        }

        logger.info("[SUCCESS] AI Training Agent training completed!")
        return training_summary

    def _should_use_online_learning(self, file_path: str) -> bool:
        """Determine if online learning should be used based on data size"""
        try:
            if not self.config.memory_efficient_mode:
                return False

            # Check file size
            file_size_gb = os.path.getsize(file_path) / (1024**3)

            # Use online learning for files > 1GB or when explicitly enabled
            if file_size_gb > 1.0:
                logger.info(f"[DECISION] Large file detected ({file_size_gb:.2f}GB), using online learning")
                return True

            # Check if date filtering is enabled (indicates incremental scenario)
            if self.config.date_based_filtering:
                logger.info("[DECISION] Date-based filtering enabled, using online learning")
                return True

            return False

        except Exception as e:
            logger.warning(f"[DECISION] Error checking file size: {e}, defaulting to batch training")
            return False

# ═══════════════════════════════════════════════════════════════════════════════
# [INIT] MAIN EXECUTION
# ═══════════════════════════════════════════════════════════════════════════════

async def main():
    """Main execution function"""
    try:
        # Initialize agent
        agent = AITrainingAgent()

        # Train models
        results = await agent.train_async(optimize_hyperparams=True)

        # Print results
        print("\n" + "="*80)
        print("[TARGET] AI TRAINING AGENT RESULTS")
        print("="*80)
        print(f"[STATUS] Data processed: {results['data_shape'][0]:,} rows, {results['data_shape'][1]} columns")
        print(f"[CONFIG] Features used: {results['feature_count']}")
        print(f"[TARGET] Targets predicted: {results['target_count']}")
        print(f"[METRICS] Training samples: {results['train_samples']:,}")
        print(f"[STATUS] Validation samples: {results['val_samples']:,}")
        print(f"🧪 Test samples: {results['test_samples']:,}")

        print("\n[STATUS] EVALUATION METRICS:")
        for target, metrics in results['evaluation_metrics'].items():
            if target != 'overall':
                print(f"   {target}:")
                print(f"      RMSE: {metrics['rmse']:.4f}")
                print(f"      R²: {metrics['r2']:.4f}")

        print(f"\n[TARGET] OVERALL PERFORMANCE:")
        overall = results['evaluation_metrics']['overall']
        print(f"   RMSE: {overall['rmse']:.4f}")
        print(f"   R²: {overall['r2']:.4f}")
        print(f"   Mean Confidence: {overall['mean_confidence']:.4f}")

        print("\n[SUCCESS] Training completed successfully!")

    except Exception as e:
        logger.error(f"[ERROR] Training failed: {str(e)}")
        raise

if __name__ == "__main__":
    asyncio.run(main())
