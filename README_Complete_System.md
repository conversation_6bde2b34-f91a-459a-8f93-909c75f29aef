# Complete Backtesting + ML Training System

A comprehensive end-to-end system that combines advanced backtesting with machine learning to optimize trading strategy selection and performance prediction.

## 🎯 System Overview

This system provides a complete workflow from strategy backtesting to ML-driven strategy selection:

1. **Enhanced Backtesting**: Advanced backtesting with walk-forward analysis, market regime detection, and statistical validation
2. **ML Training**: Sophisticated machine learning models trained on backtesting results
3. **Strategy Prediction**: Use trained models to predict strategy performance and select optimal strategies
4. **Automated Workflow**: Complete pipeline automation with comprehensive reporting

## 🏗️ Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Market Data   │───▶│  Enhanced        │───▶│  Backtesting    │
│   & Strategies  │    │  Backtesting     │    │  Results        │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                                         │
                                                         ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│  Strategy       │◀───│  Enhanced ML     │◀───│  Feature        │
│  Predictions    │    │  Training Agent  │    │  Engineering    │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                       │
         ▼                       ▼
┌─────────────────┐    ┌──────────────────┐
│  Trading        │    │  Model           │
│  Insights       │    │  Explainability  │
└─────────────────┘    └──────────────────┘
```

## 🚀 Key Features

### Enhanced Backtesting System
- **Walk-Forward Analysis**: Prevents overfitting with proper out-of-sample testing
- **Market Regime Detection**: Identifies trending, ranging, and volatile market conditions
- **Statistical Validation**: Monte Carlo simulation and bootstrap testing
- **Realistic Costs**: Transaction costs, slippage, and margin requirements
- **Risk Management**: Position sizing and correlation limits

### Advanced ML Training
- **Multi-Task Learning**: Predicts multiple performance metrics simultaneously
- **State-of-the-Art Models**: LightGBM, XGBoost, CatBoost, TabNet
- **Automated Optimization**: Hyperparameter tuning with Optuna
- **Feature Engineering**: Automatic creation of derived features and interactions
- **Model Explainability**: SHAP values for model interpretation
- **Ensemble Methods**: Weighted averaging for improved predictions

### Windows 10 Optimized
- **GPU Acceleration**: Automatic detection and utilization of NVIDIA GPUs
- **Memory Efficient**: Optimized for systems with 8GB+ RAM
- **Parallel Processing**: Multi-core CPU utilization
- **Easy Installation**: Automated setup scripts

## 📋 Requirements

### System Requirements
- **OS**: Windows 10 (64-bit)
- **Python**: 3.8+ (3.9+ recommended)
- **Memory**: 8GB+ RAM (16GB+ recommended)
- **Storage**: 5GB+ free space
- **CPU**: Multi-core processor recommended

### Optional GPU Requirements
- **NVIDIA GPU** with CUDA 11.0+
- **CUDA Toolkit** 11.0+
- **8GB+ GPU memory** for large datasets

## 🛠️ Installation

### Quick Setup
```bash
# Clone or download the system
cd Equity

# Run automated setup
python setup_complete_system.py

# Verify installation
python -c "import lightgbm, xgboost, catboost; print('✓ All packages installed')"
```

### Manual Installation
```bash
# Install Python dependencies
pip install -r requirements_complete_system.txt

# Create directory structure
python create_directories.py

# Verify GPU support (optional)
python check_gpu_support.py
```

## 📊 Usage

### Complete Workflow
```python
from complete_backtesting_ml_workflow import CompleteTradingWorkflow

# Initialize and run complete workflow
workflow = CompleteTradingWorkflow()
await workflow.initialize()

# Run all phases
backtest_results = await workflow.run_backtesting_phase()
training_results = await workflow.run_ml_training_phase(backtest_results)
predictions, insights = await workflow.run_prediction_phase()
```

### Individual Components

#### 1. Enhanced Backtesting
```python
from agents.enhanced_backtesting_improved import EnhancedBacktester, BacktestConfig

# Configure backtesting
config = BacktestConfig(
    initial_capital=100000,
    transaction_cost_pct=0.05,
    walk_forward_steps=5
)

# Run backtesting
backtester = EnhancedBacktester(config)
results = await backtester.run_comprehensive_backtest()
```

#### 2. ML Training
```python
from agents.enhanced_model_training_agent import EnhancedModelTrainingAgent

# Initialize training agent
agent = EnhancedModelTrainingAgent()

# Load and train on backtesting results
df = await agent.load_backtesting_data("data/backtest_improved/results.parquet")
training_results = await agent.train_all_models(df)
```

#### 3. Strategy Prediction
```python
# Make predictions on new strategies
predictions = await agent.predict(new_strategy_data, "sharpe_ratio_prediction")
top_strategies = agent.select_top_strategies(predictions, n_strategies=5)
```

## 📈 Input Data Format

### Market Data
```python
# Required columns for backtesting
market_data = {
    'datetime': pd.Timestamp,
    'open': float,
    'high': float, 
    'low': float,
    'close': float,
    'volume': int,
    # Technical indicators (auto-generated if missing)
    'rsi_14': float,
    'ema_20': float,
    'macd': float,
    # ... additional indicators
}
```

### Strategy Definitions
```yaml
# strategies.yaml
strategies:
  - name: "RSI_Mean_Reversion"
    long: "rsi_14 < 30 and close > ema_20"
    short: "rsi_14 > 70 and close < ema_20"
    market_conditions: ["ranging", "low_volatility"]
    
  - name: "MACD_Momentum"
    long: "macd > macd_signal and volume > sma_20_volume"
    short: "macd < macd_signal and volume > sma_20_volume"
    market_conditions: ["trending_up", "trending_down"]
```

## 📊 Output and Results

### Backtesting Results
```python
{
    'strategy_name': str,
    'avg_sharpe_ratio': float,
    'avg_roi': float,
    'avg_profit_factor': float,
    'avg_max_drawdown': float,
    'consistency_score': float,
    'avg_accuracy': float,
    'walk_forward_results': list,
    'regime_performance': dict,
    'statistical_significance': dict
}
```

### ML Training Results
```python
{
    'sharpe_ratio_prediction': {
        'lightgbm': {'test_score': 0.85, 'r2_score': 0.85},
        'xgboost': {'test_score': 0.82, 'r2_score': 0.82},
        'ensemble': {'test_score': 0.87, 'r2_score': 0.87}
    },
    'feature_importance': {
        'top_features': [
            {'feature': 'consistency_score', 'importance': 0.23},
            {'feature': 'avg_accuracy', 'importance': 0.19}
        ]
    }
}
```

### Trading Insights
```python
{
    'top_strategies': ['Strategy_A', 'Strategy_B', 'Strategy_C'],
    'risk_warnings': ['High_Risk_Strategy_X'],
    'recommendations': [
        'Focus on strategies with predicted Sharpe > 1.0',
        'Diversify across market regimes',
        'Monitor model performance monthly'
    ],
    'expected_performance': {
        'portfolio_sharpe': 1.45,
        'expected_roi': 12.3,
        'max_drawdown': -8.2
    }
}
```

## 🔧 Configuration

### Main Configuration
```yaml
# config/optimized_training_config.yaml
prediction_tasks:
  sharpe_ratio_prediction:
    type: "regression"
    target_columns: ["avg_sharpe_ratio"]
    weight: 0.25

models:
  enabled_models: ["lightgbm", "xgboost", "catboost"]
  ensemble_method: "weighted_average"

training:
  optuna_trials: 100
  cv_folds: 5
  early_stopping_rounds: 50
```

### Performance Tuning
```yaml
# For faster training (development)
training:
  optuna_trials: 20
  cv_folds: 3
  enabled_models: ["lightgbm"]

# For maximum accuracy (production)
training:
  optuna_trials: 200
  cv_folds: 10
  enabled_models: ["lightgbm", "xgboost", "catboost", "tabnet"]
```

## 🎯 Performance Optimization

### GPU Acceleration
```python
# Automatic GPU detection and usage
config.use_gpu = True  # Auto-detect
config.lightgbm_params['device_type'] = 'gpu'
config.xgboost_params['tree_method'] = 'gpu_hist'
config.catboost_params['task_type'] = 'GPU'
```

### Memory Optimization
```python
# For large datasets
config.memory_efficient_mode = True
config.batch_size = 1000
config.streaming_enabled = True
```

### Parallel Processing
```python
# Utilize all CPU cores
config.n_jobs = -1
config.parallel_cv = True
config.concurrent_models = True
```

## 📊 Monitoring and Evaluation

### Model Performance Tracking
```python
# Monitor model performance over time
performance_tracker = ModelPerformanceTracker()
performance_tracker.track_predictions(predictions, actual_results)
performance_tracker.detect_drift()
```

### Backtesting Validation
```python
# Validate backtesting methodology
validator = BacktestValidator()
validation_results = validator.validate_methodology(backtest_results)
```

## 🧪 Testing and Validation

### Run Complete Test Suite
```bash
# Run all tests
python -m pytest tests/ -v

# Run specific test categories
python -m pytest tests/test_backtesting.py -v
python -m pytest tests/test_ml_training.py -v
python -m pytest tests/test_integration.py -v
```

### Performance Benchmarks
```bash
# Benchmark system performance
python benchmarks/run_benchmarks.py

# Compare with baseline
python benchmarks/compare_performance.py
```

## 🔍 Model Explainability

### SHAP Analysis
```python
# Generate SHAP explanations
shap_values = agent.get_shap_values("sharpe_ratio_prediction")
agent.plot_shap_summary(shap_values)
```

### Feature Importance
```python
# Analyze feature importance
importance = agent.get_feature_importance()
agent.plot_feature_importance(importance, top_n=15)
```

## 🚀 Production Deployment

### Model Serving
```python
# Deploy models for real-time prediction
from model_server import ModelServer

server = ModelServer(agent)
server.start(host="localhost", port=8000)
```

### Automated Retraining
```python
# Set up automated retraining pipeline
from retraining_pipeline import RetrainingPipeline

pipeline = RetrainingPipeline(
    retrain_frequency="weekly",
    performance_threshold=0.1
)
pipeline.start()
```

## 🔧 Troubleshooting

### Common Issues

1. **GPU Not Detected**
   ```bash
   # Check CUDA installation
   nvidia-smi
   python -c "import torch; print(torch.cuda.is_available())"
   ```

2. **Memory Issues**
   ```python
   # Reduce memory usage
   config.batch_size = 500
   config.memory_efficient_mode = True
   ```

3. **Slow Training**
   ```python
   # Speed up training
   config.optuna_trials = 50
   config.cv_folds = 3
   config.early_stopping_rounds = 20
   ```

### Performance Issues

1. **Poor Predictions**
   - Increase `optuna_trials` for better hyperparameters
   - Enable feature engineering
   - Check data quality and outliers
   - Add more diverse strategies to training data

2. **Overfitting**
   - Increase cross-validation folds
   - Enable early stopping
   - Reduce model complexity
   - Add regularization

## 📚 Advanced Usage

### Custom Strategy Development
```python
# Create custom strategy evaluator
class CustomStrategyEvaluator:
    def evaluate_strategy(self, strategy_def, market_data):
        # Custom evaluation logic
        return strategy_results

# Integrate with backtesting system
backtester.add_custom_evaluator(CustomStrategyEvaluator())
```

### Custom ML Models
```python
# Add custom ML model
class CustomModel:
    def fit(self, X, y):
        # Custom training logic
        pass
    
    def predict(self, X):
        # Custom prediction logic
        pass

# Register with training agent
agent.register_custom_model("custom_model", CustomModel)
```

## 📞 Support and Contributing

### Getting Help
1. Check the troubleshooting section
2. Review example scripts
3. Check logs in `logs/` directory
4. Create detailed issue reports

### Contributing
1. Fork the repository
2. Create feature branch
3. Add comprehensive tests
4. Submit pull request with detailed description

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- **LightGBM Team**: Excellent gradient boosting framework
- **XGBoost Team**: High-performance ML library  
- **CatBoost Team**: Advanced categorical handling
- **Optuna Team**: Automated hyperparameter optimization
- **Polars Team**: Fast DataFrame operations
- **SHAP Team**: Model explainability tools

---

**Ready to revolutionize your trading strategy selection! 🚀📈**

For detailed examples and tutorials, see the `examples/` directory.
For configuration options, see the `config/` directory.
For API documentation, see the `docs/` directory.