#!/usr/bin/env python3
"""
MODERN TRADING SYSTEM RUNNER
Clean, modern implementation of the AI trading system

Features:
- Modern agent architecture with proper separation of concerns
- Real-time WebSocket data streaming using SmartAPI v2
- Clean async/await implementation
- Proper error handling and logging
- Modern timeframes: 1min, 2min, 3min, 5min, 10min, 15min, 30min, 1hr
"""

import asyncio
import logging
import argparse
import signal
from pathlib import Path
from datetime import datetime
from typing import Dict, Any
import sys

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

from agents.modern_market_data_agent import ModernMarketDataAgent
from agents.modern_signal_generation_agent import ModernSignalGenerationAgent
from agents.risk_management_agent import RiskManagementAgent
from agents.modern_execution_agent import ModernExecutionAgent
from utils.event_bus import EventBus
from utils.config_manager import ConfigManager
from utils.logging_manager import LoggingManager

logger = logging.getLogger(__name__)

class ModernTradingSystem:
    """Modern trading system orchestrator"""
    
    def __init__(self, mode: str = "paper"):
        self.mode = mode
        self.running = False
        self.session_id = f"trading_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # Initialize core components
        self.config_manager = ConfigManager()
        self.logging_manager = LoggingManager()
        self.event_bus = EventBus()
        
        # Initialize configuration
        self.config = self._create_config()
        
        # Initialize agents
        self.agents = {}
        self._initialize_agents()
    
    def _create_config(self) -> Any:
        """Create system configuration"""
        class Config:
            def __init__(self, mode: str, config_manager: ConfigManager):
                self.mode = self._parse_mode(mode)
                self.selected_stocks = [
                    "RELIANCE", "HDFCBANK", "TCS", "INFY", "ICICIBANK",
                    "HINDUNILVR", "ITC", "SBIN", "BHARTIARTL", "KOTAKBANK",
                    "LT", "ASIANPAINT", "MARUTI", "HCLTECH", "AXISBANK"
                ]
                
                # Trading configuration
                trading_config = config_manager.get_trading_config()
                self.initial_balance = trading_config['initial_balance']
                self.max_daily_trades = trading_config['max_daily_trades']
                
                # Risk limits
                self.risk_limits = {
                    'max_position_size': trading_config['max_position_size'],
                    'max_daily_loss': trading_config['max_daily_loss'],
                    'max_drawdown': trading_config['max_drawdown']
                }
            
            def _parse_mode(self, mode: str):
                class TradingMode:
                    def __init__(self, value: str):
                        self.value = value
                return TradingMode(mode)
        
        return Config(self.mode, self.config_manager)
    
    def _initialize_agents(self):
        """Initialize all trading agents"""
        try:
            logger.info("[INIT] Initializing trading agents...")
            
            # Market Data Agent
            self.agents['market_data'] = ModernMarketDataAgent(
                self.event_bus, self.config, self.session_id
            )
            
            # Signal Generation Agent
            self.agents['signal_generation'] = ModernSignalGenerationAgent(
                self.event_bus, self.config, self.session_id
            )
            
            # Risk Management Agent
            self.agents['risk_management'] = RiskManagementAgent(
                self.event_bus, self.config, self.session_id
            )
            
            # Execution Agent
            self.agents['execution'] = ModernExecutionAgent(
                self.event_bus, self.config, self.session_id
            )
            
            logger.info(f"[SUCCESS] Initialized {len(self.agents)} agents")
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to initialize agents: {e}")
            raise
    
    async def start(self):
        """Start the trading system"""
        try:
            logger.info(f"[START] Starting Modern Trading System in {self.mode} mode...")
            
            # Start event bus
            await self.event_bus.start_processor()
            
            # Initialize all agents
            for name, agent in self.agents.items():
                logger.info(f"[INIT] Initializing {name} agent...")
                success = await agent.initialize()
                if not success:
                    raise Exception(f"Failed to initialize {name} agent")
            
            # Start all agents
            self.running = True
            agent_tasks = []
            
            for name, agent in self.agents.items():
                logger.info(f"[START] Starting {name} agent...")
                task = asyncio.create_task(agent.start())
                agent_tasks.append(task)
            
            logger.info("[SUCCESS] All agents started successfully")
            logger.info(f"[INFO] Trading {len(self.config.selected_stocks)} stocks: {', '.join(self.config.selected_stocks[:5])}...")
            
            # Wait for all agents to complete
            await asyncio.gather(*agent_tasks, return_exceptions=True)
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to start trading system: {e}")
            await self.stop()
            raise
    
    async def stop(self):
        """Stop the trading system"""
        try:
            logger.info("[STOP] Stopping Modern Trading System...")
            
            self.running = False
            
            # Stop all agents
            for name, agent in self.agents.items():
                try:
                    await agent.stop()
                    logger.info(f"[STOP] Stopped {name} agent")
                except Exception as e:
                    logger.error(f"[ERROR] Failed to stop {name} agent: {e}")
            
            # Stop event bus
            await self.event_bus.stop_processor()
            
            logger.info("[SUCCESS] Modern Trading System stopped")
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to stop trading system: {e}")
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get system status"""
        try:
            agent_status = {}
            for name, agent in self.agents.items():
                agent_status[name] = agent.get_status()
            
            return {
                'session_id': self.session_id,
                'mode': self.mode,
                'running': self.running,
                'agents': agent_status,
                'event_bus_stats': self.event_bus.get_statistics(),
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to get system status: {e}")
            return {'error': str(e)}

async def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description='Modern Trading System')
    parser.add_argument('--mode', choices=['paper', 'live', 'demo'], 
                       default='paper', help='Trading mode')
    parser.add_argument('--log-level', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'], 
                       default='INFO', help='Logging level')
    
    args = parser.parse_args()
    
    # Setup logging
    logging.basicConfig(
        level=getattr(logging, args.log_level),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Create and start trading system
    trading_system = ModernTradingSystem(args.mode)
    
    # Setup signal handlers for graceful shutdown
    def signal_handler(signum, frame):
        logger.info(f"[SIGNAL] Received signal {signum}, shutting down...")
        asyncio.create_task(trading_system.stop())
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        await trading_system.start()
    except KeyboardInterrupt:
        logger.info("[INTERRUPT] Keyboard interrupt received")
    except Exception as e:
        logger.error(f"[ERROR] System error: {e}")
    finally:
        await trading_system.stop()

if __name__ == "__main__":
    asyncio.run(main())