#!/usr/bin/env python3
"""
Test script for Enhanced Continuous Live Trading System
Tests the multi-mode functionality and paper trading features
"""

import asyncio
import sys
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parent))

from run_continuous_live_trading import EnhancedContinuousLiveTradingSystem, TradingMode

async def test_system_initialization():
    """Test system initialization in different modes"""
    
    print("="*80)
    print("TESTING ENHANCED CONTINUOUS LIVE TRADING SYSTEM")
    print("="*80)
    
    # Test 1: Demo Mode
    print("\n[TEST 1] Testing Demo Mode Initialization...")
    try:
        demo_system = EnhancedContinuousLiveTradingSystem(
            mode="demo",
            max_daily_trades=3,
            initial_balance=50000
        )
        print(f"✅ Demo system initialized successfully")
        print(f"   Mode: {demo_system.mode.value}")
        print(f"   Max trades: {demo_system.max_daily_trades}")
        print(f"   Paper account: {'Available' if demo_system.paper_account else 'Not available'}")
        
    except Exception as e:
        print(f"❌ Demo system initialization failed: {e}")
    
    # Test 2: Paper Mode
    print("\n[TEST 2] Testing Paper Mode Initialization...")
    try:
        paper_system = EnhancedContinuousLiveTradingSystem(
            mode="paper",
            max_daily_trades=5,
            initial_balance=100000
        )
        print(f"✅ Paper system initialized successfully")
        print(f"   Mode: {paper_system.mode.value}")
        print(f"   Max trades: {paper_system.max_daily_trades}")
        print(f"   Paper account balance: ₹{paper_system.paper_account.current_balance:,.2f}")
        print(f"   Commission rate: {paper_system.paper_account.commission_rate:.4f}")
        
    except Exception as e:
        print(f"❌ Paper system initialization failed: {e}")
    
    # Test 3: Live Mode (without credentials)
    print("\n[TEST 3] Testing Live Mode Initialization...")
    try:
        live_system = EnhancedContinuousLiveTradingSystem(
            mode="live",
            max_daily_trades=2,
            initial_balance=100000
        )
        print(f"✅ Live system initialized successfully")
        print(f"   Mode: {live_system.mode.value}")
        print(f"   Max trades: {live_system.max_daily_trades}")
        print(f"   Credentials available: {live_system.credentials_available}")
        
    except Exception as e:
        print(f"❌ Live system initialization failed: {e}")
    
    print("\n" + "="*80)
    print("INITIALIZATION TESTS COMPLETED")
    print("="*80)

async def test_mock_data_generation():
    """Test mock historical data generation"""
    
    print("\n[TEST 4] Testing Mock Data Generation...")
    try:
        demo_system = EnhancedContinuousLiveTradingSystem(mode="demo")
        
        # Test mock data generation
        test_symbols = ['RELIANCE', 'TCS', 'HDFCBANK']
        await demo_system._generate_mock_historical_data(test_symbols)
        
        print(f"✅ Mock data generation completed")
        print(f"   Symbols processed: {len(test_symbols)}")
        
        # Check if data was generated
        for symbol in test_symbols:
            if symbol in demo_system.timeframe_data["1min"]:
                df = demo_system.timeframe_data["1min"][symbol]
                print(f"   {symbol}: {len(df)} data points generated")
            else:
                print(f"   {symbol}: No data generated")
        
    except Exception as e:
        print(f"❌ Mock data generation failed: {e}")

async def test_paper_trading_execution():
    """Test paper trading execution"""
    
    print("\n[TEST 5] Testing Paper Trading Execution...")
    try:
        from run_continuous_live_trading import SignalPayload
        
        paper_system = EnhancedContinuousLiveTradingSystem(mode="paper")
        
        # Create a test signal
        test_signal = SignalPayload(
            symbol="RELIANCE",
            exchange="NSE",
            symbol_token="2885",
            action="BUY",
            entry_price=2800.0,
            sl_price=2750.0,
            target_price=2900.0,
            quantity=10,
            strategy_name="TEST_STRATEGY",
            signal_id="TEST_001"
        )
        
        # Execute paper trade
        success, message, trade_execution = await paper_system.execute_paper_trade(test_signal)
        
        if success:
            print(f"✅ Paper trade executed successfully")
            print(f"   Message: {message}")
            print(f"   Balance after trade: ₹{paper_system.paper_account.balance:,.2f}")
            print(f"   Active positions: {len(paper_system.paper_account.positions)}")
        else:
            print(f"❌ Paper trade execution failed: {message}")
        
    except Exception as e:
        print(f"❌ Paper trading test failed: {e}")

async def main():
    """Run all tests"""
    try:
        await test_system_initialization()
        await test_mock_data_generation()
        await test_paper_trading_execution()
        
        print("\n" + "="*80)
        print("🎉 ALL TESTS COMPLETED")
        print("="*80)
        print("\nNext steps:")
        print("1. Set up SmartAPI credentials in .env file for real data")
        print("2. Run paper trading: python run_continuous_live_trading.py --mode paper")
        print("3. Monitor performance and adjust parameters")
        print("4. Only use live mode after thorough testing!")
        
    except Exception as e:
        print(f"❌ Test suite failed: {e}")

if __name__ == "__main__":
    asyncio.run(main())