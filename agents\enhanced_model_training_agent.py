#!/usr/bin/env python3
"""
🧠 Enhanced Model Training Agent - Optimized for Backtesting Output
Advanced ML system specifically designed to utilize enhanced backtesting results

[TARGET] Multi-Task Learning Objectives:
1. Strategy Performance Prediction: Predict strategy success metrics
2. Market Regime Classification: Classify market conditions for strategy selection
3. Risk-Adjusted Return Prediction: Predict Sharpe ratio, Sortino ratio, max drawdown
4. Trade Signal Quality: Predict signal accuracy and profitability
5. Strategy Selection: Multi-class classifier for optimal strategy selection
6. Walk-Forward Consistency: Predict strategy consistency across time periods

[FEATURES] Enhanced Capabilities:
- Optimized for Windows 10 environment
- Multi-model ensemble (LightGBM, XGBoost, CatBoost, TabNet)
- Advanced feature engineering from backtesting results
- Time-series aware cross-validation
- Model explainability with SHAP integration
- Automated hyperparameter optimization with Optuna
- Real-time prediction serving
- Model versioning and registry
- Incremental learning capabilities
"""

import os
import sys
import logging
import warnings
import asyncio
import json
import pickle
import joblib
import uuid
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Union, Any
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from collections import defaultdict

import yaml
import numpy as np
import pandas as pd
import polars as pl

# Suppress warnings
warnings.filterwarnings('ignore')

# Core ML Libraries
import lightgbm as lgb
import xgboost as xgb
import optuna
from sklearn.model_selection import (
    train_test_split, KFold, TimeSeriesSplit, 
    cross_val_score, StratifiedKFold
)
from sklearn.preprocessing import (
    StandardScaler, RobustScaler, LabelEncoder, 
    MinMaxScaler, QuantileTransformer
)
from sklearn.metrics import (
    mean_squared_error, mean_absolute_error, r2_score,
    accuracy_score, precision_score, recall_score, f1_score,
    classification_report, confusion_matrix, roc_auc_score,
    mean_absolute_percentage_error
)
from sklearn.ensemble import (
    RandomForestRegressor, RandomForestClassifier,
    VotingRegressor, VotingClassifier
)
from sklearn.neural_network import MLPRegressor, MLPClassifier
from sklearn.linear_model import Ridge, LogisticRegression

# Advanced ML Libraries
try:
    from catboost import CatBoostRegressor, CatBoostClassifier
    CATBOOST_AVAILABLE = True
except ImportError:
    CATBOOST_AVAILABLE = False
    print("Warning: CatBoost not available")

try:
    import torch
    import torch.nn as nn
    import torch.optim as optim
    from pytorch_tabnet.tab_model import TabNetRegressor, TabNetClassifier
    TABNET_AVAILABLE = True
except ImportError:
    TABNET_AVAILABLE = False
    print("Warning: TabNet not available")

# Model Explainability
try:
    import shap
    SHAP_AVAILABLE = True
except ImportError:
    SHAP_AVAILABLE = False
    print("Warning: SHAP not available")

# Feature Engineering
from scipy import stats
from scipy.stats import zscore

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# ═══════════════════════════════════════════════════════════════════════════════
# [CONFIG] CONFIGURATION SECTION
# ═══════════════════════════════════════════════════════════════════════════════

@dataclass
class EnhancedTrainingConfig:
    """Enhanced Configuration for Backtesting-Optimized Model Training"""
    
    # Data Configuration
    data_dir: str = "data/backtest_improved"
    input_file: str = "enhanced_strategy_results.parquet"
    models_dir: str = "data/models/enhanced"
    registry_dir: str = "data/models/registry/enhanced"
    
    # Target Prediction Tasks
    prediction_tasks: Dict[str, Dict[str, Any]] = field(default_factory=dict)
    
    # Feature Engineering
    feature_engineering_enabled: bool = True
    create_interaction_features: bool = True
    create_polynomial_features: bool = True
    polynomial_degree: int = 2
    create_lag_features: bool = True
    lag_periods: List[int] = field(default_factory=lambda: [1, 2, 3, 5, 10])
    
    # Model Configuration
    enabled_models: List[str] = field(default_factory=lambda: ["lightgbm", "xgboost"])
    ensemble_method: str = "weighted_average"  # "voting", "stacking", "weighted_average"
    
    # Training Configuration
    test_size: float = 0.2
    validation_size: float = 0.2
    random_state: int = 42
    cv_folds: int = 5
    cv_strategy: str = "time_series"  # "kfold", "time_series", "stratified"
    
    # Hyperparameter Optimization
    optuna_enabled: bool = True
    optuna_trials: int = 100
    optuna_timeout: int = 1800  # 30 minutes
    
    # Model-specific parameters
    lgb_params: Dict[str, Any] = field(default_factory=dict)
    xgb_params: Dict[str, Any] = field(default_factory=dict)
    catboost_params: Dict[str, Any] = field(default_factory=dict)
    tabnet_params: Dict[str, Any] = field(default_factory=dict)
    
    # Feature Selection
    feature_selection_enabled: bool = True
    feature_importance_threshold: float = 0.001
    max_features: int = 100
    
    # Model Explainability
    shap_enabled: bool = SHAP_AVAILABLE
    generate_feature_importance: bool = True
    
    # Model Registry
    model_versioning: bool = True
    auto_save_models: bool = True
    max_model_versions: int = 5
    
    # Performance Thresholds
    min_r2_score: float = 0.1
    min_accuracy: float = 0.55
    min_auc_score: float = 0.6
    
    # Hardware Configuration
    n_jobs: int = -1
    use_gpu: bool = False  # Set based on availability
    
    def __post_init__(self):
        """Initialize default configurations"""
        
        # Check GPU availability
        if torch.cuda.is_available():
            self.use_gpu = True
            logger.info("GPU acceleration available")
        
        # Initialize prediction tasks (single target per task)
        if not self.prediction_tasks:
            self.prediction_tasks = {
                "sharpe_ratio_prediction": {
                    "type": "regression",
                    "target_columns": ["avg_sharpe_ratio"],
                    "description": "Predict Sharpe ratio",
                    "weight": 0.20
                },
                "roi_prediction": {
                    "type": "regression",
                    "target_columns": ["avg_roi"],
                    "description": "Predict ROI",
                    "weight": 0.20
                },
                "profit_factor_prediction": {
                    "type": "regression",
                    "target_columns": ["avg_profit_factor"],
                    "description": "Predict profit factor",
                    "weight": 0.15
                },
                "drawdown_prediction": {
                    "type": "regression",
                    "target_columns": ["avg_max_drawdown"],
                    "description": "Predict maximum drawdown",
                    "weight": 0.15
                },
                "profitability_classification": {
                    "type": "classification",
                    "target_columns": ["passes_risk_criteria"],
                    "description": "Classify profitable strategies",
                    "weight": 0.30
                }
            }
        
        # Initialize model parameters
        if not self.lgb_params:
            self.lgb_params = {
                "objective": "regression",
                "metric": "rmse",
                "boosting_type": "gbdt",
                "num_leaves": 31,
                "learning_rate": 0.05,
                "feature_fraction": 0.9,
                "bagging_fraction": 0.8,
                "bagging_freq": 5,
                "verbose": -1,
                "random_state": self.random_state,
                "n_jobs": self.n_jobs
            }
        
        if not self.xgb_params:
            self.xgb_params = {
                "objective": "reg:squarederror",
                "eval_metric": "rmse",
                "max_depth": 6,
                "learning_rate": 0.05,
                "n_estimators": 1000,
                "subsample": 0.8,
                "colsample_bytree": 0.9,
                "random_state": self.random_state,
                "n_jobs": self.n_jobs,
                "tree_method": "gpu_hist" if self.use_gpu else "hist"
            }
        
        if CATBOOST_AVAILABLE and not self.catboost_params:
            self.catboost_params = {
                "iterations": 1000,
                "learning_rate": 0.05,
                "depth": 6,
                "l2_leaf_reg": 3,
                "random_seed": self.random_state,
                "verbose": False,
                "task_type": "GPU" if self.use_gpu else "CPU"
            }
        
        if TABNET_AVAILABLE and not self.tabnet_params:
            self.tabnet_params = {
                "n_d": 32,
                "n_a": 32,
                "n_steps": 3,
                "gamma": 1.3,
                "lambda_sparse": 1e-3,
                "optimizer_fn": torch.optim.Adam,
                "optimizer_params": {"lr": 2e-2},
                "mask_type": "sparsemax",
                "scheduler_params": {"step_size": 10, "gamma": 0.9},
                "scheduler_fn": torch.optim.lr_scheduler.StepLR,
                "verbose": 0,
                "device_name": "cuda" if self.use_gpu else "cpu"
            }
        
        # Add CatBoost to enabled models if available
        if CATBOOST_AVAILABLE and "catboost" not in self.enabled_models:
            self.enabled_models.append("catboost")
        
        # Add TabNet to enabled models if available
        if TABNET_AVAILABLE and "tabnet" not in self.enabled_models:
            self.enabled_models.append("tabnet")

class EnhancedModelTrainingAgent:
    """Enhanced Model Training Agent optimized for backtesting results"""
    
    def __init__(self, config: EnhancedTrainingConfig = None):
        self.config = config or EnhancedTrainingConfig()
        self.models = {}
        self.ensemble_models = {}
        self.feature_importance = {}
        self.scalers = {}
        self.label_encoders = {}
        self.training_history = []
        
        # Create directories
        Path(self.config.models_dir).mkdir(parents=True, exist_ok=True)
        Path(self.config.registry_dir).mkdir(parents=True, exist_ok=True)
        
        logger.info("Enhanced Model Training Agent initialized")
    
    # ═══════════════════════════════════════════════════════════════════════════════
    # [DATA] DATA LOADING AND PREPROCESSING
    # ═══════════════════════════════════════════════════════════════════════════════
    
    async def load_backtesting_data(self, file_path: str = None) -> pl.DataFrame:
        """Load and preprocess backtesting results data"""
        
        if file_path is None:
            file_path = Path(self.config.data_dir) / self.config.input_file
        
        try:
            logger.info(f"Loading backtesting data from {file_path}")
            
            if not Path(file_path).exists():
                raise FileNotFoundError(f"Data file not found: {file_path}")
            
            # Load data
            if str(file_path).endswith('.parquet'):
                df = pl.read_parquet(file_path)
            elif str(file_path).endswith('.csv'):
                df = pl.read_csv(file_path)
            else:
                raise ValueError(f"Unsupported file format: {file_path}")
            
            logger.info(f"Loaded {df.height} rows, {df.width} columns")
            
            # Basic data validation
            if df.height == 0:
                raise ValueError("Empty dataset")
            
            # Preprocess data
            df = await self._preprocess_backtesting_data(df)
            
            logger.info(f"Preprocessed data: {df.height} rows, {df.width} columns")
            return df
            
        except Exception as e:
            logger.error(f"Failed to load backtesting data: {e}")
            raise
    
    async def _preprocess_backtesting_data(self, df: pl.DataFrame) -> pl.DataFrame:
        """Preprocess backtesting data for model training"""
        
        try:
            logger.info("Preprocessing backtesting data...")
            
            # Handle missing values
            df = self._handle_missing_values(df)
            
            # Create derived features
            if self.config.feature_engineering_enabled:
                df = self._create_derived_features(df)
            
            # Remove outliers
            df = self._remove_outliers(df)
            
            # Create target variables
            df = self._create_target_variables(df)
            
            return df
            
        except Exception as e:
            logger.error(f"Error in data preprocessing: {e}")
            raise
    
    def _handle_missing_values(self, df: pl.DataFrame) -> pl.DataFrame:
        """Handle missing values in the dataset"""
        
        # Fill numeric columns with median
        numeric_cols = df.select(pl.col(pl.NUMERIC_DTYPES)).columns
        for col in numeric_cols:
            median_val = df[col].median()
            df = df.with_columns(pl.col(col).fill_null(median_val))
        
        # Fill string columns with mode or 'unknown'
        string_cols = df.select(pl.col(pl.Utf8)).columns
        for col in string_cols:
            mode_val = df[col].mode().first()
            if mode_val is None:
                mode_val = 'unknown'
            df = df.with_columns(pl.col(col).fill_null(mode_val))
        
        return df
    
    def _create_derived_features(self, df: pl.DataFrame) -> pl.DataFrame:
        """Create derived features from backtesting results"""
        
        logger.info("Creating derived features...")
        
        # Performance ratios
        if all(col in df.columns for col in ['avg_win', 'avg_loss']):
            df = df.with_columns(
                (pl.col('avg_win') / pl.col('avg_loss').abs()).alias('win_loss_ratio')
            )
        
        # Risk-adjusted returns
        if all(col in df.columns for col in ['avg_roi', 'avg_max_drawdown']):
            df = df.with_columns(
                (pl.col('avg_roi') / pl.col('avg_max_drawdown').abs()).alias('roi_drawdown_ratio')
            )
        
        # Consistency metrics
        if all(col in df.columns for col in ['avg_sharpe_ratio', 'std_sharpe_ratio']):
            df = df.with_columns(
                (pl.col('avg_sharpe_ratio') / (pl.col('std_sharpe_ratio') + 1e-6)).alias('sharpe_consistency')
            )
        
        # Trade frequency features
        if all(col in df.columns for col in ['avg_total_trades', 'walk_forward_steps']):
            df = df.with_columns(
                (pl.col('avg_total_trades') / pl.col('walk_forward_steps')).alias('trades_per_period')
            )
        
        # Create interaction features if enabled
        if self.config.create_interaction_features:
            df = self._create_interaction_features(df)
        
        # Create lag features if enabled
        if self.config.create_lag_features:
            df = self._create_lag_features(df)
        
        return df
    
    def _create_interaction_features(self, df: pl.DataFrame) -> pl.DataFrame:
        """Create interaction features between important variables"""
        
        # Key feature pairs for interaction
        interaction_pairs = [
            ('avg_sharpe_ratio', 'consistency_score'),
            ('avg_roi', 'avg_accuracy'),
            ('avg_profit_factor', 'avg_max_drawdown'),
            ('avg_total_trades', 'avg_accuracy')
        ]
        
        for col1, col2 in interaction_pairs:
            if col1 in df.columns and col2 in df.columns:
                df = df.with_columns(
                    (pl.col(col1) * pl.col(col2)).alias(f'{col1}_{col2}_interaction')
                )
        
        return df
    
    def _create_lag_features(self, df: pl.DataFrame) -> pl.DataFrame:
        """Create lag features for time series analysis"""
        
        # Sort by strategy name and some time identifier if available
        if 'strategy_name' in df.columns:
            try:
                df = df.sort(['strategy_name'])
                
                # Create lag features for key metrics
                lag_columns = ['avg_sharpe_ratio', 'avg_roi', 'consistency_score']
                
                for col in lag_columns:
                    if col in df.columns:
                        for lag in self.config.lag_periods:
                            try:
                                df = df.with_columns(
                                    pl.col(col).shift(lag).over('strategy_name').alias(f'{col}_lag_{lag}')
                                )
                            except Exception as e:
                                logger.warning(f"Failed to create lag feature {col}_lag_{lag}: {e}")
                                continue
            except Exception as e:
                logger.warning(f"Error creating lag features: {e}")
        
        return df
    
    def _remove_outliers(self, df: pl.DataFrame) -> pl.DataFrame:
        """Remove outliers using IQR method"""
        
        numeric_cols = df.select(pl.col(pl.NUMERIC_DTYPES)).columns
        
        for col in numeric_cols:
            if col in df.columns:
                try:
                    q1 = df[col].quantile(0.25)
                    q3 = df[col].quantile(0.75)
                    
                    # Check if quantiles are None (can happen with all-null columns)
                    if q1 is None or q3 is None:
                        logger.warning(f"Skipping outlier removal for column {col} (null quantiles)")
                        continue
                    
                    iqr = q3 - q1
                    
                    # Skip if IQR is 0 (all values are the same)
                    if iqr == 0:
                        logger.debug(f"Skipping outlier removal for column {col} (IQR = 0)")
                        continue
                    
                    lower_bound = q1 - 1.5 * iqr
                    upper_bound = q3 + 1.5 * iqr
                    
                    df = df.filter(
                        (pl.col(col) >= lower_bound) & (pl.col(col) <= upper_bound)
                    )
                except Exception as e:
                    logger.warning(f"Error removing outliers for column {col}: {e}")
                    continue
        
        return df
    
    def _create_target_variables(self, df: pl.DataFrame) -> pl.DataFrame:
        """Create target variables for different prediction tasks"""
        
        # Create binary profitability target
        if 'passes_risk_criteria' not in df.columns:
            if all(col in df.columns for col in ['avg_sharpe_ratio', 'avg_profit_factor', 'avg_max_drawdown']):
                df = df.with_columns(
                    (
                        (pl.col('avg_sharpe_ratio') >= 1.0) &
                        (pl.col('avg_profit_factor') >= 1.2) &
                        (pl.col('avg_max_drawdown').abs() <= 10.0)
                    ).alias('passes_risk_criteria')
                )
        
        # Create regime suitability target
        if 'best_regime' not in df.columns and 'regime_performance' in df.columns:
            # This would need to be extracted from the regime_performance dict
            # For now, create a placeholder
            df = df.with_columns(pl.lit('trending_up').alias('best_regime'))
        
        return df
    
    # ═══════════════════════════════════════════════════════════════════════════════
    # [TRAINING] MODEL TRAINING AND OPTIMIZATION
    # ═══════════════════════════════════════════════════════════════════════════════
    
    async def train_all_models(self, df: pl.DataFrame) -> Dict[str, Any]:
        """Train models for all prediction tasks"""
        
        logger.info("Starting comprehensive model training...")
        
        training_results = {}
        
        for task_name, task_config in self.config.prediction_tasks.items():
            logger.info(f"Training models for task: {task_name}")
            
            try:
                # Prepare data for this task
                task_data = self._prepare_task_data(df, task_config)
                
                if task_data is None:
                    logger.warning(f"No valid data for task: {task_name}")
                    continue
                
                # Train models for this task
                task_results = await self._train_task_models(task_name, task_data, task_config)
                training_results[task_name] = task_results
                
                logger.info(f"Completed training for task: {task_name}")
                
            except Exception as e:
                logger.error(f"Failed to train task {task_name}: {e}")
                training_results[task_name] = {"error": str(e)}
        
        # Create ensemble models
        if len(training_results) > 1:
            logger.info("Creating ensemble models...")
            ensemble_results = await self._create_ensemble_models(training_results)
            training_results["ensemble"] = ensemble_results
        
        # Save training results
        await self._save_training_results(training_results)
        
        logger.info("Model training completed successfully!")
        return training_results
    
    def _prepare_task_data(self, df: pl.DataFrame, task_config: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Prepare data for a specific prediction task"""
        
        target_columns = task_config["target_columns"]
        
        # Check if target columns exist
        available_targets = [col for col in target_columns if col in df.columns]
        if not available_targets:
            logger.warning(f"No target columns available: {target_columns}")
            return None
        
        # Select features (exclude target columns)
        feature_columns = [col for col in df.columns if col not in target_columns]
        feature_columns = [col for col in feature_columns if df[col].dtype in [pl.Float64, pl.Float32, pl.Int64, pl.Int32]]
        
        if len(feature_columns) == 0:
            logger.warning("No valid feature columns found")
            return None
        
        # Filter out rows with null targets
        valid_df = df.filter(
            pl.all_horizontal([pl.col(col).is_not_null() for col in available_targets])
        )
        
        if valid_df.height == 0:
            logger.warning("No valid rows after filtering nulls")
            return None
        
        # Convert to pandas for sklearn compatibility
        X = valid_df.select(feature_columns).to_pandas()
        y = valid_df.select(available_targets).to_pandas()
        
        # Handle NaN values in features
        X = X.fillna(X.median())  # Fill NaN with median for numeric features
        
        # Remove any remaining NaN rows
        nan_mask = X.isna().any(axis=1) | y.isna().any(axis=1)
        if nan_mask.any():
            logger.warning(f"Removing {nan_mask.sum()} rows with NaN values")
            X = X[~nan_mask]
            y = y[~nan_mask]
        
        if len(X) == 0:
            logger.warning("No valid data after NaN removal")
            return None
        
        # Always handle single target at a time
        # For multi-target tasks, we'll train separate models for each target
        if len(available_targets) > 1:
            # For now, just use the first target
            logger.info(f"Multi-target task detected, using first target: {available_targets[0]}")
            y = y.iloc[:, 0]
            available_targets = [available_targets[0]]
        else:
            y = y.iloc[:, 0]  # Convert to Series for single target
        
        return {
            "X": X,
            "y": y,
            "feature_names": feature_columns,
            "target_names": available_targets,
            "n_samples": len(X)
        }
    
    async def _train_task_models(self, task_name: str, task_data: Dict[str, Any], task_config: Dict[str, Any]) -> Dict[str, Any]:
        """Train all enabled models for a specific task"""
        
        X = task_data["X"]
        y = task_data["y"]
        task_type = task_config["type"]
        
        # Create train/test split
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=self.config.test_size, 
            random_state=self.config.random_state,
            stratify=y if task_type == "classification" and len(np.unique(y)) > 1 else None
        )
        
        # Scale features while preserving column names
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = scaler.transform(X_test)
        
        # Convert back to DataFrame to preserve column names
        X_train_scaled = pd.DataFrame(X_train_scaled, columns=X_train.columns, index=X_train.index)
        X_test_scaled = pd.DataFrame(X_test_scaled, columns=X_test.columns, index=X_test.index)
        
        # Store scaler
        self.scalers[task_name] = scaler
        
        # Handle label encoding for classification
        if task_type == "classification":
            le = LabelEncoder()
            y_train = le.fit_transform(y_train)
            y_test = le.transform(y_test)
            self.label_encoders[task_name] = le
        
        task_models = {}
        task_results = {}
        
        for model_name in self.config.enabled_models:
            logger.info(f"  Training {model_name} for {task_name}...")
            
            try:
                # Train model with hyperparameter optimization
                model_result = await self._train_single_model(
                    model_name, X_train_scaled, X_test_scaled, y_train, y_test, task_type
                )
                
                task_models[model_name] = model_result["model"]
                task_results[model_name] = model_result["metrics"]
                
                logger.info(f"  {model_name} completed - Score: {model_result['metrics'].get('test_score', 'N/A'):.4f}")
                
            except Exception as e:
                logger.error(f"  Failed to train {model_name}: {e}")
                task_results[model_name] = {"error": str(e)}
        
        # Store models
        self.models[task_name] = task_models
        
        # Generate feature importance
        if self.config.generate_feature_importance:
            feature_importance = self._generate_feature_importance(task_models, task_data["feature_names"])
            self.feature_importance[task_name] = feature_importance
            task_results["feature_importance"] = feature_importance
        
        return task_results
    
    async def _train_single_model(self, model_name: str, X_train: np.ndarray, X_test: np.ndarray, 
                                 y_train: np.ndarray, y_test: np.ndarray, task_type: str) -> Dict[str, Any]:
        """Train a single model with hyperparameter optimization"""
        
        if self.config.optuna_enabled:
            # Hyperparameter optimization with Optuna
            study = optuna.create_study(direction='maximize')
            
            def objective(trial):
                return self._optuna_objective(trial, model_name, X_train, y_train, task_type)
            
            study.optimize(objective, n_trials=self.config.optuna_trials, timeout=self.config.optuna_timeout)
            best_params = study.best_params
        else:
            best_params = self._get_default_params(model_name, task_type)
        
        # Train final model with best parameters
        model = self._create_model(model_name, task_type, best_params)
        
        # Ensure feature names are preserved for LightGBM models
        if model_name == "lightgbm" and hasattr(X_train, 'columns'):
            # LightGBM can accept feature names directly
            model.fit(X_train, y_train, feature_name=list(X_train.columns))
        else:
            model.fit(X_train, y_train)
        
        # Evaluate model
        train_score = model.score(X_train, y_train)
        test_score = model.score(X_test, y_test)
        
        # Generate predictions for detailed metrics
        y_pred = model.predict(X_test)
        
        # Calculate detailed metrics
        metrics = self._calculate_detailed_metrics(y_test, y_pred, task_type)
        metrics.update({
            "train_score": train_score,
            "test_score": test_score,
            "best_params": best_params
        })
        
        return {
            "model": model,
            "metrics": metrics
        }
    
    def _optuna_objective(self, trial, model_name: str, X_train: np.ndarray, y_train: np.ndarray, task_type: str) -> float:
        """Optuna objective function for hyperparameter optimization"""
        
        # Define hyperparameter search space
        if model_name == "lightgbm":
            params = {
                "n_estimators": trial.suggest_int("n_estimators", 100, 1000),
                "max_depth": trial.suggest_int("max_depth", 3, 10),
                "learning_rate": trial.suggest_float("learning_rate", 0.01, 0.3),
                "num_leaves": trial.suggest_int("num_leaves", 10, 100),
                "feature_fraction": trial.suggest_float("feature_fraction", 0.5, 1.0),
                "bagging_fraction": trial.suggest_float("bagging_fraction", 0.5, 1.0),
            }
        elif model_name == "xgboost":
            params = {
                "n_estimators": trial.suggest_int("n_estimators", 100, 1000),
                "max_depth": trial.suggest_int("max_depth", 3, 10),
                "learning_rate": trial.suggest_float("learning_rate", 0.01, 0.3),
                "subsample": trial.suggest_float("subsample", 0.5, 1.0),
                "colsample_bytree": trial.suggest_float("colsample_bytree", 0.5, 1.0),
            }
        elif model_name == "catboost" and CATBOOST_AVAILABLE:
            params = {
                "iterations": trial.suggest_int("iterations", 100, 1000),
                "depth": trial.suggest_int("depth", 3, 10),
                "learning_rate": trial.suggest_float("learning_rate", 0.01, 0.3),
                "l2_leaf_reg": trial.suggest_float("l2_leaf_reg", 1, 10),
            }
        else:
            params = {}
        
        # Create and train model
        model = self._create_model(model_name, task_type, params)
        
        # Cross-validation score with adaptive CV folds
        n_samples = len(X_train)
        cv_folds = min(3, max(2, n_samples // 5))  # Adaptive CV folds based on sample size
        
        if cv_folds < 2:
            # If too few samples, use simple train-test split
            from sklearn.model_selection import train_test_split
            X_train_cv, X_val_cv, y_train_cv, y_val_cv = train_test_split(
                X_train, y_train, test_size=0.3, random_state=42
            )
            # Preserve feature names in cross-validation too
            if model_name == "lightgbm" and hasattr(X_train_cv, 'columns'):
                model.fit(X_train_cv, y_train_cv, feature_name=list(X_train_cv.columns))
            else:
                model.fit(X_train_cv, y_train_cv)
            if task_type == 'classification':
                from sklearn.metrics import accuracy_score
                y_pred = model.predict(X_val_cv)
                return accuracy_score(y_val_cv, y_pred)
            else:
                from sklearn.metrics import r2_score
                y_pred = model.predict(X_val_cv)
                return r2_score(y_val_cv, y_pred)
        else:
            cv_scores = cross_val_score(model, X_train, y_train, cv=cv_folds, scoring='accuracy' if task_type == 'classification' else 'r2')
            return cv_scores.mean()
    
    def _create_model(self, model_name: str, task_type: str, params: Dict[str, Any]):
        """Create a model instance with given parameters"""
        
        base_params = self._get_default_params(model_name, task_type)
        base_params.update(params)
        
        if model_name == "lightgbm":
            if task_type == "classification":
                base_params["objective"] = "binary" if len(np.unique(params.get("y", [0, 1]))) == 2 else "multiclass"
                return lgb.LGBMClassifier(**base_params)
            else:
                return lgb.LGBMRegressor(**base_params)
                
        elif model_name == "xgboost":
            if task_type == "classification":
                base_params["objective"] = "binary:logistic" if len(np.unique(params.get("y", [0, 1]))) == 2 else "multi:softprob"
                return xgb.XGBClassifier(**base_params)
            else:
                return xgb.XGBRegressor(**base_params)
                
        elif model_name == "catboost" and CATBOOST_AVAILABLE:
            if task_type == "classification":
                return CatBoostClassifier(**base_params)
            else:
                return CatBoostRegressor(**base_params)
                
        elif model_name == "tabnet" and TABNET_AVAILABLE:
            if task_type == "classification":
                return TabNetClassifier(**base_params)
            else:
                return TabNetRegressor(**base_params)
        
        else:
            # Fallback to sklearn models
            if task_type == "classification":
                return RandomForestClassifier(random_state=self.config.random_state)
            else:
                return RandomForestRegressor(random_state=self.config.random_state)
    
    def _get_default_params(self, model_name: str, task_type: str) -> Dict[str, Any]:
        """Get default parameters for a model"""
        
        if model_name == "lightgbm":
            return self.config.lgb_params.copy()
        elif model_name == "xgboost":
            return self.config.xgb_params.copy()
        elif model_name == "catboost":
            return self.config.catboost_params.copy()
        elif model_name == "tabnet":
            return self.config.tabnet_params.copy()
        else:
            return {}
    
    def _calculate_detailed_metrics(self, y_true: np.ndarray, y_pred: np.ndarray, task_type: str) -> Dict[str, float]:
        """Calculate detailed performance metrics"""
        
        metrics = {}
        
        if task_type == "classification":
            metrics["accuracy"] = accuracy_score(y_true, y_pred)
            metrics["precision"] = precision_score(y_true, y_pred, average='weighted', zero_division=0)
            metrics["recall"] = recall_score(y_true, y_pred, average='weighted', zero_division=0)
            metrics["f1_score"] = f1_score(y_true, y_pred, average='weighted', zero_division=0)
            
            # AUC for binary classification
            if len(np.unique(y_true)) == 2:
                try:
                    metrics["auc"] = roc_auc_score(y_true, y_pred)
                except:
                    metrics["auc"] = 0.0
        else:
            metrics["r2_score"] = r2_score(y_true, y_pred)
            metrics["mse"] = mean_squared_error(y_true, y_pred)
            metrics["mae"] = mean_absolute_error(y_true, y_pred)
            metrics["rmse"] = np.sqrt(metrics["mse"])
            
            # MAPE (avoiding division by zero)
            try:
                metrics["mape"] = mean_absolute_percentage_error(y_true, y_pred)
            except:
                metrics["mape"] = float('inf')
        
        return metrics
    
    def _generate_feature_importance(self, models: Dict[str, Any], feature_names: List[str]) -> Dict[str, Any]:
        """Generate feature importance from trained models"""
        
        importance_dict = {}
        
        for model_name, model in models.items():
            try:
                if hasattr(model, 'feature_importances_'):
                    importance = model.feature_importances_
                elif hasattr(model, 'coef_'):
                    importance = np.abs(model.coef_).flatten()
                else:
                    continue
                
                # Create feature importance dataframe
                feature_importance_df = pd.DataFrame({
                    'feature': feature_names,
                    'importance': importance
                }).sort_values('importance', ascending=False)
                
                importance_dict[model_name] = feature_importance_df.to_dict('records')
                
            except Exception as e:
                logger.warning(f"Could not extract feature importance for {model_name}: {e}")
        
        return importance_dict
    
    async def _create_ensemble_models(self, training_results: Dict[str, Any]) -> Dict[str, Any]:
        """Create ensemble models from individual task models"""
        
        ensemble_results = {}
        
        for task_name, task_results in training_results.items():
            if "error" in task_results:
                continue
            
            try:
                task_models = self.models.get(task_name, {})
                if len(task_models) < 2:
                    continue
                
                # Create ensemble based on configuration
                if self.config.ensemble_method == "voting":
                    ensemble_model = self._create_voting_ensemble(task_models, task_name)
                elif self.config.ensemble_method == "weighted_average":
                    ensemble_model = self._create_weighted_ensemble(task_models, task_results)
                else:
                    continue
                
                self.ensemble_models[task_name] = ensemble_model
                ensemble_results[task_name] = {"created": True, "n_models": len(task_models)}
                
            except Exception as e:
                logger.error(f"Failed to create ensemble for {task_name}: {e}")
                ensemble_results[task_name] = {"error": str(e)}
        
        return ensemble_results
    
    def _create_voting_ensemble(self, models: Dict[str, Any], task_name: str):
        """Create voting ensemble"""
        
        task_config = self.config.prediction_tasks[task_name]
        
        if task_config["type"] == "classification":
            return VotingClassifier(
                estimators=[(name, model) for name, model in models.items()],
                voting='soft'
            )
        else:
            return VotingRegressor(
                estimators=[(name, model) for name, model in models.items()]
            )
    
    def _create_weighted_ensemble(self, models: Dict[str, Any], task_results: Dict[str, Any]):
        """Create weighted ensemble based on model performance"""
        
        # Calculate weights based on performance
        weights = {}
        for model_name, model in models.items():
            if model_name in task_results:
                score = task_results[model_name].get("test_score", 0)
                weights[model_name] = max(0, score)  # Ensure non-negative weights
        
        # Normalize weights
        total_weight = sum(weights.values())
        if total_weight > 0:
            weights = {k: v/total_weight for k, v in weights.items()}
        else:
            weights = {k: 1/len(models) for k in models.keys()}
        
        # Store weights for prediction
        return {
            "models": models,
            "weights": weights,
            "type": "weighted_average"
        }
    
    # ═══════════════════════════════════════════════════════════════════════════════
    # [PREDICTION] MODEL PREDICTION AND SERVING
    # ═══════════════════════════════════════════════════════════════════════════════
    
    async def predict(self, data: Union[pd.DataFrame, np.ndarray], task_name: str, use_ensemble: bool = True) -> Dict[str, Any]:
        """Make predictions using trained models"""
        
        try:
            # Prepare data
            if isinstance(data, pd.DataFrame):
                X = data.values
            else:
                X = data
            
            # Scale features
            if task_name in self.scalers:
                X = self.scalers[task_name].transform(X)
            
            predictions = {}
            
            if use_ensemble and task_name in self.ensemble_models:
                # Use ensemble model
                ensemble_model = self.ensemble_models[task_name]
                
                if isinstance(ensemble_model, dict) and ensemble_model["type"] == "weighted_average":
                    # Weighted average ensemble
                    weighted_preds = []
                    for model_name, model in ensemble_model["models"].items():
                        pred = model.predict(X)
                        weight = ensemble_model["weights"][model_name]
                        weighted_preds.append(pred * weight)
                    
                    predictions["ensemble"] = np.sum(weighted_preds, axis=0)
                else:
                    # Voting ensemble
                    predictions["ensemble"] = ensemble_model.predict(X)
            
            # Individual model predictions
            if task_name in self.models:
                for model_name, model in self.models[task_name].items():
                    predictions[model_name] = model.predict(X)
            
            # Handle label decoding for classification
            task_config = self.config.prediction_tasks[task_name]
            if task_config["type"] == "classification" and task_name in self.label_encoders:
                le = self.label_encoders[task_name]
                for key, pred in predictions.items():
                    predictions[key] = le.inverse_transform(pred.astype(int))
            
            return predictions
            
        except Exception as e:
            logger.error(f"Prediction failed for task {task_name}: {e}")
            return {"error": str(e)}
    
    # ═══════════════════════════════════════════════════════════════════════════════
    # [PERSISTENCE] MODEL SAVING AND LOADING
    # ═══════════════════════════════════════════════════════════════════════════════
    
    async def _save_training_results(self, training_results: Dict[str, Any]):
        """Save training results and models"""
        
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # Save models
            for task_name, models in self.models.items():
                task_dir = Path(self.config.models_dir) / task_name
                task_dir.mkdir(exist_ok=True)
                
                for model_name, model in models.items():
                    model_path = task_dir / f"{model_name}_{timestamp}.joblib"
                    joblib.dump(model, model_path)
            
            # Save scalers
            scalers_path = Path(self.config.models_dir) / f"scalers_{timestamp}.joblib"
            joblib.dump(self.scalers, scalers_path)
            
            # Save label encoders
            if self.label_encoders:
                encoders_path = Path(self.config.models_dir) / f"label_encoders_{timestamp}.joblib"
                joblib.dump(self.label_encoders, encoders_path)
            
            # Save training results
            results_path = Path(self.config.models_dir) / f"training_results_{timestamp}.json"
            with open(results_path, 'w') as f:
                json.dump(training_results, f, indent=2, default=str)
            
            # Save feature importance
            if self.feature_importance:
                importance_path = Path(self.config.models_dir) / f"feature_importance_{timestamp}.json"
                with open(importance_path, 'w') as f:
                    json.dump(self.feature_importance, f, indent=2, default=str)
            
            logger.info(f"Training results saved with timestamp: {timestamp}")
            
        except Exception as e:
            logger.error(f"Failed to save training results: {e}")
    
    async def load_models(self, timestamp: str = None):
        """Load trained models"""
        
        try:
            if timestamp is None:
                # Find latest timestamp
                model_files = list(Path(self.config.models_dir).glob("scalers_*.joblib"))
                if not model_files:
                    raise FileNotFoundError("No saved models found")
                
                latest_file = max(model_files, key=lambda x: x.stat().st_mtime)
                timestamp = latest_file.stem.split('_', 1)[1]
            
            # Load scalers
            scalers_path = Path(self.config.models_dir) / f"scalers_{timestamp}.joblib"
            if scalers_path.exists():
                self.scalers = joblib.load(scalers_path)
            
            # Load label encoders
            encoders_path = Path(self.config.models_dir) / f"label_encoders_{timestamp}.joblib"
            if encoders_path.exists():
                self.label_encoders = joblib.load(encoders_path)
            
            # Load models
            for task_name in self.config.prediction_tasks.keys():
                task_dir = Path(self.config.models_dir) / task_name
                if not task_dir.exists():
                    continue
                
                task_models = {}
                for model_file in task_dir.glob(f"*_{timestamp}.joblib"):
                    model_name = model_file.stem.rsplit('_', 1)[0]
                    task_models[model_name] = joblib.load(model_file)
                
                if task_models:
                    self.models[task_name] = task_models
            
            logger.info(f"Models loaded successfully (timestamp: {timestamp})")
            
        except Exception as e:
            logger.error(f"Failed to load models: {e}")
            raise

# ═══════════════════════════════════════════════════════════════════════════════
# [MAIN] MAIN EXECUTION FUNCTION
# ═══════════════════════════════════════════════════════════════════════════════

async def main():
    """Main execution function for testing"""
    
    try:
        # Initialize agent
        config = EnhancedTrainingConfig()
        agent = EnhancedModelTrainingAgent(config)
        
        # Load backtesting data
        data_path = Path("data/backtest_improved/enhanced_strategy_results.parquet")
        if not data_path.exists():
            logger.error(f"Data file not found: {data_path}")
            return
        
        df = await agent.load_backtesting_data(str(data_path))
        
        # Train models
        results = await agent.train_all_models(df)
        
        # Print results summary
        print("\n" + "="*80)
        print("TRAINING RESULTS SUMMARY")
        print("="*80)
        
        for task_name, task_results in results.items():
            if "error" in task_results:
                print(f"\n{task_name}: ERROR - {task_results['error']}")
                continue
            
            print(f"\n{task_name.upper()}:")
            for model_name, metrics in task_results.items():
                if isinstance(metrics, dict) and "test_score" in metrics:
                    print(f"  {model_name}: {metrics['test_score']:.4f}")
        
        print("\n" + "="*80)
        
    except Exception as e:
        logger.error(f"Main execution failed: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(main())