#!/usr/bin/env python3
"""
Complete System Setup Script
Automated setup for the Enhanced Backtesting + ML Training System
"""

import os
import sys
import subprocess
import logging
from pathlib import Path
import platform

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CompleteSystemSetup:
    """Setup manager for the complete trading system"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.python_executable = sys.executable
        
    def check_system_requirements(self):
        """Check system requirements"""
        logger.info("Checking system requirements...")
        
        # Check Python version
        python_version = sys.version_info
        if python_version < (3, 8):
            raise RuntimeError(f"Python 3.8+ required, found {python_version.major}.{python_version.minor}")
        
        # Check OS
        if platform.system() != "Windows":
            logger.warning("System optimized for Windows 10, but should work on other platforms")
        
        # Check available memory
        try:
            import psutil
            memory_gb = psutil.virtual_memory().total / (1024**3)
            logger.info(f"Available memory: {memory_gb:.1f}GB")
            if memory_gb < 8:
                logger.warning("Less than 8GB RAM detected. Consider upgrading for optimal performance.")
        except ImportError:
            logger.info("psutil not available, skipping memory check")
        
        logger.info("✓ System requirements check completed")
    
    def check_gpu_availability(self):
        """Check GPU availability"""
        logger.info("Checking GPU availability...")
        
        try:
            import torch
            if torch.cuda.is_available():
                gpu_name = torch.cuda.get_device_name(0)
                gpu_memory = torch.cuda.get_device_properties(0).total_memory / (1024**3)
                logger.info(f"✓ CUDA GPU detected: {gpu_name}")
                logger.info(f"  GPU Memory: {gpu_memory:.1f}GB")
                return True
            else:
                logger.info("No CUDA GPU detected, using CPU")
                return False
        except ImportError:
            logger.info("PyTorch not installed, will install with CPU support")
            return False
    
    def create_directory_structure(self):
        """Create necessary directory structure"""
        logger.info("Creating directory structure...")
        
        directories = [
            "data/features",
            "data/backtest_improved",
            "data/backtest_improved/temp",
            "data/models/enhanced",
            "data/models/registry/enhanced",
            "logs",
            "reports",
            "reports/enhanced_training",
            "config",
            "examples",
            "tests",
            "benchmarks"
        ]
        
        for directory in directories:
            dir_path = self.project_root / directory
            dir_path.mkdir(parents=True, exist_ok=True)
            logger.info(f"  Created: {directory}")
        
        logger.info("✓ Directory structure created")
    
    def install_requirements(self):
        """Install Python requirements"""
        logger.info("Installing required packages...")
        
        # Core requirements
        requirements = [
            "numpy>=1.24.0",
            "pandas>=2.0.0", 
            "polars>=0.20.0",
            "scikit-learn>=1.3.0",
            "scipy>=1.10.0",
            
            # ML Libraries
            "lightgbm>=4.0.0",
            "xgboost>=2.0.0",
            "catboost>=1.2.0",
            
            # PyTorch (CPU version by default)
            "torch>=2.0.0",
            "pytorch-tabnet>=4.0.0",
            
            # Optimization
            "optuna>=3.0.0",
            
            # Explainability
            "shap>=0.42.0",
            
            # Utilities
            "joblib>=1.3.0",
            "pyyaml>=6.0",
            "tqdm>=4.65.0",
            
            # Visualization
            "matplotlib>=3.7.0",
            "seaborn>=0.12.0",
            "plotly>=5.15.0",
            
            # Testing
            "pytest>=7.4.0",
            "pytest-asyncio>=0.21.0",
            
            # System utilities
            "psutil>=5.9.0",
            "pathlib2>=2.3.7"
        ]
        
        # Install packages
        for requirement in requirements:
            try:
                subprocess.run([
                    self.python_executable, "-m", "pip", "install", requirement
                ], check=True, capture_output=True)
                logger.info(f"  ✓ {requirement.split('>=')[0]}")
            except subprocess.CalledProcessError as e:
                logger.error(f"  ✗ Failed to install {requirement}: {e}")
        
        logger.info("✓ Requirements installation completed")
    
    def verify_installation(self):
        """Verify that all packages are properly installed"""
        logger.info("Verifying installation...")
        
        test_imports = [
            "numpy", "pandas", "polars", "sklearn", "scipy",
            "lightgbm", "xgboost", "catboost", "torch", "pytorch_tabnet",
            "optuna", "shap", "joblib", "yaml"
        ]
        
        failed_imports = []
        
        for package in test_imports:
            try:
                if package == "sklearn":
                    import sklearn
                elif package == "yaml":
                    import yaml
                else:
                    __import__(package)
                logger.info(f"  ✓ {package}")
            except ImportError as e:
                logger.error(f"  ✗ {package}: {e}")
                failed_imports.append(package)
        
        if failed_imports:
            logger.error(f"Failed to import: {failed_imports}")
            return False
        
        logger.info("✓ All packages verified successfully")
        return True
    
    def create_sample_config(self):
        """Create sample configuration files"""
        logger.info("Creating sample configuration files...")
        
        # Create sample strategies config
        strategies_config = {
            'strategies': [
                {
                    'name': 'RSI_Mean_Reversion',
                    'long': 'rsi_14 < 30 and close > ema_20',
                    'short': 'rsi_14 > 70 and close < ema_20',
                    'market_conditions': ['ranging', 'low_volatility']
                },
                {
                    'name': 'MACD_Momentum', 
                    'long': 'macd > macd_signal and volume > sma_20_volume',
                    'short': 'macd < macd_signal and volume > sma_20_volume',
                    'market_conditions': ['trending_up', 'trending_down']
                },
                {
                    'name': 'Bollinger_Breakout',
                    'long': 'close > bb_upper and volume > sma_20_volume',
                    'short': 'close < bb_lower and volume > sma_20_volume', 
                    'market_conditions': ['high_volatility']
                }
            ]
        }
        
        config_file = self.project_root / "config" / "strategies_improved.yaml"
        with open(config_file, 'w') as f:
            import yaml
            yaml.dump(strategies_config, f, default_flow_style=False)
        
        logger.info(f"  Created: {config_file}")
        logger.info("✓ Sample configuration files created")
    
    def run_quick_test(self):
        """Run a quick test to verify everything works"""
        logger.info("Running quick system test...")
        
        try:
            # Test enhanced training agent
            test_script = self.project_root / "examples" / "quick_test_enhanced_training.py"
            if test_script.exists():
                result = subprocess.run([
                    self.python_executable, str(test_script)
                ], capture_output=True, text=True, timeout=300)
                
                if result.returncode == 0:
                    logger.info("✓ Quick test passed")
                    return True
                else:
                    logger.error(f"Quick test failed: {result.stderr}")
                    return False
            else:
                logger.warning("Quick test script not found, skipping")
                return True
                
        except Exception as e:
            logger.error(f"Error running quick test: {e}")
            return False
    
    def print_next_steps(self):
        """Print next steps for the user"""
        logger.info("\n" + "="*60)
        logger.info("🎉 SETUP COMPLETED SUCCESSFULLY!")
        logger.info("="*60)
        
        print("\n📋 Next Steps:")
        print("1. Review the configuration files in config/")
        print("2. Add your market data to data/features/")
        print("3. Run the complete workflow:")
        print("   python complete_backtesting_ml_workflow.py")
        print("\n📚 Documentation:")
        print("• README_Complete_System.md - Complete system overview")
        print("• README_Enhanced_Training.md - ML training details")
        print("• examples/ - Usage examples and tutorials")
        print("\n🧪 Testing:")
        print("• python examples/quick_test_enhanced_training.py")
        print("• python examples/enhanced_training_example.py")
        print("\n🔧 Configuration:")
        print("• config/optimized_training_config.yaml - ML training config")
        print("• config/strategies_improved.yaml - Trading strategies")
        
        logger.info("="*60)

def main():
    """Main setup function"""
    setup = CompleteSystemSetup()
    
    try:
        logger.info("🚀 Starting Complete System Setup")
        logger.info("="*50)
        
        # Run setup steps
        setup.check_system_requirements()
        setup.check_gpu_availability()
        setup.create_directory_structure()
        setup.install_requirements()
        
        if setup.verify_installation():
            setup.create_sample_config()
            setup.run_quick_test()
            setup.print_next_steps()
        else:
            logger.error("❌ Setup failed during package verification")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"Setup failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)