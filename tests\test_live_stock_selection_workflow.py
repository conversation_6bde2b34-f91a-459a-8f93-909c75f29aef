#!/usr/bin/env python3
"""
🧪 Comprehensive Test Suite for Live Stock Selection Workflow
Tests all agents, integration with existing ML models, performance, and end-to-end workflow validation

Test Categories:
1. Unit Tests for Individual Agents
2. Integration Tests with Existing ML Models
3. Performance Tests for Processing 500+ Stocks
4. End-to-End Workflow Validation
5. Error Handling and Fallback Scenarios
"""

import pytest
import asyncio
import logging
import polars as pl
import pandas as pd
import numpy as np
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import tempfile
import yaml
import json

# Import agents to test
from agents.live_data_management_agent import LiveDataManagementAgent, DataQualityMetrics
from agents.live_feature_engineering_agent import LiveFeatureEngineeringAgent, FeatureEngineering
from agents.live_ml_prediction_agent import LiveMLPredictionAgent, PredictionResult
from agents.live_stock_selection_agent import LiveStockSelectionAgent, StockScore
from agents.live_strategy_assignment_agent import LiveStrategyAssignmentAgent, StrategyAssignment
from agents.live_stock_selection_workflow import LiveStockSelectionWorkflow, WorkflowResult

logger = logging.getLogger(__name__)

class TestDataManagementAgent:
    """Test suite for Live Data Management Agent"""
    
    @pytest.fixture
    async def data_agent(self):
        """Create data management agent for testing"""
        agent = LiveDataManagementAgent()
        await agent.initialize()
        yield agent
        await agent.cleanup()
        
    @pytest.fixture
    def sample_stock_symbols(self):
        """Sample stock symbols for testing"""
        return ["RELIANCE", "TCS", "INFY", "HDFC", "ICICIBANK"]
        
    @pytest.mark.asyncio
    async def test_agent_initialization(self, data_agent):
        """Test agent initialization"""
        assert data_agent is not None
        assert hasattr(data_agent, 'config')
        assert hasattr(data_agent, 'data_config')
        
    @pytest.mark.asyncio
    async def test_download_historical_data(self, data_agent, sample_stock_symbols):
        """Test historical data download"""
        # Download data
        data = await data_agent.download_historical_data(sample_stock_symbols)
        
        # Verify results
        assert isinstance(data, dict)
        assert len(data) > 0
        
        # Check data structure
        for symbol, df in data.items():
            assert isinstance(df, pl.DataFrame)
            assert not df.is_empty()
            required_columns = ['timestamp', 'open', 'high', 'low', 'close', 'volume']
            assert all(col in df.columns for col in required_columns)
            
    @pytest.mark.asyncio
    async def test_data_quality_validation(self, data_agent, sample_stock_symbols):
        """Test data quality validation"""
        # Download data
        data = await data_agent.download_historical_data(sample_stock_symbols)
        
        # Get quality metrics
        quality_metrics = data_agent.get_quality_metrics()
        
        # Verify quality metrics
        assert isinstance(quality_metrics, dict)
        assert len(quality_metrics) > 0
        
        for symbol, metrics in quality_metrics.items():
            assert isinstance(metrics, DataQualityMetrics)
            assert metrics.symbol == symbol
            assert 0 <= metrics.quality_score <= 1
            assert metrics.missing_percentage >= 0
            
    @pytest.mark.asyncio
    async def test_rate_limiting(self, data_agent):
        """Test API rate limiting functionality"""
        # Test with multiple rapid requests
        symbols = ["RELIANCE", "TCS"]
        
        start_time = datetime.now()
        await data_agent.download_historical_data(symbols)
        end_time = datetime.now()
        
        # Should take some time due to rate limiting
        duration = (end_time - start_time).total_seconds()
        assert duration >= 1.0  # At least 1 second for rate limiting

class TestFeatureEngineeringAgent:
    """Test suite for Live Feature Engineering Agent"""
    
    @pytest.fixture
    async def feature_agent(self):
        """Create feature engineering agent for testing"""
        agent = LiveFeatureEngineeringAgent()
        return agent
        
    @pytest.fixture
    def sample_ohlcv_data(self):
        """Create sample OHLCV data for testing"""
        dates = pd.date_range(start='2024-01-01', periods=30, freq='D')
        np.random.seed(42)
        
        prices = 100 + np.cumsum(np.random.randn(30) * 0.02)
        volumes = np.random.randint(1000, 10000, 30)
        
        data = {
            'timestamp': dates,
            'open': prices * (1 + np.random.randn(30) * 0.001),
            'high': prices * (1 + np.abs(np.random.randn(30)) * 0.01),
            'low': prices * (1 - np.abs(np.random.randn(30)) * 0.01),
            'close': prices,
            'volume': volumes
        }
        
        return {"TEST_SYMBOL": pl.DataFrame(data)}
        
    @pytest.mark.asyncio
    async def test_feature_calculation(self, feature_agent, sample_ohlcv_data):
        """Test feature calculation for stocks"""
        # Calculate features
        features = await feature_agent.calculate_features_for_stocks(sample_ohlcv_data)
        
        # Verify results
        assert isinstance(features, dict)
        assert len(features) > 0
        
        for symbol, feature_eng in features.items():
            assert isinstance(feature_eng, FeatureEngineering)
            assert feature_eng.symbol == symbol
            assert feature_eng.feature_count > 0
            assert isinstance(feature_eng.features, dict)
            
    @pytest.mark.asyncio
    async def test_technical_indicators(self, feature_agent, sample_ohlcv_data):
        """Test specific technical indicators"""
        features = await feature_agent.calculate_features_for_stocks(sample_ohlcv_data)
        
        feature_eng = next(iter(features.values()))
        feature_dict = feature_eng.features
        
        # Check for expected indicators
        expected_indicators = [
            'volatility_std', 'rsi_14', 'macd', 'bb_upper', 'bb_lower',
            'ema_5', 'ema_20', 'stoch_k', 'volume_ratio', 'roc'
        ]
        
        found_indicators = [ind for ind in expected_indicators if ind in feature_dict]
        assert len(found_indicators) > 5  # Should have most indicators
        
    @pytest.mark.asyncio
    async def test_target_variable_calculation(self, feature_agent, sample_ohlcv_data):
        """Test target variable (forward return) calculation"""
        features = await feature_agent.calculate_features_for_stocks(sample_ohlcv_data)
        
        feature_eng = next(iter(features.values()))
        
        # Should have target return
        assert feature_eng.target_return is not None
        assert isinstance(feature_eng.target_return, (int, float))

class TestMLPredictionAgent:
    """Test suite for Live ML Prediction Agent"""
    
    @pytest.fixture
    async def ml_agent(self):
        """Create ML prediction agent for testing"""
        agent = LiveMLPredictionAgent()
        await agent.initialize()
        return agent
        
    @pytest.fixture
    def sample_features(self):
        """Create sample features for testing"""
        return {
            "RELIANCE": {
                "volatility_std": 0.02,
                "rsi_14": 65.0,
                "macd": 0.5,
                "bb_percent_b": 0.7,
                "ema_20": 2500.0,
                "volume_ratio": 1.2,
                "roc": 2.5
            },
            "TCS": {
                "volatility_std": 0.015,
                "rsi_14": 45.0,
                "macd": -0.2,
                "bb_percent_b": 0.3,
                "ema_20": 3800.0,
                "volume_ratio": 0.9,
                "roc": -1.0
            }
        }
        
    @pytest.mark.asyncio
    async def test_model_loading(self, ml_agent):
        """Test model loading functionality"""
        # Check if models are loaded (may not exist in test environment)
        model_info = ml_agent.get_model_info()
        
        # Should at least have the structure
        assert isinstance(model_info, dict)
        
    @pytest.mark.asyncio
    async def test_prediction_generation(self, ml_agent, sample_features):
        """Test prediction generation"""
        # Generate predictions
        predictions = await ml_agent.generate_predictions(sample_features)
        
        # Verify results
        assert isinstance(predictions, dict)
        
        for symbol, prediction in predictions.items():
            assert isinstance(prediction, PredictionResult)
            assert prediction.symbol == symbol
            
    @pytest.mark.asyncio
    async def test_fallback_scenarios(self, ml_agent, sample_features):
        """Test fallback scenarios when models fail"""
        # This should work even if models are not available
        predictions = await ml_agent.generate_predictions(sample_features)
        
        # Should still return results (possibly with fallback models)
        assert isinstance(predictions, dict)

class TestStockSelectionAgent:
    """Test suite for Live Stock Selection Agent"""
    
    @pytest.fixture
    def selection_agent(self):
        """Create stock selection agent for testing"""
        return LiveStockSelectionAgent()
        
    @pytest.fixture
    def sample_predictions(self):
        """Create sample predictions for testing"""
        predictions = {}
        symbols = ["RELIANCE", "TCS", "INFY", "HDFC", "ICICIBANK"]
        
        for symbol in symbols:
            predictions[symbol] = PredictionResult(
                symbol=symbol,
                expected_return=np.random.normal(0.05, 0.03),
                expected_return_confidence=np.random.uniform(0.6, 0.9),
                risk_metrics={
                    'volatility': np.random.uniform(0.01, 0.04),
                    'expected_drawdown': np.random.uniform(0.05, 0.15)
                },
                strategy_suitability={
                    'momentum': np.random.uniform(0.4, 0.9),
                    'mean_reversion': np.random.uniform(0.3, 0.8),
                    'breakout': np.random.uniform(0.3, 0.9)
                },
                confidence_intervals={},
                model_versions={},
                prediction_quality=np.random.uniform(0.7, 0.95),
                is_valid=True
            )
            
        return predictions
        
    @pytest.fixture
    def sample_quality_metrics(self):
        """Create sample quality metrics for testing"""
        metrics = {}
        symbols = ["RELIANCE", "TCS", "INFY", "HDFC", "ICICIBANK"]
        
        for symbol in symbols:
            metrics[symbol] = DataQualityMetrics(
                symbol=symbol,
                total_points=np.random.randint(500, 1000),
                missing_points=np.random.randint(0, 50),
                missing_percentage=np.random.uniform(0, 5),
                outliers_detected=np.random.randint(0, 10),
                data_completeness=np.random.uniform(0.8, 1.0),
                date_range_coverage=np.random.uniform(0.9, 1.0),
                is_valid=True,
                quality_score=np.random.uniform(0.7, 0.95)
            )
            
        return metrics
        
    @pytest.mark.asyncio
    async def test_stock_scoring(self, selection_agent, sample_predictions, sample_quality_metrics):
        """Test stock scoring functionality"""
        # Select stocks
        result = await selection_agent.select_stocks(sample_predictions, sample_quality_metrics)
        
        # Verify results
        assert result is not None
        assert isinstance(result.selected_stocks, list)
        assert isinstance(result.stock_scores, dict)
        assert len(result.stock_scores) > 0
        
        # Check score structure
        for symbol, score in result.stock_scores.items():
            assert isinstance(score, StockScore)
            assert 0 <= score.composite_score <= 1
            
    @pytest.mark.asyncio
    async def test_diversification(self, selection_agent, sample_predictions, sample_quality_metrics):
        """Test diversification constraints"""
        result = await selection_agent.select_stocks(sample_predictions, sample_quality_metrics)
        
        # Should have diversification metrics
        assert 'diversification_metrics' in result.__dict__
        
    @pytest.mark.asyncio
    async def test_risk_filtering(self, selection_agent, sample_predictions, sample_quality_metrics):
        """Test risk filtering functionality"""
        result = await selection_agent.select_stocks(sample_predictions, sample_quality_metrics)
        
        # Should have risk warnings
        assert isinstance(result.risk_warnings, list)

class TestStrategyAssignmentAgent:
    """Test suite for Live Strategy Assignment Agent"""

    @pytest.fixture
    def strategy_agent(self):
        """Create strategy assignment agent for testing"""
        return LiveStrategyAssignmentAgent()

    @pytest.fixture
    def sample_selected_stocks(self):
        """Sample selected stocks for testing"""
        return ["RELIANCE", "TCS", "INFY"]

    @pytest.fixture
    def sample_predictions_for_strategy(self):
        """Create sample predictions for strategy testing"""
        predictions = {}
        symbols = ["RELIANCE", "TCS", "INFY"]

        for symbol in symbols:
            predictions[symbol] = PredictionResult(
                symbol=symbol,
                expected_return=np.random.normal(0.05, 0.03),
                expected_return_confidence=np.random.uniform(0.6, 0.9),
                risk_metrics={
                    'volatility': np.random.uniform(0.01, 0.04),
                    'expected_drawdown': np.random.uniform(0.05, 0.15)
                },
                strategy_suitability={
                    'momentum': np.random.uniform(0.4, 0.9),
                    'mean_reversion': np.random.uniform(0.3, 0.8),
                    'breakout': np.random.uniform(0.3, 0.9),
                    'trend_following': np.random.uniform(0.4, 0.8)
                },
                confidence_intervals={},
                model_versions={},
                prediction_quality=np.random.uniform(0.7, 0.95),
                is_valid=True
            )

        return predictions

    @pytest.fixture
    def sample_features_for_strategy(self):
        """Create sample features for strategy testing"""
        features = {}
        symbols = ["RELIANCE", "TCS", "INFY"]

        for symbol in symbols:
            features[symbol] = FeatureEngineering(
                symbol=symbol,
                features={},
                target_return=np.random.normal(0.05, 0.03),
                market_conditions={
                    'volatility_regime': np.random.choice(['low_volatility', 'normal_volatility', 'high_volatility']),
                    'trend_strength': {'linear_regression_slope': np.random.normal(0, 0.01)}
                },
                feature_count=50,
                is_valid=True,
                quality_score=0.85
            )

        return features

    @pytest.mark.asyncio
    async def test_strategy_assignment(self, strategy_agent, sample_selected_stocks,
                                     sample_predictions_for_strategy, sample_features_for_strategy):
        """Test strategy assignment functionality"""
        # Assign strategies
        result = await strategy_agent.assign_strategies(
            sample_selected_stocks, sample_predictions_for_strategy, sample_features_for_strategy
        )

        # Verify results
        assert result is not None
        assert isinstance(result.assignments, dict)
        assert len(result.assignments) > 0

        # Check assignment structure
        for symbol, assignment in result.assignments.items():
            assert isinstance(assignment, StrategyAssignment)
            assert assignment.symbol == symbol
            assert assignment.primary_strategy in ['momentum', 'mean_reversion', 'breakout', 'trend_following']
            assert 0 <= assignment.primary_confidence <= 1

    @pytest.mark.asyncio
    async def test_strategy_distribution(self, strategy_agent, sample_selected_stocks,
                                       sample_predictions_for_strategy, sample_features_for_strategy):
        """Test strategy distribution analysis"""
        result = await strategy_agent.assign_strategies(
            sample_selected_stocks, sample_predictions_for_strategy, sample_features_for_strategy
        )

        # Should have strategy distribution
        assert isinstance(result.strategy_distribution, dict)
        assert sum(result.strategy_distribution.values()) == len(sample_selected_stocks)

    @pytest.mark.asyncio
    async def test_performance_expectations(self, strategy_agent, sample_selected_stocks,
                                          sample_predictions_for_strategy, sample_features_for_strategy):
        """Test performance expectation calculations"""
        result = await strategy_agent.assign_strategies(
            sample_selected_stocks, sample_predictions_for_strategy, sample_features_for_strategy
        )

        # Should have performance summary
        assert isinstance(result.performance_summary, dict)

        # Check for expected metrics
        for assignment in result.assignments.values():
            assert isinstance(assignment.expected_performance, dict)

class TestLiveStockSelectionWorkflow:
    """Test suite for the complete Live Stock Selection Workflow"""

    @pytest.fixture
    async def workflow(self):
        """Create workflow for testing"""
        workflow = LiveStockSelectionWorkflow()
        await workflow.initialize()
        yield workflow
        await workflow.cleanup()

    @pytest.fixture
    def sample_stock_list(self):
        """Sample stock list for testing"""
        return ["RELIANCE", "TCS", "INFY", "HDFC", "ICICIBANK"]

    @pytest.mark.asyncio
    async def test_workflow_initialization(self, workflow):
        """Test workflow initialization"""
        assert workflow is not None
        assert workflow.data_agent is not None
        assert workflow.feature_agent is not None
        assert workflow.ml_agent is not None
        assert workflow.selection_agent is not None
        assert workflow.strategy_agent is not None

    @pytest.mark.asyncio
    async def test_end_to_end_workflow(self, workflow, sample_stock_list):
        """Test complete end-to-end workflow"""
        # Execute workflow
        result = await workflow.execute_workflow(sample_stock_list)

        # Verify results
        assert isinstance(result, WorkflowResult)
        assert isinstance(result.selected_stocks, list)
        assert isinstance(result.strategy_assignments, dict)
        assert isinstance(result.execution_summary, dict)

        # Check execution summary
        assert 'status' in result.execution_summary
        assert 'total_execution_time' in result.execution_summary

    @pytest.mark.asyncio
    async def test_workflow_error_handling(self, workflow):
        """Test workflow error handling with invalid inputs"""
        # Test with empty stock list
        result = await workflow.execute_workflow([])

        # Should still return a result (possibly with warnings)
        assert isinstance(result, WorkflowResult)

    @pytest.mark.asyncio
    async def test_workflow_performance(self, workflow):
        """Test workflow performance with larger stock list"""
        # Create larger stock list
        large_stock_list = [f"STOCK_{i:03d}" for i in range(50)]

        start_time = datetime.now()
        result = await workflow.execute_workflow(large_stock_list)
        end_time = datetime.now()

        execution_time = (end_time - start_time).total_seconds()

        # Should complete within reasonable time (adjust as needed)
        assert execution_time < 300  # 5 minutes max
        assert isinstance(result, WorkflowResult)

class TestIntegrationWithExistingSystem:
    """Test integration with existing ML models and trading system"""

    @pytest.mark.asyncio
    async def test_config_compatibility(self):
        """Test configuration compatibility"""
        # Test loading configuration
        workflow = LiveStockSelectionWorkflow()

        assert workflow.config is not None
        assert 'data_management' in workflow.config
        assert 'feature_engineering' in workflow.config
        assert 'ml_prediction' in workflow.config
        assert 'stock_selection' in workflow.config
        assert 'strategy_assignment' in workflow.config

    @pytest.mark.asyncio
    async def test_data_format_compatibility(self):
        """Test data format compatibility with existing system"""
        # Create sample data in expected format
        dates = pd.date_range(start='2024-01-01', periods=30, freq='D')
        sample_data = pl.DataFrame({
            'timestamp': dates,
            'open': np.random.uniform(100, 110, 30),
            'high': np.random.uniform(110, 120, 30),
            'low': np.random.uniform(90, 100, 30),
            'close': np.random.uniform(100, 110, 30),
            'volume': np.random.randint(1000, 10000, 30)
        })

        # Test with feature engineering agent
        feature_agent = LiveFeatureEngineeringAgent()
        features = await feature_agent.calculate_features_for_stocks({"TEST": sample_data})

        assert len(features) > 0

    @pytest.mark.asyncio
    async def test_fallback_mechanisms(self):
        """Test fallback mechanisms when ML models are not available"""
        # Test workflow with potential model failures
        workflow = LiveStockSelectionWorkflow()

        # Should initialize even if some models are missing
        success = await workflow.initialize()

        # May succeed or fail depending on environment, but should not crash
        assert isinstance(success, bool)

class TestPerformanceAndScalability:
    """Test performance and scalability for processing 500+ stocks"""

    @pytest.mark.asyncio
    async def test_large_scale_processing(self):
        """Test processing large number of stocks"""
        # Create large stock list
        large_stock_list = [f"STOCK_{i:03d}" for i in range(100)]  # Start with 100 for testing

        workflow = LiveStockSelectionWorkflow()
        await workflow.initialize()

        try:
            start_time = datetime.now()
            result = await workflow.execute_workflow(large_stock_list)
            end_time = datetime.now()

            execution_time = (end_time - start_time).total_seconds()

            # Performance assertions
            assert execution_time < 600  # 10 minutes max for 100 stocks
            assert isinstance(result, WorkflowResult)

            # Memory usage should be reasonable
            # (In a real test, you might check memory usage here)

        finally:
            await workflow.cleanup()

    @pytest.mark.asyncio
    async def test_concurrent_processing(self):
        """Test concurrent processing capabilities"""
        # Test multiple concurrent workflows (if needed)
        workflows = []

        try:
            # Create multiple workflows
            for i in range(3):
                workflow = LiveStockSelectionWorkflow()
                await workflow.initialize()
                workflows.append(workflow)

            # Run them concurrently with small stock lists
            tasks = []
            for i, workflow in enumerate(workflows):
                stock_list = [f"STOCK_{j:03d}" for j in range(i*5, (i+1)*5)]
                tasks.append(workflow.execute_workflow(stock_list))

            results = await asyncio.gather(*tasks, return_exceptions=True)

            # All should complete successfully or with expected exceptions
            for result in results:
                if not isinstance(result, Exception):
                    assert isinstance(result, WorkflowResult)

        finally:
            # Cleanup all workflows
            for workflow in workflows:
                await workflow.cleanup()

# Utility functions for testing
def create_test_config() -> Dict[str, Any]:
    """Create test configuration"""
    return {
        'data_management': {
            'historical_days': 25,
            'data_directory': 'data/test',
            'api_rate_limit': {'requests_per_minute': 10}
        },
        'feature_engineering': {
            'technical_indicators': {
                'volatility': {'enabled': True, 'rolling_window': 20},
                'rsi': {'enabled': True, 'periods': [14]},
                'macd': {'enabled': True}
            }
        },
        'ml_prediction': {
            'model_loading': {'models_directory': 'data/test_models'}
        },
        'stock_selection': {
            'scoring_weights': {
                'predicted_returns': 40,
                'risk_adjusted_returns': 30,
                'strategy_fit_confidence': 20,
                'data_quality_score': 10
            }
        },
        'strategy_assignment': {
            'available_strategies': {
                'momentum': {'name': 'Momentum Strategy'},
                'mean_reversion': {'name': 'Mean Reversion Strategy'}
            }
        }
    }

if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v"])
