#!/usr/bin/env python3
"""
🧪 Test Runner for Live Stock Selection Workflow
Simple test runner that can be used without pytest for basic validation

Usage:
    python tests/run_tests.py
    python tests/run_tests.py --quick  # Run only quick tests
    python tests/run_tests.py --integration  # Run integration tests
"""

import asyncio
import logging
import sys
import argparse
from pathlib import Path
from datetime import datetime

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

# Import test classes
from tests.test_live_stock_selection_workflow import (
    TestDataManagementAgent,
    TestFeatureEngineeringAgent, 
    TestMLPredictionAgent,
    TestStockSelectionAgent,
    TestStrategyAssignmentAgent,
    TestLiveStockSelectionWorkflow,
    TestIntegrationWithExistingSystem,
    TestPerformanceAndScalability
)

logger = logging.getLogger(__name__)

class SimpleTestRunner:
    """Simple test runner for basic validation"""
    
    def __init__(self):
        self.passed_tests = 0
        self.failed_tests = 0
        self.test_results = []
        
    async def run_test(self, test_name: str, test_func, *args, **kwargs):
        """Run a single test and record results"""
        try:
            logger.info(f"🧪 Running {test_name}...")
            start_time = datetime.now()
            
            if asyncio.iscoroutinefunction(test_func):
                await test_func(*args, **kwargs)
            else:
                test_func(*args, **kwargs)
                
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            self.passed_tests += 1
            self.test_results.append({
                'name': test_name,
                'status': 'PASSED',
                'duration': duration,
                'error': None
            })
            logger.info(f"✅ {test_name} PASSED ({duration:.2f}s)")
            
        except Exception as e:
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            self.failed_tests += 1
            self.test_results.append({
                'name': test_name,
                'status': 'FAILED',
                'duration': duration,
                'error': str(e)
            })
            logger.error(f"❌ {test_name} FAILED ({duration:.2f}s): {e}")
            
    async def run_quick_tests(self):
        """Run quick validation tests"""
        logger.info("🚀 Running Quick Validation Tests...")
        
        # Test configuration loading
        await self.run_test("Config Loading", self._test_config_loading)
        
        # Test agent initialization
        await self.run_test("Agent Initialization", self._test_agent_initialization)
        
        # Test basic workflow
        await self.run_test("Basic Workflow", self._test_basic_workflow)
        
    async def run_integration_tests(self):
        """Run integration tests"""
        logger.info("🔗 Running Integration Tests...")
        
        # Test data management
        await self.run_test("Data Management Integration", self._test_data_management_integration)
        
        # Test feature engineering
        await self.run_test("Feature Engineering Integration", self._test_feature_engineering_integration)
        
        # Test end-to-end workflow
        await self.run_test("End-to-End Workflow", self._test_end_to_end_workflow)
        
    async def run_performance_tests(self):
        """Run performance tests"""
        logger.info("⚡ Running Performance Tests...")
        
        # Test with larger dataset
        await self.run_test("Large Dataset Processing", self._test_large_dataset_processing)
        
    async def _test_config_loading(self):
        """Test configuration loading"""
        from agents.live_stock_selection_workflow import LiveStockSelectionWorkflow
        
        workflow = LiveStockSelectionWorkflow()
        assert workflow.config is not None
        assert 'data_management' in workflow.config
        
    async def _test_agent_initialization(self):
        """Test agent initialization"""
        from agents.live_data_management_agent import LiveDataManagementAgent
        from agents.live_feature_engineering_agent import LiveFeatureEngineeringAgent
        
        # Test data agent
        data_agent = LiveDataManagementAgent()
        await data_agent.initialize()
        await data_agent.cleanup()
        
        # Test feature agent
        feature_agent = LiveFeatureEngineeringAgent()
        assert feature_agent is not None
        
    async def _test_basic_workflow(self):
        """Test basic workflow functionality"""
        from agents.live_stock_selection_workflow import LiveStockSelectionWorkflow
        
        workflow = LiveStockSelectionWorkflow()
        success = await workflow.initialize()
        
        # Should initialize (may fail if models not available, but shouldn't crash)
        assert isinstance(success, bool)
        
        await workflow.cleanup()
        
    async def _test_data_management_integration(self):
        """Test data management integration"""
        from agents.live_data_management_agent import LiveDataManagementAgent
        
        agent = LiveDataManagementAgent()
        await agent.initialize()
        
        # Test with small stock list
        test_symbols = ["RELIANCE", "TCS"]
        data = await agent.download_historical_data(test_symbols)
        
        assert isinstance(data, dict)
        
        await agent.cleanup()
        
    async def _test_feature_engineering_integration(self):
        """Test feature engineering integration"""
        import polars as pl
        import pandas as pd
        import numpy as np
        from agents.live_feature_engineering_agent import LiveFeatureEngineeringAgent
        
        # Create sample data
        dates = pd.date_range(start='2024-01-01', periods=30, freq='D')
        sample_data = {
            'TEST_SYMBOL': pl.DataFrame({
                'timestamp': dates,
                'open': np.random.uniform(100, 110, 30),
                'high': np.random.uniform(110, 120, 30),
                'low': np.random.uniform(90, 100, 30),
                'close': np.random.uniform(100, 110, 30),
                'volume': np.random.randint(1000, 10000, 30)
            })
        }
        
        agent = LiveFeatureEngineeringAgent()
        features = await agent.calculate_features_for_stocks(sample_data)
        
        assert isinstance(features, dict)
        assert len(features) > 0
        
    async def _test_end_to_end_workflow(self):
        """Test end-to-end workflow"""
        from agents.live_stock_selection_workflow import LiveStockSelectionWorkflow
        
        workflow = LiveStockSelectionWorkflow()
        await workflow.initialize()
        
        # Test with small stock list
        test_symbols = ["RELIANCE", "TCS", "INFY"]
        result = await workflow.execute_workflow(test_symbols)
        
        assert result is not None
        assert hasattr(result, 'selected_stocks')
        assert hasattr(result, 'execution_summary')
        
        await workflow.cleanup()
        
    async def _test_large_dataset_processing(self):
        """Test processing larger dataset"""
        from agents.live_stock_selection_workflow import LiveStockSelectionWorkflow
        
        workflow = LiveStockSelectionWorkflow()
        await workflow.initialize()
        
        # Test with larger stock list
        large_symbols = [f"STOCK_{i:03d}" for i in range(20)]  # 20 stocks for testing
        
        start_time = datetime.now()
        result = await workflow.execute_workflow(large_symbols)
        end_time = datetime.now()
        
        duration = (end_time - start_time).total_seconds()
        
        assert result is not None
        assert duration < 120  # Should complete within 2 minutes for 20 stocks
        
        await workflow.cleanup()
        
    def print_summary(self):
        """Print test summary"""
        total_tests = self.passed_tests + self.failed_tests
        success_rate = (self.passed_tests / total_tests * 100) if total_tests > 0 else 0
        
        print(f"\n{'='*60}")
        print(f"🧪 TEST SUMMARY")
        print(f"{'='*60}")
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {self.passed_tests} ✅")
        print(f"Failed: {self.failed_tests} ❌")
        print(f"Success Rate: {success_rate:.1f}%")
        print(f"{'='*60}")
        
        if self.failed_tests > 0:
            print(f"\n❌ FAILED TESTS:")
            for result in self.test_results:
                if result['status'] == 'FAILED':
                    print(f"  • {result['name']}: {result['error']}")
                    
        print(f"\n⏱️  TEST DURATIONS:")
        for result in self.test_results:
            status_icon = "✅" if result['status'] == 'PASSED' else "❌"
            print(f"  {status_icon} {result['name']}: {result['duration']:.2f}s")

async def main():
    """Main test runner"""
    parser = argparse.ArgumentParser(description='Live Stock Selection Workflow Test Runner')
    parser.add_argument('--quick', action='store_true', help='Run only quick tests')
    parser.add_argument('--integration', action='store_true', help='Run integration tests')
    parser.add_argument('--performance', action='store_true', help='Run performance tests')
    parser.add_argument('--all', action='store_true', help='Run all tests')
    parser.add_argument('--log-level', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'], 
                       default='INFO', help='Logging level')
    
    args = parser.parse_args()
    
    # Setup logging
    logging.basicConfig(
        level=getattr(logging, args.log_level),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    runner = SimpleTestRunner()
    
    try:
        if args.quick or args.all:
            await runner.run_quick_tests()
            
        if args.integration or args.all:
            await runner.run_integration_tests()
            
        if args.performance or args.all:
            await runner.run_performance_tests()
            
        if not any([args.quick, args.integration, args.performance, args.all]):
            # Default: run quick tests
            await runner.run_quick_tests()
            
    except KeyboardInterrupt:
        logger.info("Tests interrupted by user")
    except Exception as e:
        logger.error(f"Test runner failed: {e}")
    finally:
        runner.print_summary()
        
    # Exit with error code if tests failed
    sys.exit(1 if runner.failed_tests > 0 else 0)

if __name__ == "__main__":
    asyncio.run(main())
